module.exports = {
  extends: [
    require.resolve('@umijs/fabric/dist/eslint'),
    'prettier',
    'chervon',
    'chervon/react',
    'chervon/typescript',
  ],
  globals: {
    ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION: true,
    page: true,
    REACT_APP_ENV: true,
    API: 'readonly',
    APP: 'readonly',
    ROOT_PATH: true,
  },
  rules: {
    semi: ['error', 'always'],
    '@typescript-eslint/prefer-optional-chain': 'warn',
    'react/jsx-no-useless-fragment': 'off',
    'object-curly-spacing': ['error', 'always'],
    '@typescript-eslint/no-shadow': 'error',
    'max-params': ['error', 5],
  },
};
