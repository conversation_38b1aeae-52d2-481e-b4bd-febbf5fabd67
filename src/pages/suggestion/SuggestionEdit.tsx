import FormContainer from '@/components/Form/FormContainer';
import { len128, len500 } from '@/utils/validate';
import { Access } from '@@/plugin-access/access';
import { PlusOutlined } from '@ant-design/icons';
import { ProForm, ProFormInstance, ProFormText } from '@ant-design/pro-components';
import { Button, message } from 'antd';
import React, { useImperativeHandle, useMemo, useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { ProFormDependency, ProFormSelect, ProFormTextArea } from '@ant-design/pro-form';
import { LocaleID } from '@/components/Locale';
import { get, omit } from 'lodash-es';
import { EditorItem, Validator } from '@/components/Form/EditorItem';
import { createSuggestion, updateSuggestion } from '@/services/suggestion';
import { useRequest } from '@@/plugin-request/request';

const SuggestionEdit = React.forwardRef<APP.RefType<API.SuggestionItem>, APP.EditFormProps>(
  (props, ref) => {
    const { refresh } = props;
    const intl = useIntl();
    const formRef = useRef<ProFormInstance<API.SuggestionItem>>();
    const [visible, setVisible] = useState<boolean>(false);
    const [readonly, setReadonly] = useState<boolean>(false);
    const [initialValues, setInitialValues] = useState<API.SuggestionItem>({} as any);

    const editorRef = useRef<{ validate: Validator }>(null);
    const access = useAccess();

    const { run: handleSubmit, loading } = useRequest(
      async (values) => {
        console.log('values:', values);
        if (values.suggestionId) {
          await updateSuggestion({
            ...omit(values, ['title', 'content', 'msg']),
            title: get(values, ['title', 'message']),
            content: get(values, ['content', 'message']),
          });
        } else {
          await createSuggestion({
            ...omit(values, ['title', 'content']),
            title: get(values, ['title', 'message']),
            content: get(values, ['content', 'message']),
          });
        }
        message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
        setVisible(false);
        refresh?.();
      },
      { manual: true },
    );

    const onClose = () => {
      setVisible(false);
    };

    useImperativeHandle(ref, () => ({
      edit: (item: API.SuggestionItem, r) => {
        if (!access.canUpdateSuggestion()) {
          console.warn(intl.formatMessage({ id: 'webCommon_page_noAccess_toast_text' }));
          return;
        }
        setReadonly(!!r);
        setInitialValues(item);
        setVisible(true);
      },
    }));

    const onCreate = () => {
      setInitialValues({} as any);
      setVisible(true);
      setReadonly(false);
    };

    const header = useMemo(() => {
      if (readonly) return 'webOperation_suggestion_view_drawer_title';
      else if (initialValues.suggestionId) return 'webOperation_suggestion_update_drawer_title';
      return 'webOperation_suggestion_create_drawer_title';
    }, [readonly, initialValues]);

    return (
      <>
        <FormContainer
          title={<FormattedMessage id={header} />}
          width="50%"
          onCancel={onClose}
          onConfirm={() => {
            console.log('=====', formRef.current);
            formRef.current?.submit();
          }}
          open={visible}
          destroyOnClose={true}
          loading={loading}
          hiddenConfirm={readonly}
        >
          <ProForm
            initialValues={initialValues}
            onFinish={async (v) => {
              await handleSubmit(v);
            }}
            formRef={formRef}
            labelCol={{ flex: '150px' }}
            layout="horizontal"
            onReset={onClose}
            submitter={false}
            readonly={readonly}
          >
            {initialValues.suggestionId ? (
              <ProFormText
                name="suggestionId"
                readonly
                label={intl.formatMessage({ id: 'webOperation_suggestion_id_input_text' })}
              />
            ) : null}

            <ProFormText
              name={['title', 'message']}
              label={intl.formatMessage({
                id: 'webOperation_suggestion_title_input_text',
              })}
              rules={[
                {
                  required: true,
                  message: (
                    <FormattedMessage id="webOperation_suggestion_title_input_placeholder" />
                  ),
                },
                len128,
              ]}
              placeholder={intl.formatMessage({
                id: 'webOperation_suggestion_title_input_placeholder',
              })}
              extra={
                !readonly ? (
                  <ProFormDependency name={['title', 'langId']}>
                    {({ title }) => <LocaleID id={title?.langId} />}
                  </ProFormDependency>
                ) : null
              }
            />

            <ProFormText
              label={intl.formatMessage({
                id: 'webCommon_page_langId_common_text',
              })}
              name={['title', 'langId']}
              hidden={!readonly}
              readonly
            />

            <ProForm.Item
              name={['content', 'message']}
              label={intl.formatMessage({
                id: 'webOperation_suggestion_content_input_text',
              })}
              required
              validateTrigger={['onBlur']}
              rules={[
                {
                  validator: (rule, value, callback) => {
                    if (!editorRef.current) {
                      console.error('editor ref not found!');
                      return;
                    }
                    return editorRef.current.validate(rule, value, callback);
                  },
                  message: (
                    <FormattedMessage id="webOperation_suggestion_content_input_placeholder" />
                  ),
                },
              ]}
              extra={
                !readonly ? (
                  <ProFormDependency name={['content', 'langId']}>
                    {({ content }) => <LocaleID id={content?.langId} />}
                  </ProFormDependency>
                ) : null
              }
            >
              <EditorItem readonly={readonly} style={{ height: '300px' }} ref={editorRef} />
            </ProForm.Item>

            <ProFormText
              label={intl.formatMessage({
                id: 'webCommon_page_langId_common_text',
              })}
              name={['content', 'langId']}
              hidden={!readonly}
              readonly
            />

            <ProFormTextArea
              name={['extra']}
              label={intl.formatMessage({
                id: 'webOperation_suggestion_extra_input_text',
              })}
              placeholder={intl.formatMessage({
                id: 'webOperation_suggestion_extra_input_placeholder',
              })}
              rules={[
                len500,
                {
                  pattern: /^(http|https):\/\/\S*/,
                  message: intl.formatMessage({
                    id: 'webOperation_suggestion_extra_input_placeholder',
                  }),
                },
              ]}
            />

            <ProFormSelect
              name="msg"
              hidden={!readonly}
              readonly
              label={intl.formatMessage({
                id: 'webOperation_suggestion_name_input_text',
              })}
            />
          </ProForm>
        </FormContainer>
        <Access accessible={access.canCreateSuggestion()}>
          <Button type="primary" onClick={onCreate}>
            <PlusOutlined />
            <FormattedMessage id="webOperation_suggestion_create_button_text" />
          </Button>
        </Access>
      </>
    );
  },
);

export default SuggestionEdit;
