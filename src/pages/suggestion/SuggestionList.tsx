import ResizableTable from '@/components/Table';
import { compose, rangeExtra, timeParams } from '@/hooks/column';
import { useIntl } from '@@/plugin-locale/localeExports';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { message, Tooltip, Space as AntSpace } from 'antd';
import { useRef, useState } from 'react';
import { FormattedMessage } from 'umi';
import { get, omit } from 'lodash-es';
import { AccessButton, AccessLink } from '@/components/Button/AccessButton';
import { rbac } from '@/access';
import {
  deleteSuggestion,
  downloadSuggestionTemplate,
  exportSuggestion,
  listSuggestion,
} from '@/services/suggestion';
import SuggestionEdit from '@/pages/suggestion/SuggestionEdit';
import ErrorModal from '@/components/ErrorModal';
import Space from '@/components/Space';
import { DownloadOutlined } from '@ant-design/icons';
import { createKAC } from '@/components/KeepAlive';
import ImportTemplate from '@/components/Import/index';
const SuggestionList = () => {
  const actionRef = useRef<ActionType>();
  const editRef = useRef<APP.RefType<API.SuggestionItem>>(null);
  const intl = useIntl();
  const [params, setParams] = useState<Partial<API.SuggestionPageParams>>({} as any);
  const errorRef = useRef<APP.ViewRefType>(null);

  const columns: ProColumns<API.SuggestionItem>[] = [
    {
      title: <FormattedMessage id="webOperation_suggestion_id_tableColumn_text" />,
      dataIndex: 'suggestionId',
      fixed: 'left',
      order: 8,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_suggestion_title_tableColumn_text" />,
      dataIndex: ['title', 'message'],
      order: 7,
      width: 150,
    },
    {
      title: <FormattedMessage id="webCommon_page_langId_common_text" />,
      dataIndex: ['title', 'langId'],
      hideInSearch: true,
      width: 150,
    },
    // {
    //   title: <FormattedMessage id="webOperation_suggestion_content_tableColumn_text" />,
    //   dataIndex: ['content', 'message'],
    //   hideInSearch: true,
    //   width: 150,
    // },
    // {
    //   title: <FormattedMessage id="webCommon_page_langId_common_text" />,
    //   dataIndex: ['content', 'langId'],
    //   hideInSearch: true,
    //   width: 150,
    // },
    {
      title: <FormattedMessage id="webOperation_suggestion_extra_tableColumn_text" />,
      dataIndex: 'extra',
      hideInSearch: true,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_suggestion_name_tableColumn_text" />,
      dataIndex: 'msg',
      hideInSearch: true,
      width: 150,
      ellipsis: {
        showTitle: false,
      },
      render: (_, record) => (
        <Tooltip placement="topLeft" title={record.msg?.join('，')}>
          {record.msg?.join('，')}
        </Tooltip>
      ),
    },
    ...rangeExtra,
    {
      title: <FormattedMessage id="webOperation_suggestion_option_tableColumn_text" />,
      dataIndex: 'option',
      valueType: 'option',
      width: 200,
      fixed: 'right',
      render: (_: any, record: API.SuggestionItem) => (
        <Space>
          <AccessLink
            text="webCommon_page_detail_common_text"
            code={rbac.SUGGESTION.VIEW}
            onClick={() => {
              editRef.current?.edit(record, true);
            }}
          />
          <AccessLink
            text="webCommon_page_edit_common_text"
            code={rbac.SUGGESTION.UPDATE}
            onClick={() => {
              editRef.current?.edit(record, false);
            }}
          />
          <AccessLink
            text="webCommon_page_delete_tableButton_linkText"
            code={rbac.SUGGESTION.DELETE}
            modal={{
              content: (
                <div style={{ marginBottom: '10px' }}>
                  <div>
                    <FormattedMessage id="webOperation_suggestion_deleteLine1_modal_message" />
                  </div>
                  <div>
                    <FormattedMessage id="webOperation_suggestion_deleteLine2_modal_message" />
                  </div>
                </div>
              ),
            }}
            onClick={async () => {
              try {
                await deleteSuggestion(record.suggestionId!);
                message.success(
                  intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }),
                );
                actionRef.current?.reload();
              } catch (e: any) {
                try {
                  const res = JSON.parse(e.message);
                  if (res.responseCode === '1090242006') {
                    errorRef.current?.open(res.errorMessage);
                  } else {
                    message.error(res.errorMessage);
                  }
                } catch (err) {
                  message.error(
                    intl.formatMessage({ id: 'webCommon_page_internalError_toast_text' }),
                  );
                }
              }
            }}
          />
        </Space>
      ),
    },
  ];

  return (
    <>
      <ResizableTable<API.SuggestionItem, API.SuggestionPageParams>
        actionRef={actionRef}
        rowKey="suggestionId"
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        scroll={{ x: 150 * 10 + 50 }}
        headerTitle={
          <AntSpace>
            <ImportTemplate
              key="import"
              pageName="suggestion"
              buttonName="webCommon_page_import_button_text"
              getTemplate={downloadSuggestionTemplate}
              url="/operation-platform/suggestion/import"
              urlParam="file"
              refresh={() => {
                return actionRef.current?.reload();
              }}
            />
            <AccessButton
              text="webCommon_page_export_button_text"
              code={rbac.SUGGESTION.EXPORT}
              icon={<DownloadOutlined />}
              onClick={async () => {
                await exportSuggestion({
                  ...omit(params, ['current']),
                  pageNum: params.current,
                });
              }}
            />
            <SuggestionEdit ref={editRef} refresh={() => actionRef.current?.reload()} />
          </AntSpace>
        }
        request={listSuggestion}
        columns={columns}
        beforeSearchSubmit={(queries) => {
          const temp = compose(queries, timeParams, (args) => ({
            ...omit(args, 'title'),
            title: get(args, ['title', 'message']),
          }));
          setParams(omit(temp, '_timestamp'));
          return temp;
        }}
      />
      <ErrorModal title="webCommon_page_deleteForbid_modal_title" ref={errorRef} />
    </>
  );
};

export default createKAC(SuggestionList);
