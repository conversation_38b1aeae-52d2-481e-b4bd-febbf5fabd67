import React, { useRef, useState } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Divider, Modal, Space, message, Button } from 'antd';
import { useRequest } from '@@/plugin-request/request';
import { Access } from '@@/plugin-access/access';
import { ProFormInstance, ProFormSelect } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { map } from 'lodash-es';

import ResizableTable from '@/components/Table';
import Import from '@/components/Import/index';
import { createKAC } from '@/components/KeepAlive';
import { rbac } from '@/access';
import DealerEdit, { RefType } from './EuropeEdit';
import { deleteEuropeDealer, getEuropeList, getEuropeImportTemplate } from '@/services/dealer';
import { listColumns } from './columns/EuropeColumns';
import { useColumn } from '@/hooks/column';

const EuropeList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const editRef = useRef<RefType>(null);
  const { createColumns } = useColumn();
  const intl = useIntl();
  const access = useAccess();
  const [dealerIds, setDealerIds] = useState<string[] | undefined>(undefined);
  const [importParams, setImportParams] = useState<{ [key: string]: any } | undefined>(undefined);

  const { run: handleDelete, loading } = useRequest(
    async () => {
      if (!dealerIds) return;
      await deleteEuropeDealer(dealerIds);
      setDealerIds(undefined);
      setSelectedValues([]);
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      actionRef.current?.reload();
    },
    { manual: true },
  );

  const actionColumn: ProColumns = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 200,
    fixed: 'right',
    render: (_, record) => [
      <Space key="edit" split={<Divider type="vertical" />}>
        <Access accessible={access.checkAuth(rbac.DEALER.EU.VIEW)}>
          <a
            key="view"
            onClick={() => {
              editRef.current?.edit(record, 'view');
            }}
          >
            <FormattedMessage id="webCommon_page_view_common_text" />
          </a>
        </Access>
        <Access accessible={access.checkAuth(rbac.DEALER.EU.EDIT)}>
          <a
            key="edit"
            onClick={() => {
              editRef.current?.edit(record, 'edit');
            }}
          >
            <FormattedMessage id="webCommon_page_edit_common_text" />
          </a>
        </Access>
        <Access accessible={access.checkAuth(rbac.DEALER.EU.DELETE)}>
          <a
            key="delete"
            onClick={() => {
              setDealerIds([record.dealerId]);
            }}
          >
            <FormattedMessage id="webCommon_page_delete_tableButton_linkText" />
          </a>
        </Access>
      </Space>,
    ],
  };

  const initColumns = [...listColumns('list'), actionColumn];
  const allColumns = createColumns('companyDealer', initColumns);
  const [selectedValues, setSelectedValues] = useState<API.EuropeDealerItem[]>([]);
  const onSelectChange = (
    newSelectedRowKeys: React.Key[],
    selectedRows: API.EuropeDealerItem[],
  ) => {
    setSelectedValues(selectedRows);
  };

  const rowSelection = {
    selectedRowKeys: map(selectedValues, (item: API.EuropeDealerItem) => item.dealerId),
    preserveSelectedRowKeys: true,
    onChange: onSelectChange,
  };
  return (
    <>
      <ResizableTable<API.EuropeDealerItem, API.EuropeDealerPageParams>
        actionRef={actionRef}
        formRef={formRef}
        rowKey="dealerId"
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          defaultPageSize: 10,
          pageSizeOptions: ['10', '20', '50'],
          showTotal: (total) =>
            `${intl.formatMessage({
              id: 'webCommon_page_prefix_pagination_label',
            })}${total}${intl.formatMessage({
              id: 'webCommon_page_suffix_pagination_label',
            })}`,
        }}
        headerTitle={
          <Space>
            <Access accessible={access.checkAuth(rbac.DEALER.EU.BATCHDELETE)}>
              <Button
                type="primary"
                ghost
                onClick={() => {
                  if (selectedValues.length > 0) {
                    setDealerIds(
                      map(selectedValues, (item: API.EuropeDealerItem) => item.dealerId),
                    );
                  }
                }}
              >
                <FormattedMessage id="webCommon_page_batchDelete_tableButton_linkText" />
              </Button>
            </Access>
            <Access accessible={access.checkAuth(rbac.DEALER.EU.IMPORT)}>
              <Import
                key="import"
                pageName="dealer"
                getTemplate={getEuropeImportTemplate}
                url="/operation-platform/dealer/eu/import"
                urlParam="file"
                refresh={() => {
                  return actionRef.current?.reload();
                }}
                data={importParams}
                formItems={
                  <ProFormSelect
                    name="countryCode"
                    label={intl.formatMessage({
                      id: 'webOperation_companyDealer_country_tableColumn_text',
                    })}
                    rules={[
                      {
                        required: true,
                        message:
                          intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                          intl.formatMessage({
                            id: 'webOperation_companyDealer_country_tableColumn_text',
                          }),
                      },
                    ]}
                    transform={(value: any) => ({})}
                    fieldProps={{
                      onChange: (e) => {
                        setImportParams({ countryCode: e });
                      },
                    }}
                    valueEnum={{
                      Germany: intl.formatMessage({
                        id: 'webOperation_dictionary_germany_select_text',
                      }),
                      French: intl.formatMessage({
                        id: 'webOperation_dictionary_french_select_text',
                      }),
                      Italy: intl.formatMessage({
                        id: 'webOperation_dictionary_italy_select_text',
                      }),
                    }}
                  />
                }
              />
            </Access>
            <DealerEdit
              ref={editRef}
              key="create"
              refresh={() => {
                return actionRef.current?.reload();
              }}
            />
          </Space>
        }
        rowSelection={rowSelection}
        scroll={{ x: 1300 }}
        request={getEuropeList}
        columns={allColumns}
      />
      <Modal
        title={intl.formatMessage({ id: 'webCommon_page_confirmToDelete_modal_title' })}
        visible={!!dealerIds}
        onOk={handleDelete}
        onCancel={() => setDealerIds(undefined)}
        confirmLoading={loading}
      >
        <FormattedMessage id="webOperation_dealer_delete_modal_message" />
      </Modal>
    </>
  );
};
export default createKAC(EuropeList);
