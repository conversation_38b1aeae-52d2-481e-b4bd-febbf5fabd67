import type { typeColumns } from '@/hooks/column';
import { extra, dateFilter } from '@/hooks/column';
import { FormattedMessage, getIntl } from '@@/plugin-locale/localeExports';
import { digitChecked, urlChecked } from '@/utils/validate';

export const listColumns = (mode = 'new'): typeColumns[] => [
  ...dateFilter,
  {
    dataIndex: 'dealerId',
    width: 160,
    showInForm: mode === 'new' ? false : true,
    readonly: mode === 'edit' ? true : false,
  },
  {
    dataIndex: 'name',
    width: 150,
    showInForm: true,
    required: true,
  },
  {
    dataIndex: 'address',
    showInForm: true,
    required: true,
    width: 250,
  },
  {
    dataIndex: 'city',
    width: 150,
    showInForm: true,
    langCode: 'webOperation_companyDealer_city_tableColumn_text',
  },
  {
    dataIndex: 'state',
    width: 150,
    showInForm: true,
    langCode: 'webOperation_companyDealer_state_tableColumn_text',
  },
  {
    dataIndex: 'country',
    width: 150,
    showInForm: true,
  },
  {
    dataIndex: 'zipcode',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
  },
  {
    dataIndex: 'telephone',
    width: 160,
    showInForm: true,
  },
  {
    dataIndex: 'email',
    width: 150,
    showInForm: true,
  },
  {
    dataIndex: 'website',
    showInForm: true,
    hideInSearch: true,
    rule: urlChecked,
    width: 250,
  },
  {
    dataIndex: 'hours',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
    valueType: 'digit',
    fieldProps: { addonAfter: <FormattedMessage id="webOperation_dealer_hour_label_text" /> },
  },
  {
    dataIndex: 'lat',
    showInForm: true,
    required: true,
    rule: digitChecked,
    langCode: 'webOperation_companyDealer_lat_tableColumn_text',
    width: 150,
  },
  {
    dataIndex: 'lng',
    showInForm: true,
    required: true,
    rule: digitChecked,
    langCode: 'webOperation_companyDealer_lng_tableColumn_text',
    width: 150,
  },
  {
    dataIndex: 'category',
    showInForm: true,
    required: true,
    valueType: mode === 'list' ? 'select' : 'checkbox',
    fieldProps: { mode: 'multiple', showArrow: true },
    width: 150,
    valueEnum: {
      'Ace Hardware': getIntl().formatMessage({
        id: 'webOperation_dictionary_aceHardware_select_text',
      }),
      'Service Location': getIntl().formatMessage({
        id: 'webOperation_dictionary_serviceLocation_select_text',
      }),
      "Lowe's": getIntl().formatMessage({ id: 'webOperation_dictionary_lowes_select_text' }),
      Dealer: getIntl().formatMessage({ id: 'webOperation_dictionary_dealer_select_text' }),
    },
  },
  {
    dataIndex: 'zoomLevel',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
    valueType: 'digit',
    fieldProps: { precision: 0 },
  },
  {
    dataIndex: 'color',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
  },
  {
    dataIndex: 'fontClass',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
  },
  {
    dataIndex: 'isActive',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
  },
  {
    dataIndex: 'image',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
  },
  {
    dataIndex: 'storeLocatorId',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
  },
  ...extra,
];

export const viewColumns: typeColumns[] = [
  {
    dataIndex: 'dealerId',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'name',
    showInForm: true,
    readonly: true,
  },

  {
    dataIndex: 'city',
    showInForm: true,
    readonly: true,
    langCode: 'webOperation_companyDealer_city_tableColumn_text',
  },
  {
    dataIndex: 'state',
    showInForm: true,
    readonly: true,
    langCode: 'webOperation_companyDealer_state_tableColumn_text',
  },
  {
    dataIndex: 'country',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'address',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'zipcode',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'telephone',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'email',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'website',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'hours',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'lat',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'lng',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'category',
    showInForm: true,
    readonly: true,
    valueType: 'select',
    valueEnum: {
      'Ace Hardware': getIntl().formatMessage({
        id: 'webOperation_dictionary_aceHardware_select_text',
      }),
      'Service Location': getIntl().formatMessage({
        id: 'webOperation_dictionary_serviceLocation_select_text',
      }),
      "Lowe's": getIntl().formatMessage({ id: 'webOperation_dictionary_lowes_select_text' }),
      Dealer: getIntl().formatMessage({ id: 'webOperation_dictionary_dealer_select_text' }),
    },
  },
  {
    dataIndex: 'zoomLevel',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'color',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'fontClass',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'isActive',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'image',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'storeLocatorId',
    showInForm: true,
    readonly: true,
  },
];
