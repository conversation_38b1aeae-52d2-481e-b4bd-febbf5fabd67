import type { typeColumns } from '@/hooks/column';
import { extra, dateFilter } from '@/hooks/column';
import { len300 } from '@/utils/validate';
import { getIntl } from 'umi';

export const listColumns = (mode = 'new'): typeColumns[] => [
  ...dateFilter,
  {
    dataIndex: 'dealerId',
    width: 160,
    showInForm: mode === 'new' ? false : true,
    readonly: mode === 'edit' ? true : false,
    langCode: 'webOperation_dealer_dealerId_tableColumn_text',
  },
  {
    dataIndex: 'countryCode',
    width: 160,
    showInForm: true,
    required: true,
    langCode: 'webOperation_companyDealer_country_tableColumn_text',
    valueType: 'select',
    valueEnum: {
      Germany: getIntl().formatMessage({
        id: 'webOperation_dictionary_germany_select_text',
      }),
      French: getIntl().formatMessage({
        id: 'webOperation_dictionary_french_select_text',
      }),
      Italy: getIntl().formatMessage({
        id: 'webOperation_dictionary_italy_select_text',
      }),
    },
  },
  {
    dataIndex: 'title',
    width: 150,
    showInForm: true,
    required: true,
    // addonRender:
    //   mode === 'edit' ? (
    //     <ProFormDependency name={['nameLangId']}>
    //       {({ nameLangId }) => {
    //         return (
    //           <span>
    //             <FormattedMessage id="webCommon_page_langId_common_text" />
    //             {nameLangId}
    //           </span>
    //         );
    //       }}
    //     </ProFormDependency>
    //   ) : undefined,
  },
  {
    dataIndex: 'addressOne',
    width: 150,
    showInForm: true,
    hideInSearch: true,
    rule: len300,
  },
  {
    dataIndex: 'addressTwo',
    width: 150,
    showInForm: true,
    hideInSearch: true,
    rule: len300,
  },
  {
    dataIndex: 'town',
    width: 150,
    showInForm: true,
  },
  {
    dataIndex: 'city',
    width: 150,
    showInForm: true,
  },
  {
    dataIndex: 'region',
    width: 150,
    showInForm: true,
  },
  {
    dataIndex: 'postcode',
    width: 150,
    showInForm: true,
    required: true,
  },
  {
    dataIndex: 'phoneNumber',
    width: 160,
    showInForm: true,
  },
  {
    dataIndex: 'email',
    width: 150,
    showInForm: true,
  },
  {
    dataIndex: 'geolocation',
    width: 150,
    showInForm: true,
    hideInSearch: true,
  },
  {
    dataIndex: 'langCode',
    width: 150,
    showInForm: true,
    hideInSearch: true,
  },
  {
    dataIndex: 'premium',
    width: 150,
    showInForm: true,
    hideInSearch: true,
  },
  {
    dataIndex: 'proXRangeSeller',
    width: 180,
    showInForm: true,
    hideInSearch: true,
  },
  {
    dataIndex: 'rideOnSeller',
    width: 150,
    showInForm: true,
    hideInSearch: true,
  },
  {
    dataIndex: 'ad',
    width: 150,
    showInForm: true,
    hideInSearch: true,
  },
  ...extra,
];

export const viewColumns: typeColumns[] = [
  {
    dataIndex: 'dealerId',
    showInForm: true,
    readonly: true,
    langCode: 'webOperation_dealer_dealerId_tableColumn_text',
  },
  {
    dataIndex: 'countryCode',
    showInForm: true,
    readonly: true,
    langCode: 'webOperation_companyDealer_country_tableColumn_text',
    valueType: 'select',
    valueEnum: {
      Germany: getIntl().formatMessage({
        id: 'webOperation_dictionary_germany_select_text',
      }),
      French: getIntl().formatMessage({
        id: 'webOperation_dictionary_french_select_text',
      }),
      Italy: getIntl().formatMessage({
        id: 'webOperation_dictionary_italy_select_text',
      }),
    },
  },
  {
    dataIndex: 'title',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'addressOne',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'addressTwo',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'town',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'city',
    readonly: true,
    showInForm: true,
  },
  {
    dataIndex: 'region',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'postcode',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'phoneNumber',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'email',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'geolocation',
    readonly: true,
    showInForm: true,
  },
  {
    dataIndex: 'langCode',
    readonly: true,
    showInForm: true,
  },
  {
    dataIndex: 'premium',
    readonly: true,
    showInForm: true,
  },
  {
    dataIndex: 'proXRangeSeller',
    readonly: true,
    showInForm: true,
  },
  {
    dataIndex: 'rideOnSeller',
    readonly: true,
    showInForm: true,
  },
  {
    dataIndex: 'ad',
    readonly: true,
    showInForm: true,
  },
];
