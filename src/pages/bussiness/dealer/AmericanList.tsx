import React, { useRef, useState } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Button, Divider, Modal, Space, message } from 'antd';
import { useRequest } from '@@/plugin-request/request';
import { Access } from '@@/plugin-access/access';
import { ProFormInstance } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import ResizableTable from '@/components/Table';
import Import from '@/components/Import/index';
import { createKAC } from '@/components/KeepAlive';
import { rbac } from '@/access';
import DealerEdit, { RefType } from './AmericanEdit';
import {
  deleteAmericanDealer,
  getAmericanList,
  getAmericanImportTemplate,
} from '@/services/dealer';
import { listColumns } from './columns/AmericanColumns';
import { useColumn } from '@/hooks/column';
import { ImportOutlined } from '@ant-design/icons';

const AmericanList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const editRef = useRef<RefType>(null);
  const { createColumns } = useColumn();
  const intl = useIntl();
  const access = useAccess();
  const [dealerId, setDealerId] = useState<string | undefined>();
  const [confirmImport, setConfirmImport] = useState<boolean>(false);

  const { run: handleDelete, loading } = useRequest(
    async () => {
      if (!dealerId) return;
      await deleteAmericanDealer(dealerId);
      setDealerId(undefined);
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      actionRef.current?.reload();
    },
    { manual: true },
  );

  const actionColumn: ProColumns = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 200,
    fixed: 'right',
    render: (_, record) => [
      <Space key="edit" split={<Divider type="vertical" />}>
        <Access accessible={access.checkAuth(rbac.DEALER.NA.VIEW)}>
          <a
            key="view"
            onClick={() => {
              editRef.current?.edit(record, 'view');
            }}
          >
            <FormattedMessage id="webCommon_page_view_common_text" />
          </a>
        </Access>
        <Access accessible={access.checkAuth(rbac.DEALER.NA.EDIT)}>
          <a
            key="edit"
            onClick={() => {
              editRef.current?.edit(record, 'edit');
            }}
          >
            <FormattedMessage id="webCommon_page_edit_common_text" />
          </a>
        </Access>
        <Access accessible={access.checkAuth(rbac.DEALER.NA.DELETE)}>
          <a
            key="delete"
            onClick={() => {
              setDealerId(record.dealerId);
            }}
          >
            <FormattedMessage id="webCommon_page_delete_tableButton_linkText" />
          </a>
        </Access>
      </Space>,
    ],
  };

  const initColumns = [...listColumns('list'), actionColumn];
  const allColumns = createColumns('dealer', initColumns);
  return (
    <>
      <ResizableTable<API.DealerItem, API.DealerPageParams>
        actionRef={actionRef}
        formRef={formRef}
        rowKey="dealerId"
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        headerTitle={
          <Space>
            <Access accessible={access.checkAuth(rbac.DEALER.NA.IMPORT)}>
              <Button type="primary" ghost onClick={() => setConfirmImport(true)}>
                <ImportOutlined />
                <FormattedMessage id="webOperation_dealer_import_common_text" />
              </Button>
              <Modal
                title={intl.formatMessage({ id: 'webOperation_dealer_import_common_text' })}
                open={confirmImport}
                onCancel={() => {
                  setConfirmImport(false);
                }}
                confirmLoading={loading}
                footer={[
                  <Button key="cancel" onClick={() => setConfirmImport(false)}>
                    <FormattedMessage id="webCommon_page_cancel_button_text" />
                  </Button>,
                  <Import
                    key="import"
                    pageName="dealer"
                    hideIcon={true}
                    hideConfirmModal={() => setConfirmImport(false)}
                    getTemplate={getAmericanImportTemplate}
                    url="/operation-platform/dealer/na/import"
                    urlParam="file"
                    refresh={() => {
                      return actionRef.current?.reload();
                    }}
                  />,
                ]}
              >
                <FormattedMessage id="webOperation_dealer_comfirm_import_modal_message" />
              </Modal>
            </Access>
            <DealerEdit
              ref={editRef}
              key="create"
              refresh={() => {
                return actionRef.current?.reload();
              }}
            />
          </Space>
        }
        scroll={{ x: 1300 }}
        request={getAmericanList}
        columns={allColumns}
      />
      <Modal
        title={intl.formatMessage({ id: 'webCommon_page_confirmToDelete_modal_title' })}
        open={!!dealerId}
        onOk={handleDelete}
        onCancel={() => setDealerId(undefined)}
        confirmLoading={loading}
      >
        <FormattedMessage id="webOperation_dealer_delete_modal_message" />
      </Modal>
    </>
  );
};
export default createKAC(AmericanList);
