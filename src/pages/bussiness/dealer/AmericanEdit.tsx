import { useRequest } from '@@/plugin-request/request';
import { PlusOutlined } from '@ant-design/icons';
import { ProForm, ProFormInstance } from '@ant-design/pro-components';
import { Button, message } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Access } from '@@/plugin-access/access';

import FormContainer from '@/components/Form/FormContainer';
import type { typeColumns } from '@/hooks/column';
import { FormFields } from '@/components/Form/FormFields';
import { listColumns, viewColumns } from './columns/AmericanColumns';
import { createAmericanDealer, editAmericanDealer } from '@/services/dealer';
import { rbac } from '@/access';
type DealerItem = API.DealerItem;

export interface FormProps {
  refresh?: (resetPageIndex?: boolean | undefined) => Promise<void> | undefined;
}

export interface RefType {
  edit: (item: DealerItem, mode: string) => void;
}

const EditForm = React.forwardRef<RefType, FormProps>((props, ref) => {
  const { refresh } = props;
  const intl = useIntl();
  const access = useAccess();
  const [visible, setVisible] = useState<boolean>(false);
  const formRef = useRef<ProFormInstance<DealerItem>>();
  const [columns, setColumns] = useState<typeColumns[]>([]);
  const [initialValues, setInitialValues] = useState<DealerItem>({});
  const [method, setMethod] = useState<string>('new');

  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    edit: (item: DealerItem = {}, mode: string) => {
      if (mode === 'view') {
        setColumns(viewColumns);
      } else {
        item.hours = item.hours ? Number(item.hours) : undefined;
        item.zoomLevel = item.zoomLevel ? Number(item.zoomLevel) : undefined;
        setColumns(listColumns(mode));
      }
      setMethod(mode);
      setInitialValues(item);
      setVisible(true);
    },
  }));

  const { run: handleSubmit, loading } = useRequest(
    async (values) => {
      if (initialValues.dealerId) {
        await editAmericanDealer(values);
      } else {
        await createAmericanDealer(values);
      }
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setVisible(false);
      refresh?.();
    },
    { manual: true },
  );

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: `webOperation_dealer_${method}_drawer_title` })}
        width="50%"
        onCancel={onClose}
        onConfirm={() => formRef.current?.submit()}
        open={visible}
        destroyOnClose={true}
        loading={loading}
        hiddenConfirm={method === 'view' ? true : false}
      >
        <ProForm
          formRef={formRef}
          initialValues={initialValues}
          onFinish={handleSubmit}
          labelCol={{ span: 5 }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
          className={method === 'view' ? 'operation-ant-view-form' : ''}
        >
          <FormFields columns={columns} pageName="dealer" />
        </ProForm>
      </FormContainer>
      <Access accessible={access.checkAuth(rbac.DEALER.NA.CREATE)}>
        <Button
          type="primary"
          onClick={() => {
            setInitialValues({});
            setMethod('new');
            setColumns(listColumns('new'));
            setVisible(true);
          }}
        >
          <PlusOutlined />
          <FormattedMessage id="webOperation_dealer_new_drawer_title" />
        </Button>
      </Access>
    </>
  );
});

export default EditForm;
