import React, { useRef, useState } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Divider, Space, Spin, Button } from 'antd';
import { Access } from '@@/plugin-access/access';
import { ProForm, ProFormInstance, ProFormText } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import ResizableTable from '@/components/Table';
import { createKAC } from '@/components/KeepAlive';
import { rbac } from '@/access';
import Modal, { RefType as ModalRefType } from '@/components/Modal/index';
import { showCustomConfirmModal } from '@/hooks/action';
import {
  getList,
  getDetail,
  exportList,
  stopCompany,
  enableCompany,
  cancelCompany,
} from '@/services/company';
import CompanyDetail, { RefType } from './AccountDetail';
import CompanyAdminChanges, { RefType as AdminRefType } from './AdminChanges';
import { listColumns } from './columns/Account';
import { useColumn } from '@/hooks/column';
import { len128 } from '@/utils/validate';
import useExport from '@/hooks/useExport';

const CompanyList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const detailRef = useRef<RefType>(null);
  const historyRef = useRef<AdminRefType>(null);
  const formRef = useRef<ProFormInstance>();
  const modalFormRef = useRef<ProFormInstance>();

  const modalRef = useRef<ModalRefType>(null);
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const { createColumns } = useColumn();
  const access = useAccess();
  const intl = useIntl();
  const { run: handleExport, loading: exportLoading } = useExport(
    exportList,
    formRef,
    intl.formatMessage({ id: 'webOperation_sms_export_fileName_text' }),
  );

  const showDetail = async (id: string) => {
    setActionLoading(true);
    try {
      const res = await getDetail(id);
      setActionLoading(false);
      detailRef.current?.view(res);
    } catch (e) {
      setActionLoading(false);
    }
  };
  const actionColumn: ProColumns = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 380,
    fixed: 'right',
    render: (_, record) => [
      <Space key="detail" split={<Divider type="vertical" />}>
        <Access accessible={access.checkAuth(rbac.COMPANY.ACCOUNT.DETAIL)}>
          <a
            key="view"
            onClick={() => {
              showDetail(record.companyId);
            }}
          >
            <FormattedMessage id="webOperation_company_detail_button_text" />
          </a>
        </Access>
        {record.companyState !== '4' ? (
          <Access accessible={access.checkAuth(rbac.COMPANY.ACCOUNT.STATUS)}>
            <a
              onClick={() =>
                showCustomConfirmModal(
                  modalRef,
                  {
                    api: record.companyState === '1' ? stopCompany : enableCompany,
                    id: record.companyId,
                  },
                  record.companyState === '1' ? 'company_stop' : 'company_enable',
                  record.companyState === '1' ? (
                    <>
                      <div
                        style={{
                          textAlign: 'center',
                        }}
                      >
                        <FormattedMessage id="webOperation_company_confirmToStopLine1_message_text" />
                      </div>
                      <div
                        style={{
                          textAlign: 'center',
                        }}
                      >
                        <FormattedMessage id="webOperation_company_confirmToStopLine2_message_text" />
                      </div>
                      <div
                        style={{
                          color: '#D18A1A',
                          textAlign: 'center',
                          marginTop: '5px',
                        }}
                      >
                        <FormattedMessage id="webOperation_company_sensitive_message_text" />
                      </div>
                    </>
                  ) : (
                    <FormattedMessage id="webOperation_company_confirmToEnable_message_text" />
                  ),
                )
              }
            >
              <FormattedMessage
                id={
                  record.companyState === '1'
                    ? 'webCommon_page_disable_tableButton_linkText'
                    : 'webCommon_page_enable_tableButton_linkText'
                }
              />
            </a>
          </Access>
        ) : null}
        <Access accessible={access.checkAuth(rbac.COMPANY.ACCOUNT.ADMINCHANGES)}>
          <a onClick={() => historyRef.current?.show(record.companyId)}>
            <FormattedMessage id="webOperation_company_viewUpdate_button_text" />
          </a>
        </Access>
        {record.companyState !== '4' ? (
          <Access accessible={access.checkAuth(rbac.COMPANY.ACCOUNT.CANCEL)}>
            <a
              onClick={() =>
                showCustomConfirmModal(
                  modalRef,
                  {
                    api: cancelCompany,
                    id: record.companyId,
                  },
                  'company_cancel',
                  <>
                    <div
                      style={{
                        textAlign: 'center',
                      }}
                    >
                      <FormattedMessage id="webOperation_company_confirmToCancelLine1_message_text" />
                    </div>
                    <div
                      style={{
                        textAlign: 'center',
                      }}
                    >
                      <FormattedMessage id="webOperation_company_confirmToCancelLine2_message_text" />
                    </div>
                    <div
                      style={{
                        color: '#D18A1A',
                        textAlign: 'center',
                        marginTop: '5px',
                      }}
                    >
                      <FormattedMessage id="webOperation_company_sensitive_message_text" />
                      <div
                        style={{
                          marginBottom: '10px',
                        }}
                      >
                        <FormattedMessage id="webOperation_company_confirmToInputPassword_message_text" />
                      </div>
                    </div>
                    <ProForm layout="horizontal" submitter={false} formRef={modalFormRef}>
                      <ProFormText.Password
                        name="password"
                        rules={[
                          {
                            required: true,
                            message:
                              intl.formatMessage({
                                id: 'webCommon_page_input_common_placeholder',
                              }) +
                              intl.formatMessage({
                                id: 'webOperation_company_password_input_placeholder',
                              }),
                          },
                          len128,
                        ]}
                      />
                      <ProFormText
                        name="companyId"
                        key="id"
                        label={false}
                        initialValue={record.companyId}
                        hidden={true}
                      />
                    </ProForm>
                    ,
                  </>,
                )
              }
            >
              <FormattedMessage id="webOperation_company_cancel_button_text" />
            </a>
          </Access>
        ) : null}
      </Space>,
    ],
  };

  const initColumns = [...listColumns, actionColumn];
  const allColumns = createColumns('company', initColumns);

  return (
    <>
      <Spin spinning={actionLoading}>
        <ResizableTable<API.CompanyItem, API.CompanyPageParams>
          actionRef={actionRef}
          formRef={formRef}
          rowKey={(_, index: number | undefined) => String(index)}
          defaultSize="small"
          search={{
            labelWidth: 'auto',
          }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            defaultPageSize: 10,
            pageSizeOptions: ['10', '20', '50'],
            showTotal: (total) =>
              `${intl.formatMessage({
                id: 'webCommon_page_prefix_pagination_label',
              })}${total}${intl.formatMessage({
                id: 'webCommon_page_suffix_pagination_label',
              })}`,
          }}
          headerTitle={
            <Access accessible={access.checkAuth(rbac.COMPANY.ACCOUNT.EXPORT)}>
              <Button loading={exportLoading} type="primary" onClick={handleExport}>
                <FormattedMessage id="webCommon_page_export_button_text" />
              </Button>
            </Access>
          }
          scroll={{ x: 1300 }}
          request={getList}
          columns={allColumns}
        />
      </Spin>
      <CompanyDetail ref={detailRef} key="detail" />
      <CompanyAdminChanges ref={historyRef} key="history" />
      <Modal ref={modalRef} parentRef={actionRef} formRef={modalFormRef} />
    </>
  );
};
export default createKAC(CompanyList);
