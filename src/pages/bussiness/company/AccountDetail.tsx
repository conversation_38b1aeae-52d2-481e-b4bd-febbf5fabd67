import { useIntl } from 'umi';
import React, { useImperativeHandle, useState } from 'react';
import { ProForm } from '@ant-design/pro-components';
import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';
import { detailColumns } from './columns/Account';
import dayjs from 'dayjs';
type CompanyItem = Partial<API.CompanyItem>;

export interface RefType {
  view: (item: CompanyItem) => void;
}

const CompanyDetail = React.forwardRef<RefType>((props, ref) => {
  const intl = useIntl();
  const [visible, setVisible] = useState<boolean>(false);
  const [record, setRecord] = useState<CompanyItem>({});
  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    view: (item: CompanyItem) => {
      item.registerTime = item.registerTime
        ? dayjs(Number(item.registerTime)).format('YYYY-MM-DD HH:mm:ss')
        : undefined;
      setRecord(item);
      setVisible(true);
    },
  }));

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: 'webOperation_company_detail_drawer_text' })}
        width="45%"
        onCancel={onClose}
        hiddenConfirm={true}
        open={visible}
        destroyOnClose={true}
      >
        <ProForm
          initialValues={record}
          labelCol={{ span: 7 }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
        >
          <FormFields columns={detailColumns} pageName="company" />
        </ProForm>
      </FormContainer>
    </>
  );
});

export default CompanyDetail;
