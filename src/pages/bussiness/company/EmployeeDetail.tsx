import { useIntl } from 'umi';
import React, { useImperativeHandle, useState } from 'react';
import { ProForm } from '@ant-design/pro-components';
import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';
import { detailColumns } from './columns/Employee';
import dayjs from 'dayjs';

type EmployeeItem = Partial<API.CompanyEmployeeItem>;

export interface RefType {
  view: (item: EmployeeItem) => void;
}

const CompanyEmployeeDetail = React.forwardRef<RefType>((props, ref) => {
  const intl = useIntl();
  const [visible, setVisible] = useState<boolean>(false);
  const [record, setRecord] = useState<EmployeeItem>({});

  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    view: (item: EmployeeItem) => {
      item.registerTime = item.registerTime
        ? dayjs(Number(item.registerTime)).format('YYYY-MM-DD HH:mm:ss')
        : undefined;
      item.activeTime = item.activeTime
        ? dayjs(Number(item.activeTime)).format('YYYY-MM-DD HH:mm:ss')
        : undefined;
      setRecord(item);
      setVisible(true);
    },
  }));

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: 'webOperation_companyEmployee_detail_common_text' })}
        width="45%"
        onCancel={onClose}
        hiddenConfirm={true}
        open={visible}
        destroyOnClose={true}
      >
        <ProForm
          initialValues={record}
          labelCol={{ span: 7 }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
        >
          <FormFields columns={detailColumns} pageName="companyEmployee" />
        </ProForm>
      </FormContainer>
    </>
  );
});

export default CompanyEmployeeDetail;
