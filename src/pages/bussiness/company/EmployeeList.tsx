import React, { useRef, useState } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Divider, Space, Spin, Button } from 'antd';
import { Access } from '@@/plugin-access/access';
import { ProFormInstance } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';

import ResizableTable from '@/components/Table';
import { createKAC } from '@/components/KeepAlive';
import Modal, { RefType as ModalRefType } from '@/components/Modal/index';
import { showCustomConfirmModal } from '@/hooks/action';
import {
  getEmployees,
  exportEmployee,
  getEmployeeDetail,
  stopEmployee,
  enableEmployee,
} from '@/services/company';
import CompanyEmployeeDetail, { RefType } from './EmployeeDetail';
import { listColumns } from './columns/Employee';
import { useColumn } from '@/hooks/column';
import useExport from '@/hooks/useExport';
import { rbac } from '@/access';
const CompanyEmployee: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const detailRef = useRef<RefType>(null);
  const formRef = useRef<ProFormInstance>();
  const modalFormRef = useRef<ProFormInstance>();

  const modalRef = useRef<ModalRefType>(null);
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const { createColumns } = useColumn();
  const access = useAccess();
  const intl = useIntl();
  const { run: handleExport, loading: exportLoading } = useExport(
    exportEmployee,
    formRef,
    intl.formatMessage({ id: 'webOperation_sms_export_fileName_text' }),
  );

  const showDetail = async (id: string) => {
    setActionLoading(true);
    try {
      const res = await getEmployeeDetail(id);
      setActionLoading(false);
      detailRef.current?.view(res);
    } catch (e) {
      setActionLoading(false);
    }
  };
  const actionColumn: ProColumns = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 180,
    fixed: 'right',
    render: (_, record) => [
      <Space key="detail" split={<Divider type="vertical" />}>
        <Access accessible={access.checkAuth(rbac.COMPANY.EMPLOYEE.DETAIL)}>
          <a
            key="view"
            onClick={() => {
              showDetail(record.userId);
            }}
          >
            <FormattedMessage id="webOperation_companyEmployee_detail_common_text" />
          </a>
        </Access>
        {record.userState !== '4' ? (
          <Access accessible={access.checkAuth(rbac.COMPANY.EMPLOYEE.STATUS)}>
            <a
              onClick={() =>
                showCustomConfirmModal(
                  modalRef,
                  {
                    api: record.isEnabled === '0' ? enableEmployee : stopEmployee,
                    id: record.userId,
                  },
                  record.isEnabled === '0' ? 'companyEmployee_enable' : 'companyEmployee_stop',
                  record.isEnabled === '1' ? (
                    <>
                      <div
                        style={{
                          textAlign: 'center',
                        }}
                      >
                        <FormattedMessage id="webOperation_companyEmployee_confirmToStopLine1_message_text" />
                      </div>
                      <div
                        style={{
                          textAlign: 'center',
                        }}
                      >
                        <FormattedMessage id="webOperation_companyEmployee_confirmToStopLine2_message_text" />
                      </div>
                      <div
                        style={{
                          color: '#D18A1A',
                          textAlign: 'center',
                          marginTop: '5px',
                        }}
                      >
                        <FormattedMessage id="webOperation_companyEmployee_sensitive_message_text" />
                      </div>
                    </>
                  ) : (
                    <FormattedMessage id="webOperation_companyEmployee_confirmToEnable_message_text" />
                  ),
                )
              }
            >
              <FormattedMessage
                id={
                  record.isEnabled === '0'
                    ? 'webCommon_page_enable_tableButton_linkText'
                    : 'webCommon_page_disable_tableButton_linkText'
                }
              />
            </a>
          </Access>
        ) : null}
      </Space>,
    ],
  };

  const initColumns = [...listColumns, actionColumn];
  const allColumns = createColumns('companyEmployee', initColumns);

  return (
    <>
      <Spin spinning={actionLoading}>
        <ResizableTable<API.CompanyEmployeeItem, API.CompanyEmployeePageParams>
          actionRef={actionRef}
          formRef={formRef}
          rowKey={(_, index: number | undefined) => String(index)}
          defaultSize="small"
          search={{
            labelWidth: 'auto',
          }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            defaultPageSize: 10,
            pageSizeOptions: ['10', '20', '50'],
            showTotal: (total) =>
              `${intl.formatMessage({
                id: 'webCommon_page_prefix_pagination_label',
              })}${total}${intl.formatMessage({
                id: 'webCommon_page_suffix_pagination_label',
              })}`,
          }}
          headerTitle={
            <Access accessible={access.checkAuth(rbac.COMPANY.EMPLOYEE.EXPORT)}>
              <Button loading={exportLoading} type="primary" onClick={handleExport}>
                <FormattedMessage id="webCommon_page_export_button_text" />
              </Button>
            </Access>
          }
          scroll={{ x: 1300 }}
          request={getEmployees}
          columns={allColumns}
        />
      </Spin>
      <CompanyEmployeeDetail ref={detailRef} key="detail" />
      <Modal ref={modalRef} parentRef={actionRef} formRef={modalFormRef} />
    </>
  );
};
export default createKAC(CompanyEmployee);
