import React, { useState, useImperativeHandle, useRef } from 'react';
import { useIntl } from 'umi';
import { ProFormInstance } from '@ant-design/pro-components';
import FormContainer from '@/components/Form/FormContainer';
import ResizableTable from '@/components/Table';
import { useColumn } from '@/hooks/column';
import { getAdminChanges } from '@/services/company';
import { adminColumns } from './columns/Account';

export interface RefType {
  show: (id: string) => void;
}

const CompanyAdminChanges = React.forwardRef<RefType, APP.EditFormProps>((props, ref) => {
  const { createColumns } = useColumn();
  const [visible, setVisible] = useState<boolean>(false);
  const [companyId, setCompanyId] = useState<string>();
  const formRef = useRef<ProFormInstance>();
  const intl = useIntl();

  const onClose = () => {
    setVisible(false);
  };
  useImperativeHandle(ref, () => ({
    show: (id: string) => {
      setCompanyId(id);
      setVisible(true);
    },
  }));

  const allColumns = createColumns('company', adminColumns);

  return (
    <FormContainer
      title={intl.formatMessage({ id: 'webOperation_company_adminHistory_drawer_text' })}
      width="85%"
      onCancel={onClose}
      hiddenConfirm={true}
      visible={visible}
      destroyOnClose={true}
    >
      <ResizableTable<API.adminChangeRecord, API.adminChangeSearchParams>
        formRef={formRef}
        rowKey={(_, index: number | undefined) => String(index)}
        search={false}
        defaultSize="small"
        scroll={{ x: 1300 }}
        params={{
          companyId,
        }}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          defaultPageSize: 10,
          pageSizeOptions: ['10', '20', '50'],
          showTotal: (total) =>
            `${intl.formatMessage({
              id: 'webCommon_page_prefix_pagination_label',
            })}${total}${intl.formatMessage({
              id: 'webCommon_page_suffix_pagination_label',
            })}`,
        }}
        request={getAdminChanges}
        columns={allColumns}
      />
    </FormContainer>
  );
});

export default CompanyAdminChanges;
