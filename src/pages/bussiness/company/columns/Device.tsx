import type { typeColumns } from '@/hooks/column';
import { map } from 'lodash-es';

interface Parmas {
  categoryValueEnum: Record<string, string>;
  brandValueEnum: Record<string, string>;
}

export const listColumns = ({ categoryValueEnum, brandValueEnum }: Parmas): typeColumns[] => [
  {
    dataIndex: 'companyId',
    width: 180,
  },
  {
    dataIndex: 'companyName',
    width: 130,
  },
  {
    dataIndex: 'count',
    width: 150,
    hideInSearch: true,
  },
  {
    dataIndex: 'deviceId',
    width: 180,
    ellipsis: {
      showTitle: false,
    },
    render: (_, record) => map(record.deviceId, (item) => <div>{item ? item : '-'}</div>),
  },
  {
    dataIndex: 'deviceSn',
    width: 180,
    ellipsis: {
      showTitle: false,
    },
    render: (_, record) => map(record.deviceSn, (item) => <div>{item ? item : '-'}</div>),
  },
  {
    dataIndex: 'categoryCode',
    width: 150,
    valueEnum: categoryValueEnum,
    ellipsis: {
      showTitle: false,
    },
    render: (_, record) => map(record.categoryCode, (item) => <div>{item ? item : '-'}</div>),
  },
  {
    dataIndex: 'brandId',
    width: 150,
    isRender: true,
    valueEnum: brandValueEnum,
  },
  {
    dataIndex: 'model',
    width: 200,
    ellipsis: {
      showTitle: false,
    },
    render: (_, record) => map(record.model, (item) => <div>{item ? item : '-'}</div>),
  },
];
