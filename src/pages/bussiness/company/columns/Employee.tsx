import type { typeColumns } from '@/hooks/column';
import { FormattedMessage } from '@@/plugin-locale/localeExports';
import { fieldProps } from '@/hooks/column';
import dayjs from 'dayjs';
import { getIntl } from 'umi';

export const listColumns: typeColumns[] = [
  {
    title: <FormattedMessage id="webOperation_user_registerTime_tableColumn_text" />,
    dataIndex: 'registerTimeFilter',
    valueType: 'dateRange',
    hideInTable: true,
    showLabel: true,
    fieldProps,
    langCode: 'webOperation_user_registerTime_tableColumn_text',
  },
  {
    title: <FormattedMessage id="webOperation_device_activationTime_tableColumn_text" />,
    dataIndex: 'activeTimeFilter',
    valueType: 'dateRange',
    hideInTable: true,
    showLabel: true,
    fieldProps,
    langCode: 'webOperation_device_activationTime_tableColumn_text',
  },
  {
    dataIndex: 'userId',
    width: 170,
  },
  {
    dataIndex: 'userEmail',
    width: 130,
  },
  {
    dataIndex: 'userFirstName',
    width: 120,
    hideInSearch: true,
  },
  {
    dataIndex: 'userLastName',
    width: 150,
    hideInSearch: true,
  },
  {
    dataIndex: 'userName',
    hideInTable: true,
  },
  {
    dataIndex: 'userRole',
    width: 150,
    hideInSearch: true,
    valueEnum: {
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_admin_select_text',
      }),
      2: getIntl().formatMessage({
        id: 'webOperation_dictionary_manager_select_text',
      }),
      3: getIntl().formatMessage({
        id: 'webOperation_dictionary_employee_select_text',
      }),
    },
  },
  {
    dataIndex: 'userRoles',
    fieldProps: { mode: 'multiple', showArrow: true },
    langCode: 'webOperation_companyEmployee_userRole_tableColumn_text',
    hideInTable: true,
    valueEnum: {
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_admin_select_text',
      }),
      2: getIntl().formatMessage({
        id: 'webOperation_dictionary_manager_select_text',
      }),
      3: getIntl().formatMessage({
        id: 'webOperation_dictionary_employee_select_text',
      }),
    },
  },
  {
    dataIndex: 'companyId',
    width: 170,
  },
  {
    dataIndex: 'companyName',
    width: 150,
  },
  {
    dataIndex: 'userSourceType',
    width: 200,
    hideInSearch: true,
    valueEnum: {
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_selfRegistration_select_text',
      }),
      2: getIntl().formatMessage({
        id: 'webOperation_dictionary_fleetServiceWebInvitation_select_text',
      }),
      3: getIntl().formatMessage({
        id: 'webOperation_dictionary_CRMSynchronization_select_text',
      }),
    },
  },
  {
    dataIndex: 'userSourceTypes',
    fieldProps: { mode: 'multiple', showArrow: true },
    hideInTable: true,
    langCode: 'webOperation_companyEmployee_userSourceType_tableColumn_text',
    valueEnum: {
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_selfRegistration_select_text',
      }),
      2: getIntl().formatMessage({
        id: 'webOperation_dictionary_fleetServiceWebInvitation_select_text',
      }),
      3: getIntl().formatMessage({
        id: 'webOperation_dictionary_CRMSynchronization_select_text',
      }),
    },
  },
  {
    dataIndex: 'invitationEmail',
    width: 150,
  },
  {
    dataIndex: 'userState',
    width: 150,
    hideInSearch: true,
    valueEnum: {
      0: getIntl().formatMessage({
        id: 'webOperation_dictionary_inactive_select_text',
      }),
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_activated_select_text',
      }),
      4: getIntl().formatMessage({
        id: 'webOperation_dictionary_cancelled_select_text',
      }),
    },
  },
  {
    dataIndex: 'userStates',
    hideInTable: true,
    fieldProps: { mode: 'multiple', showArrow: true },
    langCode: 'webOperation_companyEmployee_userState_tableColumn_text',
    valueEnum: {
      0: getIntl().formatMessage({
        id: 'webOperation_dictionary_inactive_select_text',
      }),
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_activated_select_text',
      }),
      4: getIntl().formatMessage({
        id: 'webOperation_dictionary_cancelled_select_text',
      }),
    },
  },
  {
    dataIndex: 'isEnabled',
    width: 150,
    valueEnum: {
      0: getIntl().formatMessage({
        id: 'webOperation_dictionary_deactivate_select_text',
      }),
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_enabled_select_text',
      }),
    },
  },
  {
    dataIndex: 'activeTime',
    width: 150,
    valueType: 'dateTime',
    hideInSearch: true,
    render: (_, record) =>
      record.activeTime ? dayjs(Number(record.activeTime)).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
  {
    dataIndex: 'registerTime',
    width: 150,
    valueType: 'dateTime',
    hideInSearch: true,
    render: (_, record) =>
      record.registerTime ? dayjs(Number(record.registerTime)).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
];

export const detailColumns: typeColumns[] = [
  {
    dataIndex: 'userId',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'userEmail',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'userFirstName',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'userLastName',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'userRole',
    showInForm: true,
    readonly: true,
    valueType: 'select',
    valueEnum: {
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_admin_select_text',
      }),
      2: getIntl().formatMessage({
        id: 'webOperation_dictionary_manager_select_text',
      }),
      3: getIntl().formatMessage({
        id: 'webOperation_dictionary_employee_select_text',
      }),
    },
  },
  {
    dataIndex: 'companyId',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'companyName',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'companyCountry',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'companyAddress',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'companyPost',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'userSourceType',
    showInForm: true,
    readonly: true,
    valueType: 'select',
    valueEnum: {
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_selfRegistration_select_text',
      }),
      2: getIntl().formatMessage({
        id: 'webOperation_dictionary_fleetServiceWebInvitation_select_text',
      }),
      3: getIntl().formatMessage({
        id: 'webOperation_dictionary_CRMSynchronization_select_text',
      }),
    },
  },
  {
    dataIndex: 'invitationEmail',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'userState',
    showInForm: true,
    readonly: true,
    valueType: 'select',
    valueEnum: {
      0: getIntl().formatMessage({
        id: 'webOperation_dictionary_inactive_select_text',
      }),
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_activated_select_text',
      }),
      4: getIntl().formatMessage({
        id: 'webOperation_dictionary_cancelled_select_text',
      }),
    },
  },
  {
    dataIndex: 'isEnabled',
    showInForm: true,
    readonly: true,
    valueType: 'select',
    valueEnum: {
      0: getIntl().formatMessage({
        id: 'webOperation_dictionary_deactivate_select_text',
      }),
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_enabled_select_text',
      }),
    },
  },
  {
    dataIndex: 'activeTime',
    showInForm: true,
    readonly: true,
    valueType: 'dateTime',
    hideInSearch: true,
    render: (_, record) =>
      record.activeTime ? dayjs(Number(record.activeTime)).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
  {
    dataIndex: 'registerTime',
    showInForm: true,
    readonly: true,
    valueType: 'dateTime',
    hideInSearch: true,
    render: (_, record) =>
      record.registerTime ? dayjs(Number(record.registerTime)).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
];
