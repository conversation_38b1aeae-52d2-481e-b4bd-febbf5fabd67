import type { typeColumns } from '@/hooks/column';
import { fieldProps } from '@/hooks/column';
import { FormattedMessage } from '@@/plugin-locale/localeExports';
import { getIntl } from 'umi';
import dayjs from 'dayjs';
export const listColumns: typeColumns[] = [
  {
    title: <FormattedMessage id="webOperation_company_registerTime_tableColumn_text" />,
    dataIndex: 'registerTimeFilter',
    valueType: 'dateRange',
    hideInTable: true,
    showLabel: true,
    fieldProps,
    langCode: 'webOperation_company_registerTime_tableColumn_text',
  },
  {
    dataIndex: 'companyId',
    width: 160,
  },
  {
    dataIndex: 'companyName',
    width: 160,
  },
  {
    dataIndex: 'adminId',
    width: 170,
  },
  {
    dataIndex: 'adminEmail',
    width: 150,
  },
  {
    dataIndex: 'adminFirstName',
    width: 150,
    hideInSearch: true,
  },
  {
    dataIndex: 'adminLastName',
    width: 150,
    hideInSearch: true,
  },
  {
    dataIndex: 'adminName',
    hideInTable: true,
  },
  {
    dataIndex: 'companyState',
    width: 150,
    valueEnum: {
      1: getIntl().formatMessage({ id: 'webOperation_dictionary_normal_select_text' }),
      2: getIntl().formatMessage({ id: 'webOperation_dictionary_deactivate_select_text' }),
      4: getIntl().formatMessage({ id: 'webOperation_dictionary_cancelled_select_text' }),
    },
    fieldProps: { mode: 'multiple', showArrow: true },
  },
  {
    dataIndex: 'registerTime',
    width: 150,
    valueType: 'dateTime',
    hideInSearch: true,
    render: (_, record) =>
      record.registerTime ? dayjs(Number(record.registerTime)).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
];

export const detailColumns: typeColumns[] = [
  {
    dataIndex: 'companyId',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'companyName',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'companyCountry',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'companyAddress',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'companyPost',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'adminId',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'adminEmail',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'adminFirstName',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'adminLastName',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'companyState',
    showInForm: true,
    readonly: true,
    valueEnum: {
      1: getIntl().formatMessage({ id: 'webOperation_dictionary_normal_select_text' }),
      2: getIntl().formatMessage({ id: 'webOperation_dictionary_deactivate_select_text' }),
      4: getIntl().formatMessage({ id: 'webOperation_dictionary_cancelled_select_text' }),
    },
    valueType: 'select',
  },
  {
    dataIndex: 'registerTime',
    valueType: 'dateTime',
    showInForm: true,
    readonly: true,
    render: (_, record) =>
      record.registerTime ? dayjs(Number(record.registerTime)).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
];

export const adminColumns: typeColumns[] = [
  {
    dataIndex: 'adminId',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'adminEmail',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'adminFirstName',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'adminLastName',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'changeTime',
    valueType: 'dateTime',
    showInForm: true,
    readonly: true,
    render: (_, record) =>
      record.changeTime ? dayjs(Number(record.changeTime)).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
];
