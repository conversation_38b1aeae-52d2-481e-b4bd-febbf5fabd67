import React, { useRef } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Button } from 'antd';
import { Access } from '@@/plugin-access/access';
import { ProFormInstance } from '@ant-design/pro-components';
import ResizableTable from '@/components/Table';
import { createKAC } from '@/components/KeepAlive';
import { useDeviceCategories, useBrands } from '@/hooks/selectHooks';
import { getDevices, exportDevices } from '@/services/company';
import { listColumns } from './columns/Device';
import { useColumn } from '@/hooks/column';
import useExport from '@/hooks/useExport';
import { rbac } from '@/access';
const CompanyAndDevice: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const { createColumns } = useColumn();
  const access = useAccess();
  const intl = useIntl();
  const { valueEnum: categoryValueEnum } = useDeviceCategories();
  const { valueEnum: brandValueEnum } = useBrands();
  const { run: handleExport, loading: exportLoading } = useExport(
    exportDevices,
    formRef,
    intl.formatMessage({ id: 'webOperation_sms_export_fileName_text' }),
  );

  const allColumns = createColumns(
    'companyDevice',
    listColumns({ categoryValueEnum, brandValueEnum }),
  );

  return (
    <>
      <ResizableTable<API.CompanyAndDeviceItem, API.CompanyAndDevicePageParams>
        formRef={formRef}
        rowKey={(_, index: number | undefined) => String(index)}
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          defaultPageSize: 10,
          pageSizeOptions: ['10', '20', '50'],
          showTotal: (total) =>
            `${intl.formatMessage({
              id: 'webCommon_page_prefix_pagination_label',
            })}${total}${intl.formatMessage({
              id: 'webCommon_page_suffix_pagination_label',
            })}`,
        }}
        headerTitle={
          <Access accessible={access.checkAuth(rbac.COMPANY.DEVICE.EXPORT)}>
            <Button loading={exportLoading} type="primary" onClick={handleExport}>
              <FormattedMessage id="webCommon_page_export_button_text" />
            </Button>
          </Access>
        }
        scroll={{ x: 1300 }}
        request={getDevices}
        columns={allColumns}
      />
    </>
  );
};
export default createKAC(CompanyAndDevice);
