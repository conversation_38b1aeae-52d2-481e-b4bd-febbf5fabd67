// 框架依赖引入
import React, { useRef, useState } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Divider, Modal, Space, message } from 'antd';
import { useRequest } from '@@/plugin-request/request';
import { Access } from '@@/plugin-access/access';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import ResizableTable from '@/components/Table';
// 自定义公共依赖引入
import { useColumn } from '@/hooks/column';
import KeepAlive from '@/components/KeepAlive';
// 页面自定义依赖引入
import TemplateEdit, { RefType } from '@/pages/message/TemplateEdit';
import { deleteTemplate, getList } from '@/services/template';
import { listColumns } from './columns/TemplateList';

const TemplateList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const editRef = useRef<RefType>(null);
  const { createColumns } = useColumn();
  const intl = useIntl();
  const access = useAccess();
  const [templateId, setTemplateId] = useState<string | undefined>();

  const { run: handleDelete, loading } = useRequest(
    async () => {
      if (!templateId) return;
      await deleteTemplate(templateId);
      setTemplateId(undefined);
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      actionRef.current?.reload();
    },
    { manual: true },
  );

  const actionColumn: ProColumns = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 180,
    fixed: 'right',
    render: (_, record) => [
      <Space key="edit" split={<Divider type="vertical" />}>
        <Access accessible={access.canViewMessageTemplate()}>
          <a
            key="view"
            onClick={() => {
              editRef.current?.edit(record, 'view');
            }}
          >
            <FormattedMessage id="webCommon_page_view_common_text" />
          </a>
        </Access>
        <Access accessible={access.canEditMessageTemplate()}>
          <a
            key="edit"
            onClick={() => {
              if (record.usedTimes > 0) {
                message.info(
                  intl.formatMessage({ id: 'webOperation_template_disabled_toast_text' }),
                );
                return;
              }
              editRef.current?.edit(record, 'edit');
            }}
          >
            <FormattedMessage id="webCommon_page_edit_common_text" />
          </a>
        </Access>
        <Access accessible={access.canDeleteMessageTemplate()}>
          <a
            key="delete"
            onClick={() => {
              if (record.usedTimes > 0) {
                message.info(
                  intl.formatMessage({ id: 'webOperation_template_notDeleted_toast_text' }),
                );
                return;
              }
              setTemplateId(record.id);
            }}
          >
            <FormattedMessage id="webCommon_page_delete_tableButton_linkText" />
          </a>
        </Access>
      </Space>,
    ],
  };

  const initColumns = [...listColumns, actionColumn];
  const allColumns = createColumns('template', initColumns);

  return (
    <>
      <ResizableTable<API.MessageTemplateItem, API.MessageTemplatePageParams>
        actionRef={actionRef}
        rowKey="id"
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        headerTitle={
          <TemplateEdit
            ref={editRef}
            key="create"
            refresh={() => {
              return actionRef.current?.reload();
            }}
          />
        }
        scroll={{ x: 1300 }}
        request={getList}
        columns={allColumns}
      />

      <Modal
        title={intl.formatMessage({ id: 'webCommon_page_confirmToDelete_modal_title' })}
        open={!!templateId}
        onOk={handleDelete}
        onCancel={() => setTemplateId(undefined)}
        confirmLoading={loading}
      >
        <FormattedMessage id="webCommon_page_delete_modal_message" />
      </Modal>
    </>
  );
};
export default () => {
  return (
    <KeepAlive>
      <TemplateList />
    </KeepAlive>
  );
};
