import type { typeColumns } from '@/hooks/column';
import { extra } from '@/hooks/column';
import { ProFormDependency } from '@ant-design/pro-components';
import { FormattedMessage, getIntl } from '@@/plugin-locale/localeExports';

export const listColumns: typeColumns[] = [
  {
    dataIndex: 'id',
    width: 160,
  },
  {
    dataIndex: 'type',
    width: 150,
    showInForm: true,
    required: true,
    valueType: 'select',
    valueEnum: {
      0: getIntl().formatMessage({
        id: 'webOperation_dictionary_deviceInformation_select_text',
      }),
    },
  },
  {
    dataIndex: 'messageDisplayType',
    width: 150,
    showInForm: true,
    required: true,
    valueType: 'select',
    valueEnum: {
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_text_select_text',
      }),
      2: getIntl().formatMessage({
        id: 'webOperation_dictionary_voice_select_text',
      }),
    },
  },
  {
    dataIndex: 'name',
    width: 150,
    showInForm: true,
    required: true,
    render: (_, record) => record.name.message,
  },
  {
    dataIndex: 'title',
    width: 160,
    showInForm: true,
    required: true,
    render: (_, record) => record.title.message,
  },
  {
    dataIndex: 'content',
    width: 400,
    hideInSearch: true,
    showInForm: true,
    required: true,
    valueType: 'textarea',
    render: (_, record) => record.content.message,
  },
  {
    dataIndex: 'usedTimes',
    langCode: 'webOperation_template_used_tableColumn_text',
    width: 150,
    hideInSearch: true,
  },
  ...extra,
];

export const editColumns: typeColumns[] = [
  {
    dataIndex: 'id',
    width: 160,
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'type',
    width: 150,
    showInForm: true,
    required: true,
    valueType: 'select',
    valueEnum: {
      0: getIntl().formatMessage({
        id: 'webOperation_dictionary_deviceInformation_select_text',
      }),
    },
  },
  {
    dataIndex: 'messageDisplayType',
    width: 150,
    showInForm: true,
    required: true,
    valueType: 'select',
    valueEnum: {
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_text_select_text',
      }),
      2: getIntl().formatMessage({
        id: 'webOperation_dictionary_voice_select_text',
      }),
    },
  },
  {
    dataIndex: ['name', 'message'],
    langCode: 'webOperation_template_name_tableColumn_text',
    width: 150,
    showInForm: true,
    required: true,
    addonRender: (
      <ProFormDependency name={['name', 'langId']}>
        {({ name }) => {
          return (
            <span>
              <FormattedMessage id="webCommon_page_langId_common_text" />
              {name?.langId}
            </span>
          );
        }}
      </ProFormDependency>
    ),
  },
  {
    dataIndex: ['title', 'message'],
    langCode: 'webOperation_template_title_tableColumn_text',
    width: 160,
    showInForm: true,
    required: true,
    addonRender: (
      <ProFormDependency name={['title', 'langId']}>
        {({ title }) => {
          return (
            <span>
              <FormattedMessage id="webCommon_page_langId_common_text" />
              {title?.langId}
            </span>
          );
        }}
      </ProFormDependency>
    ),
  },
  {
    dataIndex: ['content', 'message'],
    langCode: 'webOperation_template_content_tableColumn_text',
    width: 150,
    showInForm: true,
    required: true,
    valueType: 'textarea',
  },
  {
    dataIndex: ['content', 'langId'],
    langCode: 'webCommon_page_langId_common_text',
    width: 160,
    showInForm: true,
    readonly: true,
  },
];

export const detailColumns: typeColumns[] = [
  {
    dataIndex: 'id',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'type',
    showInForm: true,
    readonly: true,
    valueType: 'select',
    valueEnum: {
      0: getIntl().formatMessage({
        id: 'webOperation_dictionary_deviceInformation_select_text',
      }),
    },
  },
  {
    dataIndex: 'messageDisplayType',
    showInForm: true,
    readonly: true,
    valueType: 'select',
    valueEnum: {
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_text_select_text',
      }),
      2: getIntl().formatMessage({
        id: 'webOperation_dictionary_voice_select_text',
      }),
    },
  },
  {
    dataIndex: ['name', 'message'],
    langCode: 'webOperation_template_name_tableColumn_text',
    showInForm: true,
    readonly: true,
    extraRender: (
      <ProFormDependency name={['name', 'langId']}>
        {({ name }) => {
          return (
            <span>
              <FormattedMessage id="webCommon_page_langId_common_text" />
              {name?.langId}
            </span>
          );
        }}
      </ProFormDependency>
    ),
  },
  {
    dataIndex: ['title', 'message'],
    langCode: 'webOperation_template_title_tableColumn_text',
    showInForm: true,
    readonly: true,
    extraRender: (
      <ProFormDependency name={['title', 'langId']}>
        {({ title }) => {
          return (
            <span>
              <FormattedMessage id="webCommon_page_langId_common_text" />
              {title?.langId}
            </span>
          );
        }}
      </ProFormDependency>
    ),
  },
  {
    dataIndex: ['content', 'message'],
    langCode: 'webOperation_template_content_tableColumn_text',
    showInForm: true,
    readonly: true,
    valueType: 'textarea',
  },
  {
    dataIndex: ['content', 'langId'],
    langCode: 'webCommon_page_langId_common_text',
    showInForm: true,
    readonly: true,
  },
];
