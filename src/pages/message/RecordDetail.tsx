import ResizableTable from '@/components/Table';
import { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import React, { useRef, useState, useImperativeHandle } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Button } from 'antd';
import { omit, get } from 'lodash-es';
import { Access } from '@@/plugin-access/access';
import { exportMessagePushRecord, viewMessageRecord } from '@/services/message';
import FormContainer from '@/components/Form/FormContainer';
import useExport from '@/hooks/useExport';

export interface RefType {
  show: (item: API.MessageRecordItem) => void;
}

const RecordDetail = React.forwardRef<RefType, APP.DetailProps>((props, ref) => {
  const actionRef = useRef<ActionType>();
  const [visible, setVisible] = useState<boolean>(false);
  const intl = useIntl();
  const access = useAccess();
  const formRef = useRef<ProFormInstance>();
  const [initialValues, setInitialValues] = useState<API.MessageRecordItem>({});
  const exportDetails = async (params: API.MessageRecordPageParams) => {
    return await exportMessagePushRecord({
      ...params,
      ...{
        systemMessageId: initialValues.msgId,
        messageType: initialValues.messageType,
        productId: initialValues.productId || undefined,
      },
    });
  };
  const { run: handleExport, loading: exportLoading } = useExport(
    exportDetails,
    formRef,
    intl.formatMessage({ id: 'webOperation_messageDetail_export_filename_text' }),
  );
  const columns: ProColumns<API.MessageRecordItem>[] = [
    {
      dataIndex: 'createTimeFilter',
      valueType: 'dateRange',
      hideInTable: true,
      title: <FormattedMessage id="webOperation_message_pushTime_tableColumn_text" />,
      fieldProps: {
        placeholder: [
          intl.formatMessage({
            id: 'webCommon_page_select_common_placeholder',
          }),
          intl.formatMessage({
            id: 'webCommon_page_select_common_placeholder',
          }),
        ],
      },
      order: 10,
    },
    {
      title: <FormattedMessage id="webOperation_message_recordId_tableColumn_text" />,
      dataIndex: 'uuid',
      order: 9,
      width: 170,
    },
    {
      title: <FormattedMessage id="webOperation_message_type_tableColumn_text" />,
      dataIndex: 'messageType',
      order: 8,
      width: 120,
      valueEnum: {
        0: <FormattedMessage id="webOperation_dictionary_systemMessage_select_text" />,
        1: <FormattedMessage id="webOperation_dictionary_marketingMessage_select_text" />,
        2: <FormattedMessage id="webOperation_dictionary_deviceMessage_select_text" />,
      },
      hideInSearch: true,
    },
    {
      title: <FormattedMessage id="webOperation_message_title_tableColumn_text" />,
      dataIndex: 'title',
      order: 7,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_message_content_tableColumn_text" />,
      dataIndex: 'content',
      hideInSearch: true,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_message_pushMethod_tableColumn_text" />,
      dataIndex: 'pushType',
      order: 6,
      width: 120,
      valueEnum: {
        POPUP: <FormattedMessage id="webOperation_dictionary_popup_select_text" />,
        MESSAGE: <FormattedMessage id="webOperation_dictionary_message_select_text" />,
        BANNER: <FormattedMessage id="webOperation_dictionary_banner_select_text" />,
        MAIL: <FormattedMessage id="webOperation_dictionary_mail_select_text" />,
        TOMBSTONE: <FormattedMessage id="webOperation_dictionary_tombstone_select_text" />,
        PHONE_VOICE: <FormattedMessage id="webOperation_dictionary_phoneVoice_select_text" />,
      },
    },
    {
      title: <FormattedMessage id="webOperation_message_targetUserId_tableColumn_text" />,
      dataIndex: 'userId',
      order: 5,
      width: 170,
    },
    {
      title: <FormattedMessage id="webOperation_message_account_tableColumn_text" />,
      dataIndex: 'email',
      order: 4,
      width: 220,
    },
    {
      title: <FormattedMessage id="webOperation_message_pushTime_tableColumn_text" />,
      dataIndex: 'createTime',
      order: 10,
      width: 150,
      hideInSearch: true,
      valueType: 'dateTime',
    },
    {
      title: <FormattedMessage id="webOperation_message_result_tableColumn_text" />,
      dataIndex: 'pushResult',
      order: 3,
      width: 120,
      valueEnum: {
        true: intl.formatMessage({ id: 'webOperation_message_succeed_dictionnary_text' }),
        false: intl.formatMessage({ id: 'webOperation_message_failed_dictionnary_text' }),
      },
    },
  ];

  useImperativeHandle(ref, () => ({
    show: (item: API.MessageRecordItem = {}) => {
      setInitialValues(item);
      setVisible(true);
    },
  }));

  const onClose = () => {
    setVisible(false);
  };
  return (
    <FormContainer
      title={intl.formatMessage({ id: 'webOperation_message_detail_drawer_title' })}
      width="80%"
      onCancel={onClose}
      open={visible}
      destroyOnClose={true}
      hiddenConfirm={true}
    >
      <ResizableTable<API.MessageRecordItem, API.MessageRecordPageParams>
        actionRef={actionRef}
        formRef={formRef}
        rowKey={(_, index: number | undefined) => String(index)}
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        scroll={{ x: 150 * 9 + 50 }}
        headerTitle={
          <Access accessible={access.canExportMessagePushRecord()}>
            <Button
              key="export"
              loading={exportLoading}
              type="primary"
              ghost
              onClick={handleExport}
            >
              <FormattedMessage id="webCommon_page_export_button_text" />
            </Button>
          </Access>
        }
        request={viewMessageRecord}
        params={{
          systemMessageId: initialValues.msgId,
          messageType: initialValues.messageType,
          productId: initialValues.productId || undefined,
        }}
        beforeSearchSubmit={(params) => {
          return {
            ...omit(params, ['createTimeFilter']),
            createStartTime: get(params, ['createTimeFilter', 0]),
            createEndTime: get(params, ['createTimeFilter', 1]),
            systemMessageId: initialValues.msgId,
            messageType: initialValues.messageType,
            productId: initialValues.productId || undefined,
          };
        }}
        columns={columns}
      />
    </FormContainer>
  );
});

export default RecordDetail;
