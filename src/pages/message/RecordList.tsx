import ResizableTable from '@/components/Table';
import { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { Button } from 'antd';
import { Access } from '@@/plugin-access/access';
import { AccessLink } from '@/components/Button/AccessButton';
import { rbac } from '@/access';
import { exportMessageRecord, listMessageRecord } from '@/services/message';
import useExport from '@/hooks/useExport';
import { createKAC } from '@/components/KeepAlive';
import RecordDetail, { RefType as DetailRefType } from './RecordDetail';

// 消息类型
enum MessageType {
  System = '0', // 系统消息
  Marketing = '1', // 营销消息
  Device = '2', // 设备消息
}
const RecordList = () => {
  const actionRef = useRef<ActionType>();
  const detailRef = useRef<DetailRefType>(null);
  const access = useAccess();
  const intl = useIntl();
  const formRef = useRef<ProFormInstance>();
  const [disabled, setDisabled] = useState(true);
  const { run: handleExport, loading: exportLoading } = useExport(
    exportMessageRecord,
    formRef,
    intl.formatMessage({ id: 'webOperation_message_export_filename_text' }),
  );

  const columns: ProColumns<API.MessageRecordItem>[] = [
    {
      title: <FormattedMessage id="webOperation_message_id_tableColumn_text" />,
      dataIndex: 'msgId',
      order: 10,
      width: 170,
    },
    {
      title: <FormattedMessage id="webOperation_message_type_tableColumn_text" />,
      dataIndex: 'messageType',
      order: 9,
      width: 150,
      valueEnum: {
        0: <FormattedMessage id="webOperation_dictionary_systemMessage_select_text" />,
        1: <FormattedMessage id="webOperation_dictionary_marketingMessage_select_text" />,
        2: <FormattedMessage id="webOperation_dictionary_deviceMessage_select_text" />,
      },
      initialValue: MessageType.System,
      fieldProps: {
        allowClear: false,
        onChange: (value: MessageType) => {
          if (value === MessageType.Device) {
            setDisabled(false);
          } else {
            formRef.current?.setFieldValue('model', '');
            formRef.current?.setFieldValue('commodityModel', '');
            setDisabled(true);
          }
        },
      },
    },
    {
      title: <FormattedMessage id="webOperation_message_productModel_tableColumn_text" />,
      dataIndex: 'model',
      width: 150,
      hideInTable: true,
      fieldProps: {
        disabled,
      },
    },
    {
      title: <FormattedMessage id="webOperation_message_commodityModel_tableColumn_text" />,
      dataIndex: 'commodityModel',
      width: 150,
      hideInTable: true,
      fieldProps: {
        disabled,
      },
    },
    {
      title: <FormattedMessage id="webOperation_message_title_tableColumn_text" />,
      dataIndex: 'title',
      order: 8,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_message_content_tableColumn_text" />,
      dataIndex: 'content',
      hideInSearch: true,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_message_pushMethod_tableColumn_text" />,
      dataIndex: 'pushTypeCodes',
      order: 7,
      valueEnum: {
        POPUP: <FormattedMessage id="webOperation_dictionary_popup_select_text" />,
        MESSAGE: <FormattedMessage id="webOperation_dictionary_message_select_text" />,
        BANNER: <FormattedMessage id="webOperation_dictionary_banner_select_text" />,
        MAIL: <FormattedMessage id="webOperation_dictionary_mail_select_text" />,
        TOMBSTONE: <FormattedMessage id="webOperation_dictionary_tombstone_select_text" />,
        PHONE_VOICE: <FormattedMessage id="webOperation_dictionary_phoneVoice_select_text" />,
      },
      width: 150,
      hideInSearch: true,
    },
    {
      title: <FormattedMessage id="webOperation_message_pushMethod_tableColumn_text" />,
      dataIndex: 'pushTypeCode',
      order: 7,
      valueEnum: {
        POPUP: <FormattedMessage id="webOperation_dictionary_popup_select_text" />,
        MESSAGE: <FormattedMessage id="webOperation_dictionary_message_select_text" />,
        BANNER: <FormattedMessage id="webOperation_dictionary_banner_select_text" />,
        MAIL: <FormattedMessage id="webOperation_dictionary_mail_select_text" />,
        TOMBSTONE: <FormattedMessage id="webOperation_dictionary_tombstone_select_text" />,
        PHONE_VOICE: <FormattedMessage id="webOperation_dictionary_phoneVoice_select_text" />,
      },
      width: 150,
      hideInTable: true,
    },
    {
      title: <FormattedMessage id="webOperation_message_pushCount_tableColumn_text" />,
      dataIndex: 'pushAllNum',
      hideInSearch: true,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_message_successCount_tableColumn_text" />,
      dataIndex: 'pushSuccessNum',
      hideInSearch: true,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_message_failCount_tableColumn_text" />,
      dataIndex: 'pushFailNum',
      hideInSearch: true,
      width: 150,
    },
    {
      title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
      dataIndex: 'option',
      valueType: 'option',
      width: 100,
      fixed: 'right',
      render: (_: any, record: API.MessageRecordItem) => (
        <AccessLink
          text="webCommon_page_detail_common_text"
          code={rbac.MESSAGE_RECORD.VIEW}
          onClick={() => {
            detailRef.current?.show(record);
          }}
        />
      ),
    },
  ];

  return (
    <>
      <ResizableTable<API.MessageRecordItem, API.MessageRecordPageParams>
        actionRef={actionRef}
        formRef={formRef}
        rowKey="msgId"
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        scroll={{ x: 150 * 9 + 50 }}
        headerTitle={
          <Access accessible={access.canExportMessageRecord()}>
            <Button
              key="export"
              loading={exportLoading}
              type="primary"
              ghost
              onClick={handleExport}
            >
              <FormattedMessage id="webCommon_page_export_button_text" />
            </Button>
          </Access>
        }
        request={listMessageRecord}
        columns={columns}
      />
      <RecordDetail ref={detailRef} key="detail" />
    </>
  );
};

export default createKAC(RecordList);
