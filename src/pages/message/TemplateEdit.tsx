import { useRequest } from '@@/plugin-request/request';
import { PlusOutlined } from '@ant-design/icons';
import { ProForm, ProFormInstance } from '@ant-design/pro-components';
import { Button, message } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Access } from '@@/plugin-access/access';

import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';
import type { typeColumns } from '@/hooks/column';
import { createTemplate, editTemplate } from '@/services/template';
import { listColumns, editColumns, detailColumns } from './columns/TemplateList';

type MessageTemplateItem = API.MessageTemplateItem;

export interface FormProps {
  refresh?: (resetPageIndex?: boolean | undefined) => Promise<void> | undefined;
}

export interface RefType {
  edit: (record: MessageTemplateItem, mode: string) => void;
}

const TemplateEdit = React.forwardRef<RefType, FormProps>((props, ref) => {
  const { refresh } = props;
  const intl = useIntl();
  const access = useAccess();
  const [visible, setVisible] = useState<boolean>(false);
  const [method, setMethod] = useState<string>('create');
  const [columns, setColumns] = useState<typeColumns[]>([]);
  const [showConfirm, setShowConfirm] = useState({} as any);
  const formRef = useRef<ProFormInstance>();
  const [initialValues, setInitialValues] = useState<API.MessageTemplateItem>({} as any);

  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    edit: (record: MessageTemplateItem, mode: string) => {
      // 设置页面表单功能模式
      setMethod(mode);
      switch (mode) {
        // 编辑
        case 'edit':
          setShowConfirm({ onConfirm: () => formRef.current?.submit() });
          setColumns(editColumns);
          break;
        // 查看
        case 'view':
          // 查看隐藏确定按钮
          setShowConfirm({ hiddenConfirm: true });
          setColumns(detailColumns);
          break;
      }
      record.type = String(record.type);
      record.messageDisplayType = String(record.messageDisplayType);
      setInitialValues(record);
      setVisible(true);
    },
  }));

  const { run: handleSubmit, loading } = useRequest(
    async (values) => {
      if (initialValues.id) {
        values.id = initialValues.id;
        values.name.langId = initialValues.name.langId;
        values.title.langId = initialValues.title.langId;
        values.content.langId = initialValues.content.langId;
        await editTemplate(values);
      } else {
        await createTemplate(values);
      }
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setVisible(false);
      refresh?.();
    },
    { manual: true },
  );

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: `webOperation_template_${method}_drawer_title` })}
        width="50%"
        onCancel={onClose}
        {...showConfirm}
        open={visible}
        destroyOnClose={true}
        loading={loading}
      >
        <ProForm
          formRef={formRef}
          initialValues={initialValues}
          onFinish={handleSubmit}
          labelCol={{ span: 5 }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
          className={method === 'view' ? 'operation-ant-view-form' : ''}
        >
          <FormFields columns={columns} pageName="template" />
        </ProForm>
      </FormContainer>
      <Access accessible={access.canCreateMessageTemplate()}>
        <Button
          type="primary"
          onClick={() => {
            // 页面表单功能为添加新协议
            setMethod('create');
            setColumns(listColumns);
            setInitialValues({} as any);
            setShowConfirm({ onConfirm: () => formRef.current?.submit() });
            setVisible(true);
          }}
        >
          <PlusOutlined />
          <FormattedMessage id="webOperation_template_create_drawer_title" />
        </Button>
      </Access>
    </>
  );
});

export default TemplateEdit;
