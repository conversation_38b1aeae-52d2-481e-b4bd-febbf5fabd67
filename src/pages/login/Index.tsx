import React from 'react';
import { useLocation } from 'umi';
import { useMount } from 'ahooks';
import { doLoginByTicket, getSsoAuthUrl } from '@/services/login';
const SsoLogin: React.FC = () => {
  const { query = {} }: any = useLocation();
  const { back, ticket } = query;
  useMount(async () => {
    if (ticket) {
      const res = await doLoginByTicket({ ticket });
      const backUrl = new URL(decodeURIComponent(back));
      const source = backUrl.searchParams.get('source');
      if (source && source === 'iframe') {
        const targetWindow = window.parent;
        targetWindow.postMessage({ ...res.data, appId: ROOT_PATH.replace(/\//g, '') }, '*');
      } else {
        localStorage.setItem(ROOT_PATH.replace(/\//g, '') + '-token', res.data.data);
        window.location.href = decodeURIComponent(back);
      }
    } else {
      const res = await getSsoAuthUrl({ clientLoginUrl: location.href });
      const { data } = res.data;
      if (data) {
        window.location.href = data;
      }
    }
  });

  return <div />;
};

export default SsoLogin;
