import { createBrand, editBrand } from '@/services/brand';
import { useRequest } from '@@/plugin-request/request';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormInstance,
  ProFormFieldSet,
  ProFormRadio,
  ProFormDependency,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Button, message, Space } from 'antd';
import { last, split, join, slice } from 'lodash-es';
import React, { useImperativeHandle, useRef, useState } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Access } from '@@/plugin-access/access';
import { getS3File, uploadImage } from '@/services/common';
import { ImageUpload } from '@/components/Form/ImageUpload';

import FormContainer from '@/components/Form/FormContainer';
import { len128, len500, urlChecked } from '@/utils/validate';

type BrandItem = API.BrandItem;

export interface BrandFormProps {
  refresh?: (resetPageIndex?: boolean | undefined) => Promise<void> | undefined;
}

export interface RefType {
  edit: (item: BrandItem) => void;
}

const EditForm = React.forwardRef<RefType, BrandFormProps>((props, ref) => {
  const { refresh } = props;
  const intl = useIntl();
  const access = useAccess();
  const [visible, setVisible] = useState<boolean>(false);
  const formRef = useRef<ProFormInstance<BrandItem>>();
  const [initialValues, setInitialValues] = useState<BrandItem>({});

  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    edit: (item: BrandItem = {}) => {
      const values = { ...item };
      if (Number(values.iconType) === 1) {
        values.iconAddress = values.brandIcon;
        values.brandIcon = '';
      }
      values.iconType = String(values.iconType);
      setInitialValues(values);
      setVisible(true);
    },
  }));

  const { run: handleSubmit, loading } = useRequest(
    async (values: API.BrandItem) => {
      if (Number(values.iconType) === 1) {
        const { key } = await getS3File(values.brandIcon);
        values.brandIcon = key;
      }
      if (Number(values.iconType) === 0 && values.brandIcon.includes('http')) {
        const urlNoProtocol = split(last(split(values.brandIcon, '://')), '/');
        values.brandIcon = join(slice(urlNoProtocol, 1, urlNoProtocol.length), '/');
      }
      if (initialValues.id) {
        values.id = initialValues.id;
        await editBrand(values);
      } else {
        values.brandName = values.brandName.message;
        await createBrand(values);
      }
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setVisible(false);
      refresh?.();
    },
    { manual: true },
  );

  const setFieldValue = (key: string, value: any) => {
    const data = {};
    data[key] = value;
    formRef.current?.setFieldsValue(data);
  };

  return (
    <>
      <FormContainer
        title={
          initialValues.id
            ? intl.formatMessage({ id: 'webOperation_brand_edit_drawer_title' })
            : intl.formatMessage({ id: 'webOperation_brand_create_drawer_title' })
        }
        width="50%"
        onCancel={onClose}
        onConfirm={() => formRef.current?.submit()}
        open={visible}
        destroyOnClose={true}
        loading={loading}
      >
        <ProForm
          formRef={formRef}
          initialValues={initialValues}
          onFinish={handleSubmit}
          labelCol={{ span: 4 }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
        >
          {initialValues.id ? (
            <ProFormText
              name="id"
              label={intl.formatMessage({ id: 'webOperation_brand_id_tableColumn_text' })}
              readonly
            />
          ) : null}
          <ProFormText
            name={['brandName', 'message']}
            label={intl.formatMessage({ id: 'webOperation_brand_brandName_tableColumn_text' })}
            rules={[
              {
                required: true,
                whitespace: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_brand_brandName_tableColumn_text' }),
              },
              len128,
            ]}
            placeholder={
              intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
              intl.formatMessage({ id: 'webOperation_brand_brandName_tableColumn_text' })
            }
            fieldProps={
              initialValues.id
                ? {
                    addonAfter: (
                      <ProFormDependency name={['brandName', 'langId']}>
                        {({ brandName }) => {
                          return (
                            <span>
                              <FormattedMessage id="webCommon_page_langId_common_text" />
                              {brandName?.langId}
                            </span>
                          );
                        }}
                      </ProFormDependency>
                    ),
                  }
                : {}
            }
          />
          <ProFormText name={['brandName', 'langId']} hidden={true} />
          <ProFormFieldSet
            name="brandInfo"
            label={intl.formatMessage({ id: 'webOperation_brand_brandIcon_tableColumn_text' })}
            type="group"
            initialValue=" "
            rules={[
              {
                required: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_brand_brandIcon_tableColumn_text' }),
              },
            ]}
            transform={(value: any) => ({})}
          >
            <Space>
              <ProFormRadio.Group
                name="iconType"
                label={false}
                valueEnum={{
                  0: intl.formatMessage({ id: 'webOperation_dictionary_imageUpload_select_text' }),
                  1: intl.formatMessage({ id: 'webOperation_dictionary_imageLink_select_text' }),
                }}
              />
              <ProFormDependency name={['iconType']}>
                {({ iconType }) => {
                  if (iconType === '1') {
                    return (
                      <ProFormTextArea
                        rules={[
                          {
                            required: true,
                            message:
                              intl.formatMessage({
                                id: 'webCommon_page_input_common_placeholder',
                              }) +
                              intl.formatMessage({
                                id: 'webOperation_brand_iconAddress_tableColumn_text',
                              }),
                          },
                          urlChecked,
                          len500,
                        ]}
                        width="lg"
                        name="iconAddress"
                        label={intl.formatMessage({
                          id: 'webOperation_brand_iconAddress_tableColumn_text',
                        })}
                        placeholder={
                          intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                          intl.formatMessage({
                            id: 'webOperation_brand_iconAddress_tableColumn_text',
                          })
                        }
                        transform={(value: any) => ({ brandIcon: value })}
                      />
                    );
                  }
                  return (
                    <ProForm.Item
                      name="brandIcon"
                      label={false}
                      rules={[
                        {
                          required: true,
                          message:
                            intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                            intl.formatMessage({
                              id: 'webOperation_brand_brandIcon_tableColumn_text',
                            }),
                        },
                      ]}
                      addonAfter={intl.formatMessage({
                        id: 'webCommon_page_imageSize_tip_message',
                      })}
                    >
                      <ImageUpload
                        name="brandIcon"
                        maxCount={1}
                        getSignedUrl={async (fileName) => {
                          const data = await uploadImage({
                            fileName,
                            fileType: 'picture',
                          });
                          const preSignedUrl = new URL(data.preSignedUrl);
                          const domain = preSignedUrl.protocol + '//' + preSignedUrl.host + '/';
                          setFieldValue?.('brandIcon', domain + data.key);
                          return data;
                        }}
                        setFieldValue={setFieldValue}
                      />
                    </ProForm.Item>
                  );
                }}
              </ProFormDependency>
            </Space>
          </ProFormFieldSet>
          <ProFormTextArea
            name="description"
            label={intl.formatMessage({ id: 'webOperation_brand_description_tableColumn_text' })}
            placeholder={
              intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
              intl.formatMessage({ id: 'webOperation_brand_description_tableColumn_text' })
            }
            rules={[len500]}
          />
        </ProForm>
      </FormContainer>
      <Access accessible={access.canCreateBrand()}>
        <Button
          type="primary"
          onClick={() => {
            setInitialValues({ iconType: '0' });
            setVisible(true);
          }}
        >
          <PlusOutlined />
          <FormattedMessage id="webOperation_brand_create_drawer_title" />
        </Button>
      </Access>
    </>
  );
});

export default EditForm;
