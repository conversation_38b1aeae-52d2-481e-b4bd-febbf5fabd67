import React, { useRef, useState } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Divider, Modal, Space, message } from 'antd';
import { useRequest } from '@@/plugin-request/request';
import { Access } from '@@/plugin-access/access';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import ResizableTable from '@/components/Table';
import KeepAlive from '@/components/KeepAlive';
import DeleteResultModal, { RefType as ModalRefType } from '@/components/Modal/index';
import { showDeleteFailReason } from '@/hooks/action';

import BrandEdit, { RefType } from '@/pages/brand/BrandEdit';
import BrandDetail, { RefType as DetailRef } from '@/pages/brand/BrandDetail';
import { deleteBrand, getList } from '@/services/brand';
import { useColumn } from '@/hooks/column';
import { listColumns } from './columns/BrandList';

const BrandList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const editRef = useRef<RefType>(null);
  const viewRef = useRef<DetailRef>(null);
  const modalRef = useRef<ModalRefType>(null);
  const { createColumns } = useColumn();
  const intl = useIntl();
  const access = useAccess();
  const [brandId, setBrandId] = useState<string | undefined>();

  const { run: handleDelete, loading } = useRequest(
    async () => {
      if (!brandId) return;
      try {
        await deleteBrand(brandId);
        setBrandId(undefined);
        message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
        actionRef.current?.reload();
      } catch (e: any) {
        setBrandId(undefined);
        showDeleteFailReason(modalRef, '1090022003', e);
      }
    },
    { manual: true },
  );

  const actionColumn: ProColumns = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 200,
    fixed: 'right',
    render: (_, record) => [
      <Space key="edit" split={<Divider type="vertical" />}>
        <Access accessible={access.canViewBrand()}>
          <a
            key="view"
            onClick={() => {
              viewRef.current?.view(record);
            }}
          >
            <FormattedMessage id="webCommon_page_view_common_text" />
          </a>
        </Access>
        <Access accessible={access.canEditBrand()}>
          <a
            key="edit"
            onClick={() => {
              editRef.current?.edit(record);
            }}
          >
            <FormattedMessage id="webCommon_page_edit_common_text" />
          </a>
        </Access>
        <Access accessible={access.canDeleteBrand()}>
          <a
            key="delete"
            onClick={() => {
              setBrandId(record.id);
            }}
          >
            <FormattedMessage id="webCommon_page_delete_tableButton_linkText" />
          </a>
        </Access>
      </Space>,
    ],
  };

  const initColumns = [...listColumns, actionColumn];
  const allColumns = createColumns('brand', initColumns);

  return (
    <>
      <ResizableTable<API.BrandItem, API.BrandPageParams>
        actionRef={actionRef}
        rowKey="id"
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        headerTitle={
          <BrandEdit
            ref={editRef}
            key="create"
            refresh={() => {
              return actionRef.current?.reload();
            }}
          />
        }
        scroll={{ x: 1300 }}
        request={getList}
        columns={allColumns}
      />
      <BrandDetail ref={viewRef} key="view" />
      <Modal
        title={intl.formatMessage({ id: 'webCommon_page_confirmToDelete_modal_title' })}
        visible={!!brandId}
        onOk={handleDelete}
        onCancel={() => setBrandId(undefined)}
        confirmLoading={loading}
      >
        <FormattedMessage id="webOperation_brand_delete_modal_message" />
      </Modal>
      <DeleteResultModal ref={modalRef} />
    </>
  );
};
export default () => {
  return (
    <KeepAlive>
      <BrandList />
    </KeepAlive>
  );
};
