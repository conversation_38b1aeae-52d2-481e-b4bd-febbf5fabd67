import type { typeColumns } from '@/hooks/column';
import { extra, dateFilter } from '@/hooks/column';
import { Image } from 'antd';

export const listColumns: typeColumns[] = [
  ...dateFilter,
  {
    dataIndex: 'id',
    width: 160,
    showInDetail: true,
  },
  {
    dataIndex: ['brandName', 'message'],
    width: 150,
    showInForm: true,
    required: true,
    langCode: 'webOperation_brand_brandName_tableColumn_text',
    hideInSearch: true,
  },
  {
    dataIndex: 'brandName',
    width: 150,
    showInForm: true,
    required: true,
    hideInTable: true,
  },
  {
    dataIndex: 'brandIcon',
    width: 150,
    hideInSearch: true,
    showInDetail: true,
    required: true,
    valueType: 'image',
    render: (_, record) => <Image width={80} height={80} src={record.brandIcon} />,
  },
  {
    dataIndex: 'langId',
    width: 160,
    render: (_, record) => record?.brandName?.langId,
  },
  ...extra,
  {
    dataIndex: 'description',
    width: 150,
    hideInSearch: true,
    showInForm: true,
    valueType: 'textarea',
  },
];

export const detailColumns: typeColumns[] = [
  {
    dataIndex: 'id',
    width: 160,
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: ['brandName', 'message'],
    langCode: 'webOperation_brand_brandName_tableColumn_text',
    width: 150,
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: ['brandName', 'langId'],
    langCode: 'webCommon_page_langId_common_text',
    width: 160,
    showInForm: true,
    readonly: true,
  },
];
