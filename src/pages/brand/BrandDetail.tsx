import { useIntl } from 'umi';
import React, { useImperativeHandle, useState } from 'react';
import { Image } from 'antd';
import { ProForm } from '@ant-design/pro-components';
import type { typeColumns } from '@/hooks/column';
import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';
import { detailColumns } from './columns/BrandList';

type BrandItem = API.BrandItem;

export interface RefType {
  view: (item: BrandItem) => void;
}

const BrandDetail = React.forwardRef<RefType, APP.DetailProps>((props, ref) => {
  const intl = useIntl();
  const [visible, setVisible] = useState<boolean>(false);
  const [record, setRecord] = useState<BrandItem>({});

  const onClose = () => {
    setVisible(false);
  };

  const afterColumns: typeColumns[] = [
    {
      dataIndex: 'brandIcon',
      width: 150,
      valueType: 'image',
      showInForm: true,
      extraRender: <Image width={120} height={120} src={record.brandIcon} />,
    },
    {
      dataIndex: 'description',
      width: 150,
      showInForm: true,
      valueType: 'textarea',
      readonly: true,
    },
  ];

  useImperativeHandle(ref, () => ({
    view: (item: BrandItem) => {
      setRecord(item);
      setVisible(true);
    },
  }));

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: 'webOperation_brand_detail_drawer_title' })}
        width="40%"
        onCancel={onClose}
        hiddenConfirm={true}
        open={visible}
        destroyOnClose={true}
      >
        <ProForm
          initialValues={record}
          labelCol={{ span: 5 }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
        >
          <FormFields columns={detailColumns} pageName="brand" />
          <FormFields columns={afterColumns} pageName="brand" />
        </ProForm>
      </FormContainer>
    </>
  );
});

export default BrandDetail;
