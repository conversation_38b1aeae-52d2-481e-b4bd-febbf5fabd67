import React, { useImperativeHandle, useRef, useState } from 'react';
import { Button } from 'antd';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import {
  ProForm,
  ProFormInstance,
  ProFormText,
  ProFormSelect,
  ProFormDependency,
  ProFormCheckbox,
} from '@ant-design/pro-components';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { omit, includes, map, has } from 'lodash-es';

import FormContainer from '@/components/Form/FormContainer';

export interface RefType {
  edit: (item: API.GroupCondition, index: number) => void;
}

export interface ConditionFormProps {
  editCondition?: (value: API.GroupCondition, index?: number) => void;
}

const ConditionsAdd = React.forwardRef<RefType, ConditionFormProps>((props, ref) => {
  const { editCondition } = props;
  const intl = useIntl();
  const formRef = useRef<ProFormInstance<API.GroupCondition>>();
  const [visible, setVisible] = useState<boolean>(false);
  const [index, setIndex] = useState<number>();
  const [initialValues, setInitialValues] = useState<API.GroupCondition>({} as any);
  const access = useAccess();

  const { run: handleSubmit, loading } = useRequest(
    async (values) => {
      editCondition?.(values, index);
      setVisible(false);
    },
    { manual: true },
  );

  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    edit: (item: API.GroupCondition, current?: number) => {
      setIndex(current);
      setInitialValues(item || {});
      setVisible(true);
    },
  }));

  const onCreate = () => {
    setInitialValues({} as any);
    setVisible(true);
    setIndex(-1);
  };
  const dict = {
    groupConditionsByVersion: [
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_firmwareVersion_select_text',
        }),
        value: 'FIRMWARE_VERSION',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_phoneOsVersion_select_text',
        }),
        value: 'PHONE_OS_VERSION',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_appVersion_select_text',
        }),
        value: 'APP_VERSION',
      },
    ],
    groupConditionsByEquals: [
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_commercialModel_select_text',
        }),
        value: 'COMMERCIAL_MODEL',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_firmwareVersion_select_text',
        }),
        value: 'FIRMWARE_VERSION',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_rnVersion_select_text',
        }),
        value: 'RN_VERSION',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_productModel_select_text',
        }),
        value: 'PRODUCT_MODEL',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_userID_select_text',
        }),
        value: 'USER_ID',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_componentNO_select_text',
        }),
        value: 'COMPONENT_NO',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_phoneModel_select_text',
        }),
        value: 'PHONE_MODEL',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_deviceID_select_text',
        }),
        value: 'DEVICE_ID',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_deviceSN_select_text',
        }),
        value: 'DEVICE_SN',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_userAccount_select_text',
        }),
        value: 'USER_ACCOUNT',
      },
    ],
    groupConditionsByEqualsAndNot: [
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_appName_select_text',
        }),
        value: 'BUSINESS_TYPE',
      },
    ],
    groupRulesByVersion: [
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_graterThan_select_text',
        }),
        value: 'GRATER_THAN',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_lessThanEqual_select_text',
        }),
        value: 'LESS_THAN_EQUAL',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_graterThanEqual_select_text',
        }),
        value: 'GRATER_THAN_EQUAL',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_lessThan_select_text',
        }),
        value: 'LESS_THAN',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_include_select_text',
        }),
        value: 'INCLUDE',
      },
    ],
    groupRulesByEquals: [
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_equal_select_text',
        }),
        value: 'EQUALS',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_include_select_text',
        }),
        value: 'INCLUDE',
      },
    ],
    groupRulesByEqualsAndNot: [
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_equal_select_text',
        }),
        value: 'EQUALS',
      },
    ],
    groupConditionType: [
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_userCondition_select_text',
        }),
        value: 'USER_CONDITION',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_deviceCondition_select_text',
        }),
        value: 'DEVICE_CONDITION',
      },
    ],
    userConditionContent: [
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_rnVersion_select_text',
        }),
        value: 'RN_VERSION',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_userId_select_text',
        }),
        value: 'USER_ID',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_phoneOsVersion_select_text',
        }),
        value: 'PHONE_OS_VERSION',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_phoneModel_select_text',
        }),
        value: 'PHONE_MODEL',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_appVersion_select_text',
        }),
        value: 'APP_VERSION',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_businessType_select_text',
        }),
        value: 'BUSINESS_TYPE',
      },
    ],
    deviceConditionContent: [
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_firmwareVersion_select_text',
        }),
        value: 'FIRMWARE_VERSION',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_productModel_select_text',
        }),
        value: 'PRODUCT_MODEL',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_deviceId_select_text',
        }),
        value: 'DEVICE_ID',
      },
    ],
    businessType: [
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_EGOConnect_select_text',
        }),
        value: '1',
      },
      {
        label: intl.formatMessage({
          id: 'webOperation_dictionary_fleetService_select_text',
        }),
        value: '2',
      },
    ],
  };

  // 根据分组条件内容，获取相应分组判断规则。为了动态扩展，采用字典存储了条件和规则的对应关系。
  // 版本条件内容对应版本判断规则，可等条件内容对应可等判断规则
  const getConditionRules = async (param: { [key: string]: any }) => {
    const values = Object.values(omit(param, 'keyWords'));
    if (values.length === 0) {
      return [];
    }

    const value = values.join('');
    const groupConditionsByVersion = map(dict['groupConditionsByVersion'], (item) => item.value);
    const groupConditionsByEquals = map(dict['groupConditionsByEquals'], (item) => item.value);
    const groupConditionsByEqualsAndNot = map(
      dict['groupConditionsByEqualsAndNot'],
      (item) => item.value,
    );
    if (includes(groupConditionsByVersion, value)) {
      return dict['groupRulesByVersion'];
    }
    if (includes(groupConditionsByEquals, value)) {
      return dict['groupRulesByEquals'];
    }
    if (includes(groupConditionsByEqualsAndNot, value)) {
      return dict['groupRulesByEqualsAndNot'];
    }
    return [];
  };
  // 联动切换时，清空下级已选数据
  const onChange = (changeValues: Partial<API.GroupCondition>) => {
    if (has(changeValues, 'conditionType')) {
      formRef.current?.setFieldsValue({ conditionContent: undefined });
      formRef.current?.setFieldsValue({ conditionRule: undefined });
      formRef.current?.setFieldsValue({ conditionValue: undefined });
    }
    if (has(changeValues, 'conditionContent')) {
      formRef.current?.setFieldsValue({ conditionRule: undefined });
      formRef.current?.setFieldsValue({ conditionValue: undefined });
    }
    if (has(changeValues, 'conditionRule')) {
      formRef.current?.setFieldsValue({ conditionValue: undefined });
    }
  };
  return (
    <>
      <FormContainer
        title={
          initialValues.conditionType
            ? intl.formatMessage({ id: 'webOperation_group_conditionsEdit_button_text' })
            : intl.formatMessage({ id: 'webOperation_group_conditionsAdd_button_text' })
        }
        width="40%"
        onCancel={onClose}
        onConfirm={() => formRef.current?.submit()}
        open={visible}
        destroyOnClose={true}
        loading={loading}
      >
        <ProForm
          initialValues={initialValues}
          onFinish={handleSubmit}
          formRef={formRef}
          labelCol={{ flex: '120px' }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
          onValuesChange={onChange}
        >
          <ProFormSelect
            name="conditionType"
            label={intl.formatMessage({ id: 'webOperation_group_conditionType_tableColumn_text' })}
            rules={[
              {
                required: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_group_conditionType_tableColumn_text' }),
              },
            ]}
            placeholder={
              intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
              intl.formatMessage({ id: 'webOperation_group_conditionType_tableColumn_text' })
            }
            options={dict['groupConditionType']}
          />
          <ProFormDependency name={['conditionType']}>
            {({ conditionType }) => {
              let content: API.selectOptions[] = [];
              if (conditionType === 'USER_CONDITION') {
                content = dict['userConditionContent'];
              } else if (conditionType === 'DEVICE_CONDITION') {
                content = dict['deviceConditionContent'];
              }
              return (
                <ProFormSelect
                  name="conditionContent"
                  label={intl.formatMessage({
                    id: 'webOperation_group_conditionContent_tableColumn_text',
                  })}
                  rules={[
                    {
                      required: true,
                      message:
                        intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                        intl.formatMessage({
                          id: 'webOperation_group_conditionContent_tableColumn_text',
                        }),
                    },
                  ]}
                  placeholder={
                    intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                    intl.formatMessage({
                      id: 'webOperation_group_conditionContent_tableColumn_text',
                    })
                  }
                  options={content}
                />
              );
            }}
          </ProFormDependency>
          <ProFormSelect
            dependencies={['conditionContent']}
            name="conditionRule"
            label={intl.formatMessage({ id: 'webOperation_group_conditionRule_tableColumn_text' })}
            rules={[
              {
                required: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_group_conditionRule_tableColumn_text' }),
              },
            ]}
            placeholder={
              intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
              intl.formatMessage({ id: 'webOperation_group_conditionRule_tableColumn_text' })
            }
            request={getConditionRules}
          />
          <ProFormDependency name={['conditionContent']}>
            {({ conditionContent }) => {
              if (conditionContent === 'BUSINESS_TYPE') {
                return (
                  <ProFormCheckbox.Group
                    name="conditionValue"
                    label={intl.formatMessage({
                      id: 'webOperation_group_conditionValue_tableColumn_text',
                    })}
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message:
                          intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                          intl.formatMessage({
                            id: 'webOperation_group_conditionValue_tableColumn_text',
                          }),
                      },
                    ]}
                    options={dict!['businessType']}
                  />
                );
              } else
                return (
                  <ProFormText
                    name="conditionValue"
                    label={intl.formatMessage({
                      id: 'webOperation_group_conditionValue_tableColumn_text',
                    })}
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message:
                          intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                          intl.formatMessage({
                            id: 'webOperation_group_conditionValue_tableColumn_text',
                          }),
                      },
                      {
                        validator: (_, value) => {
                          if (formRef.current?.getFieldValue('conditionRule') === 'INCLUDE') {
                            if (value && value.indexOf('，') > -1) {
                              return Promise.reject(
                                new Error(
                                  intl.formatMessage({
                                    id: 'webCommon_page_comma_validator_message',
                                  }),
                                ),
                              );
                            } else {
                              // const arr = split(value, ',');
                              // if (isArray(arr) && arr.length > 5) {
                              //   return Promise.reject(
                              //     new Error(
                              //       intl.formatMessage({
                              //         id: 'webOperation_group_conditionValue_validator_message',
                              //       }),
                              //     ),
                              //   );
                              // }
                            }
                          }
                          return Promise.resolve();
                        },
                      },
                      // len128,
                    ]}
                    placeholder={
                      intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                      intl.formatMessage({
                        id: 'webOperation_group_conditionValue_tableColumn_text',
                      })
                    }
                  />
                );
            }}
          </ProFormDependency>
        </ProForm>
      </FormContainer>
      <Access accessible={access.canCreateGroupCondition()}>
        <Button
          style={{ float: 'right', border: 0, marginTop: '10px' }}
          type="primary"
          onClick={onCreate}
        >
          <FormattedMessage id="webOperation_group_conditionsAdd_button_text" />
        </Button>
      </Access>
    </>
  );
});

export default ConditionsAdd;
