import React, { useImperativeHandle, useState } from 'react';
import { useIntl } from 'umi';
import type { ProColumns } from '@ant-design/pro-components';
import { ProForm } from '@ant-design/pro-components';
import ResizableTable from '@/components/Table';
import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';
import { getDeviceGroupList, getUserGroupList } from '@/services/group';
import { typeColumns, useColumn } from '@/hooks/column';
import { Tooltip } from 'antd';

export interface RefType<T = any> {
  view: (name: string, type: string) => void;
}

const GroupResult = React.forwardRef<RefType>((props, ref) => {
  const [visible, setVisible] = useState<boolean>(false);
  const intl = useIntl();
  const { createColumns } = useColumn();
  const [initialValues, setInitialValues] = useState<API.GroupItem>({} as any);
  const [groupType, setGroupType] = useState<string>('USER_GROUP');
  const [columns, setColumns] = useState<ProColumns<any>[]>([]);
  const [pageConfig, setPageConfig] = useState({} as any);

  const deviceColumns: typeColumns[] = [
    {
      dataIndex: 'deviceId',
      width: 150,
      hideInSearch: true,
    },
    {
      dataIndex: 'productModel',
      width: 120,
      hideInSearch: true,
    },
    {
      dataIndex: 'userIds',
      width: 170,
      hideInSearch: true,
      ellipsis: {
        showTitle: false,
      },
      render: (_, record) => (
        <Tooltip placement="topLeft" title={record.userIds?.join('，')}>
          {record.userIds?.join('，')}
        </Tooltip>
      ),
    },
    {
      dataIndex: 'onlineStatus',
      width: 80,
      hideInSearch: true,
      valueEnum: {
        ONLINE: {
          text: intl.formatMessage({
            id: 'webOperation_dictionary_online_select_text',
          }),
          status: 'Success',
        },
        OFFLINE: {
          text: intl.formatMessage({
            id: 'webOperation_dictionary_offline_select_text',
          }),
          status: 'Default',
        },
      },
    },
  ];
  const userColumns: typeColumns[] = [
    {
      dataIndex: 'userId',
      width: 150,
      langCode: 'webOperation_user_userId_tableColumn_text',
      hideInSearch: true,
    },
    {
      dataIndex: 'deviceId',
      langCode: 'webOperation_user_bindDeviceId_tableColumn_text',
      width: 170,
      hideInSearch: true,
    },
    {
      dataIndex: 'productModel',
      width: 120,
      hideInSearch: true,
    },
    {
      dataIndex: 'onlineStatus',
      width: 80,
      hideInSearch: true,
      valueEnum: {
        ONLINE: {
          text: intl.formatMessage({
            id: 'webOperation_dictionary_online_select_text',
          }),
          status: 'Success',
        },
        OFFLINE: {
          text: intl.formatMessage({
            id: 'webOperation_dictionary_offline_select_text',
          }),
          status: 'Default',
        },
      },
    },
  ];

  const groupInfo: typeColumns[] = [
    {
      dataIndex: 'groupName',
      width: 150,
      showInForm: true,
      readonly: true,
    },
    {
      dataIndex: 'groupType',
      width: 150,
      valueType: 'select',
      showInForm: true,
      readonly: true,
      valueEnum: {
        USER_GROUP: intl.formatMessage({
          id: 'webOperation_dictionary_userGroup_select_text',
        }),
        DEVICE_GROUP: intl.formatMessage({
          id: 'webOperation_dictionary_deviceGroup_select_text',
        }),
      },
    },
  ];

  useImperativeHandle(ref, () => ({
    view: (name: string, type: string) => {
      // 设备分组结果
      if (type === 'DEVICE_GROUP') {
        const initColumns = createColumns('groupresult', deviceColumns);
        setColumns(initColumns);
        setPageConfig({ pagination: false, params: { groupName: name, maxResults: 20 } });
      } else {
        // 用户分组结果
        const initColumns = createColumns('groupresult', userColumns);
        setColumns(initColumns);
        setPageConfig({ params: { groupName: name } });
      }
      setInitialValues({ groupName: name, groupType: type });
      setGroupType(type);
      setVisible(true);
    },
  }));

  const onClose = () => {
    setVisible(false);
  };

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: 'webOperation_group_result_tableButton_text' })}
        width="50%"
        onCancel={onClose}
        open={visible}
        hiddenConfirm={true}
        destroyOnClose={true}
      >
        <ProForm
          initialValues={initialValues}
          labelCol={{ span: 4 }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
        >
          <FormFields columns={groupInfo} pageName="group" />
        </ProForm>
        <ResizableTable<API.GroupResult, API.GroupResultPageParams>
          rowKey={(record) => record.deviceId}
          defaultSize="small"
          {...pageConfig}
          search={false}
          options={false}
          request={groupType === 'USER_GROUP' ? getUserGroupList : getDeviceGroupList}
          columns={columns}
        />
      </FormContainer>
    </>
  );
});

export default GroupResult;
