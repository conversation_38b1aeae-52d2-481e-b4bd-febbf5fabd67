import React, { useImperativeHandle, useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { Button, message, Space, Divider } from 'antd';
import {
  ProForm,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormFieldSet,
} from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { PlusOutlined } from '@ant-design/icons';
import { map } from 'lodash-es';
import ResizableTable from '@/components/Table';

import FormContainer from '@/components/Form/FormContainer';
import ConditionsAdd, { RefType as AddRefType } from '@/pages/group/ConditionsAdd';
import { createGroup, updateGroup } from '@/services/group';
import { len500 } from '@/utils/validate';
import { typeColumns, useColumn } from '@/hooks/column';

export interface GroupEditProps {
  refresh?: () => Promise<void> | undefined;
}

export interface RefType {
  edit: (item: API.GroupItem, action: string) => void;
}

const GroupEdit = React.forwardRef<RefType, GroupEditProps>((props, ref) => {
  const { refresh } = props;
  const formRef = useRef<ProFormInstance<API.GroupItem>>();
  const addRef = useRef<AddRefType>(null);
  const [visible, setVisible] = useState<boolean>(false);
  const [conditions, setConditions] = useState<API.GroupCondition[]>([]);
  const [initialValues, setInitialValues] = useState<API.GroupItem>({} as any);
  const [showConfirm, setShowConfirm] = useState({} as any);
  const [mode, setMode] = useState<string>('create');
  const access = useAccess();
  const intl = useIntl();
  const { createColumns } = useColumn();
  const [columns, setColumns] = useState<ProColumns<any>[]>([]);

  const { run: handleSubmit, loading } = useRequest(
    async (values) => {
      if (initialValues.id) {
        await updateGroup(values);
      } else {
        await createGroup(values);
      }
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setVisible(false);
      refresh?.();
    },
    { manual: true },
  );

  const editCondition = (value: API.GroupCondition, index = -1) => {
    const conditionsValue = formRef.current?.getFieldValue('items') || [];
    // 编辑分组条件
    if (index > -1) {
      conditionsValue[index] = value;
      // 添加分组条件
    } else {
      conditionsValue.unshift(value);
    }
    setConditions(conditionsValue);
    formRef.current?.setFieldsValue({ items: conditionsValue });
  };

  const deleteCondition = (index: number) => {
    const conditionsValue = formRef.current?.getFieldValue('items') || [];
    // 删除分组条件
    conditionsValue.splice(index, 1);
    setConditions(conditionsValue);
    formRef.current?.setFieldsValue({ items: conditionsValue });
  };

  const onClose = () => {
    setVisible(false);
  };
  const actionColumn: ProColumns = {
    title: intl.formatMessage({ id: 'webCommon_page_option_tableColumn_text' }),
    dataIndex: 'option',
    valueType: 'option',
    render: (_, record, index) => [
      <Space key="option" split={<Divider type="vertical" />}>
        <Access accessible={access.canEditGroupCondition()}>
          <a
            key="edit"
            onClick={() => {
              addRef.current?.edit(record, index);
            }}
          >
            <FormattedMessage id="webCommon_page_edit_common_text" />
          </a>
        </Access>
        <Access accessible={access.canDeleteGroupCondition()}>
          <a
            key="delete"
            onClick={() => {
              deleteCondition(index);
            }}
          >
            <FormattedMessage id="webCommon_page_delete_tableButton_linkText" />
          </a>
        </Access>
      </Space>,
    ],
  };
  const listColumns: typeColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'index',
      langCode: 'webCommon_page_index_tableColumn_text',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      dataIndex: 'conditionType',
      width: 120,
      hideInSearch: true,
      hideInTable: true,
    },
    {
      dataIndex: 'conditionContent',
      width: 120,
      hideInSearch: true,
      valueEnum: {
        RN_VERSION: intl.formatMessage({
          id: 'webOperation_dictionary_rnVersion_select_text',
        }),
        USER_ID: intl.formatMessage({
          id: 'webOperation_dictionary_userId_select_text',
        }),
        PHONE_OS_VERSION: intl.formatMessage({
          id: 'webOperation_dictionary_phoneOsVersion_select_text',
        }),
        PHONE_MODEL: intl.formatMessage({
          id: 'webOperation_dictionary_phoneModel_select_text',
        }),
        APP_VERSION: intl.formatMessage({
          id: 'webOperation_dictionary_appVersion_select_text',
        }),
        BUSINESS_TYPE: intl.formatMessage({
          id: 'webOperation_dictionary_businessType_select_text',
        }),
        FIRMWARE_VERSION: intl.formatMessage({
          id: 'webOperation_dictionary_firmwareVersion_select_text',
        }),
        PRODUCT_MODEL: intl.formatMessage({
          id: 'webOperation_dictionary_productModel_select_text',
        }),
        DEVICE_ID: intl.formatMessage({
          id: 'webOperation_dictionary_deviceId_select_text',
        }),
      },
    },
    {
      dataIndex: 'conditionRule',
      width: 110,
      hideInSearch: true,
      valueEnum: {
        GRATER_THAN: intl.formatMessage({
          id: 'webOperation_dictionary_graterThan_select_text',
        }),
        EQUALS: intl.formatMessage({ id: 'webOperation_dictionary_equals_select_text' }),
        EXCLUDE: intl.formatMessage({ id: 'webOperation_dictionary_exclude_select_text' }),
        LESS_THAN_EQUAL: intl.formatMessage({
          id: 'webOperation_dictionary_lessThanEqual_select_text',
        }),
        GRATER_THAN_EQUAL: intl.formatMessage({
          id: 'webOperation_dictionary_graterThanEqual_select_text',
        }),
        INCLUDE: intl.formatMessage({ id: 'webOperation_dictionary_include_select_text' }),
        LESS_THAN: intl.formatMessage({ id: 'webOperation_dictionary_lessThan_select_text' }),
      },
    },
    {
      dataIndex: 'conditionValue',
      width: 120,
    },
  ];
  useImperativeHandle(ref, () => ({
    edit: (item: API.GroupItem, action: string) => {
      const initColumns = createColumns('group', listColumns);
      if (action === 'edit') {
        initColumns.push(actionColumn);
        setShowConfirm({ onConfirm: () => formRef.current?.submit() });
      } else {
        setShowConfirm({ hiddenConfirm: true });
      }
      setColumns(initColumns);
      setMode(action);
      const items: API.GroupCondition[] = [];
      map(item.conditionContents, (value, index) => {
        items.push({
          conditionContent: value,
          conditionRule: item.conditionRules![index],
          conditionValue: item.conditionValues![index],
          conditionType: item.conditionTypes![index],
        });
      });
      setConditions(items || []);
      item.items = items;
      setInitialValues(item || {});
      setVisible(true);
    },
  }));

  const onCreate = () => {
    const initColumns = [...listColumns, actionColumn];
    const allColumns = createColumns('group', initColumns);
    setColumns(allColumns);
    setMode('create');
    setShowConfirm({ onConfirm: () => formRef.current?.submit() });
    setInitialValues({} as any);
    setConditions([]);
    setVisible(true);
  };

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: `webOperation_group_${mode}_button_text` })}
        width="50%"
        onCancel={onClose}
        {...showConfirm}
        open={visible}
        destroyOnClose={true}
        loading={loading}
      >
        <ProForm
          initialValues={initialValues}
          onFinish={handleSubmit}
          formRef={formRef}
          labelCol={{ flex: '120px' }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
        >
          <ProFormText
            name="groupName"
            label={intl.formatMessage({ id: 'webOperation_group_groupName_tableColumn_text' })}
            rules={[
              {
                required: true,
                whitespace: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_group_groupName_tableColumn_text' }),
              },
              {
                pattern: /^[A-Za-z0-9_]{1,128}$/,
                message: intl.formatMessage({ id: 'webCommon_page_en128_validator_message' }),
              },
            ]}
            placeholder={
              intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
              intl.formatMessage({ id: 'webOperation_group_groupName_tableColumn_text' })
            }
            readonly={mode === 'create' ? false : true}
          />
          <ProFormSelect
            name="groupType"
            label={intl.formatMessage({ id: 'webOperation_group_groupType_tableColumn_text' })}
            rules={[
              {
                required: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_group_groupType_tableColumn_text' }),
              },
            ]}
            placeholder={
              intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
              intl.formatMessage({ id: 'webOperation_group_groupType_tableColumn_text' })
            }
            valueEnum={{
              USER_GROUP: intl.formatMessage({
                id: 'webOperation_dictionary_userGroup_select_text',
              }),
              DEVICE_GROUP: intl.formatMessage({
                id: 'webOperation_dictionary_deviceGroup_select_text',
              }),
            }}
            readonly={mode === 'create' ? false : true}
          />
          <ProFormFieldSet
            name="items"
            type="group"
            label={intl.formatMessage({
              id: 'webOperation_group_conditionContents_tableColumn_text',
            })}
            rules={[
              {
                required: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_add_validator_message' }) +
                  intl.formatMessage({
                    id: 'webOperation_group_conditionContents_tableColumn_text',
                  }),
              },
            ]}
          >
            <ResizableTable<API.GroupCondition, API.PageParams>
              rowKey={(_, index: number | undefined) => String(index)}
              defaultSize="small"
              search={false}
              toolBarRender={false}
              columns={columns}
              pagination={false}
              dataSource={conditions}
            />
            {mode !== 'view' ? (
              <ConditionsAdd ref={addRef} key="create" editCondition={editCondition} />
            ) : (
              ''
            )}
          </ProFormFieldSet>
          <ProFormTextArea
            name="comments"
            label={intl.formatMessage({ id: 'webOperation_group_comments_tableColumn_text' })}
            placeholder={
              intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
              intl.formatMessage({ id: 'webOperation_group_comments_tableColumn_text' })
            }
            rules={[len500]}
            readonly={mode === 'view' ? true : false}
          />
        </ProForm>
      </FormContainer>
      <Access accessible={access.canCreateGroup()}>
        <Button type="primary" onClick={onCreate}>
          <PlusOutlined />
          <FormattedMessage id="webOperation_group_create_button_text" />
        </Button>
      </Access>
    </>
  );
});

export default GroupEdit;
