import React, { useRef, useState } from 'react';
import { Divider, Space, Modal, message, Tooltip } from 'antd';
import { FormattedMessage, useAccess } from 'umi';
import { Access } from '@@/plugin-access/access';
import { useIntl } from '@@/plugin-locale/localeExports';
import { useRequest } from '@@/plugin-request/request';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProForm, ProFormText, ProFormInstance } from '@ant-design/pro-components';
import ResizableTable, { beforeSearchSubmit } from '@/components/Table';
import KeepAlive from '@/components/KeepAlive';
import { dateFilter, extra, typeColumns, useColumn } from '@/hooks/column';
import { omit } from 'lodash-es';

import GroupEdit, { RefType as EditRefType } from '@/pages/group/GroupEdit';
import GroupResult, { RefType } from '@/pages/group/GroupResult';
import DeleteError, { RefType as errorRefType } from '@/pages/group/DeleteError';
import { getGroupList, deleteGroup } from '@/services/group';
import styles from './styles.less';

const GroupList: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const editRef = useRef<EditRefType>(null);
  const [groupName, setGroupName] = useState<string | undefined>();
  const resultRef = useRef<RefType>(null);
  const errorRef = useRef<errorRefType>(null);
  const intl = useIntl();
  const access = useAccess();
  const { createColumns } = useColumn();

  const { run: handleDelete, loading } = useRequest(
    async () => {
      if (!groupName) return;
      const res = await deleteGroup(groupName);
      if (res.data && res.data.length > 0) {
        errorRef.current?.open(res.data.join('\n'));
      } else {
        setGroupName(undefined);
        message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
        actionRef.current?.reload();
      }
    },
    { manual: true },
  );

  const actionColumn: ProColumns = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 260,
    fixed: 'right',
    render: (_, record) => [
      <Space key="option" split={<Divider type="vertical" />}>
        <Access accessible={access.canViewGroup()}>
          <a
            key="view"
            onClick={() => {
              editRef.current?.edit(record, 'view');
            }}
          >
            <FormattedMessage id="webCommon_page_view_common_text" />
          </a>
        </Access>
        <Access accessible={access.canEditGroup()}>
          <a
            key="update"
            onClick={() => {
              editRef.current?.edit(record, 'edit');
            }}
          >
            <FormattedMessage id="webCommon_page_edit_common_text" />
          </a>
        </Access>
        <Access accessible={access.canDeleteGroup()}>
          <a
            key="delete"
            onClick={() => {
              setGroupName(record.groupName);
            }}
          >
            <FormattedMessage id="webCommon_page_delete_tableButton_linkText" />
          </a>
        </Access>
        <Access accessible={access.canViewGroupResult()}>
          <a
            key="result"
            onClick={() => {
              resultRef.current?.view(record.groupName, record.groupType);
            }}
          >
            <FormattedMessage id="webOperation_group_result_tableButton_text" />
          </a>
        </Access>
      </Space>,
    ],
  };
  const listColumns: typeColumns[] = [
    ...dateFilter,
    {
      dataIndex: 'groupName',
      width: 150,
    },
    {
      dataIndex: 'groupType',
      width: 150,
      valueEnum: {
        USER_GROUP: intl.formatMessage({
          id: 'webOperation_dictionary_userGroup_select_text',
        }),
        DEVICE_GROUP: intl.formatMessage({
          id: 'webOperation_dictionary_deviceGroup_select_text',
        }),
      },
    },
    {
      dataIndex: 'conditionContents',
      width: 150,
      isRender: true,
      valueEnum: {
        RN_VERSION: intl.formatMessage({
          id: 'webOperation_dictionary_rnVersion_select_text',
        }),
        USER_ID: intl.formatMessage({
          id: 'webOperation_dictionary_userId_select_text',
        }),
        PHONE_OS_VERSION: intl.formatMessage({
          id: 'webOperation_dictionary_phoneOsVersion_select_text',
        }),
        PHONE_MODEL: intl.formatMessage({
          id: 'webOperation_dictionary_phoneModel_select_text',
        }),
        APP_VERSION: intl.formatMessage({
          id: 'webOperation_dictionary_appVersion_select_text',
        }),
        BUSINESS_TYPE: intl.formatMessage({
          id: 'webOperation_dictionary_businessType_select_text',
        }),
        FIRMWARE_VERSION: intl.formatMessage({
          id: 'webOperation_dictionary_firmwareVersion_select_text',
        }),
        PRODUCT_MODEL: intl.formatMessage({
          id: 'webOperation_dictionary_productModel_select_text',
        }),
        DEVICE_ID: intl.formatMessage({
          id: 'webOperation_dictionary_deviceId_select_text',
        }),
      },
    },
    {
      dataIndex: 'conditionRules',
      width: 150,
      isRender: true,
      hideInSearch: true,
      valueEnum: {
        GRATER_THAN: intl.formatMessage({
          id: 'webOperation_dictionary_graterThan_select_text',
        }),
        EQUALS: intl.formatMessage({ id: 'webOperation_dictionary_equals_select_text' }),
        EXCLUDE: intl.formatMessage({ id: 'webOperation_dictionary_exclude_select_text' }),
        LESS_THAN_EQUAL: intl.formatMessage({
          id: 'webOperation_dictionary_lessThanEqual_select_text',
        }),
        GRATER_THAN_EQUAL: intl.formatMessage({
          id: 'webOperation_dictionary_graterThanEqual_select_text',
        }),
        INCLUDE: intl.formatMessage({ id: 'webOperation_dictionary_include_select_text' }),
        LESS_THAN: intl.formatMessage({ id: 'webOperation_dictionary_lessThan_select_text' }),
      },
    },
    {
      dataIndex: 'conditionRule',
      width: 150,
      hideInTable: true,
      valueEnum: {
        GRATER_THAN: intl.formatMessage({
          id: 'webOperation_dictionary_graterThan_select_text',
        }),
        EQUALS: intl.formatMessage({ id: 'webOperation_dictionary_equals_select_text' }),
        EXCLUDE: intl.formatMessage({ id: 'webOperation_dictionary_exclude_select_text' }),
        LESS_THAN_EQUAL: intl.formatMessage({
          id: 'webOperation_dictionary_lessThanEqual_select_text',
        }),
        GRATER_THAN_EQUAL: intl.formatMessage({
          id: 'webOperation_dictionary_graterThanEqual_select_text',
        }),
        INCLUDE: intl.formatMessage({ id: 'webOperation_dictionary_include_select_text' }),
        LESS_THAN: intl.formatMessage({ id: 'webOperation_dictionary_lessThan_select_text' }),
      },
    },
    {
      dataIndex: 'conditionValues',
      width: 150,
      ellipsis: {
        showTitle: false,
      },
      render: (_, record) => (
        <Tooltip placement="topLeft" title={record.conditionValues.join('，')}>
          {record.conditionValues.join('，')}
        </Tooltip>
      ),
    },
    ...extra,
    {
      dataIndex: 'comments',
      width: 150,
      hideInSearch: true,
    },
  ];
  const initColumns = [...listColumns, actionColumn];

  const allColumns = createColumns('group', initColumns);

  const onCancel = () => {
    setGroupName(undefined);
  };

  return (
    <>
      <ResizableTable<API.GroupItem, API.GroupPageParams>
        actionRef={actionRef}
        rowKey="id"
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        headerTitle={
          <GroupEdit ref={editRef} key="create" refresh={() => actionRef.current?.reload()} />
        }
        scroll={{ x: 1300 }}
        request={getGroupList}
        columns={allColumns}
        beforeSearchSubmit={(params) => {
          return {
            ...omit(beforeSearchSubmit(params), ['conditionContents', 'conditionValues']),
            content: params.conditionContents,
            value: params.conditionValues,
          };
        }}
      />
      <Modal
        title={<FormattedMessage id="webCommon_page_confirmToDelete_modal_title" />}
        width="50%"
        onCancel={onCancel}
        onOk={() => formRef.current?.submit()}
        open={!!groupName}
        destroyOnClose={true}
        confirmLoading={loading}
      >
        <div className={styles.modalDeletePrompt}>
          <FormattedMessage id="webOperation_group_delete_modal_message" />
        </div>
        <ProForm layout="horizontal" submitter={false} onFinish={handleDelete} formRef={formRef}>
          <ProFormText
            name="confirm"
            rules={[
              {
                whitespace: true,
                required: true,
                type: 'enum',
                enum: ['Yes'],
                message: <FormattedMessage id="webOperation_group_delete_input_placeholder" />,
              },
            ]}
            placeholder={intl.formatMessage({
              id: 'webOperation_group_delete_input_placeholder',
            })}
          />
        </ProForm>
      </Modal>
      <GroupResult ref={resultRef} key="result" />
      <DeleteError ref={errorRef} key="error" />
    </>
  );
};

export default () => {
  return (
    <KeepAlive>
      <GroupList />
    </KeepAlive>
  );
};
