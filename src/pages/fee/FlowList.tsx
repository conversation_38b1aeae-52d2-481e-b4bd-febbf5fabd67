import React, { useRef } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Divider, Space, Button } from 'antd';
import { Access } from '@@/plugin-access/access';
import { ProFormInstance } from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import ResizableTable from '@/components/Table';
import { createKAC } from '@/components/KeepAlive';

import { getFlowList, exportFlowList } from '@/services/fee';
import FlowDetail, { RefType } from './FlowDetail';
import { listColumns } from './columns/FlowList';
import { useColumn } from '@/hooks/column';
import useExport from '@/hooks/useExport';

const FlowList: React.FC = () => {
  const detailRef = useRef<RefType>(null);
  const formRef = useRef<ProFormInstance>();
  const { createColumns } = useColumn();
  const access = useAccess();
  const intl = useIntl();
  const { run: handleExport, loading: exportLoading } = useExport(
    exportFlowList,
    formRef,
    intl.formatMessage({ id: 'webOperation_sms_export_fileName_text' }),
  );
  const actionColumn: ProColumns = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 150,
    fixed: 'right',
    render: (_, record) => [
      <Space key="detail" split={<Divider type="vertical" />}>
        <Access accessible={access.canViewFlowDetail()}>
          <a
            key="view"
            onClick={() => {
              detailRef.current?.show(
                record.chargingId,
                formRef.current?.getFieldFormatValue?.('chargingStartTime')
                  ? dayjs(formRef.current?.getFieldFormatValue?.('chargingStartTime')).format(
                      'YYYY-MM-DD',
                    )
                  : undefined,
                formRef.current?.getFieldFormatValue?.('chargingEndTime')
                  ? dayjs(formRef.current?.getFieldFormatValue?.('chargingEndTime')).format(
                      'YYYY-MM-DD',
                    )
                  : undefined,
              );
            }}
          >
            <FormattedMessage id="webCommon_page_detail_common_text" />
          </a>
        </Access>
      </Space>,
    ],
  };

  const initColumns = [...listColumns, actionColumn];
  const allColumns = createColumns('flow', initColumns);

  return (
    <>
      <ResizableTable<API.FeeSms, API.FeeSmsPageParams>
        formRef={formRef}
        rowKey="chargingId"
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        headerTitle={
          <Access accessible={access.canExportFlow()}>
            <Button loading={exportLoading} type="primary" onClick={handleExport}>
              <FormattedMessage id="webCommon_page_export_button_text" />
            </Button>
          </Access>
        }
        scroll={{ x: 1300 }}
        request={getFlowList}
        columns={allColumns}
      />
      <FlowDetail ref={detailRef} key="detail" />
    </>
  );
};
export default createKAC(FlowList);
