import type { typeColumns } from '@/hooks/column';
import { FormattedMessage } from 'umi';
export const listColumns: typeColumns[] = [
  {
    dataIndex: 'chargingId',
    width: 160,
    order: 7,
  },
  {
    dataIndex: 'msgId',
    width: 160,
    order: 6,
  },
  {
    dataIndex: 'msgTitle',
    width: 300,
    order: 5,
  },
  {
    dataIndex: 'chargingStartTime',
    width: 150,
    valueType: 'dateTime',
    showLabel: true,
    order: 2,
  },
  {
    dataIndex: 'chargingEndTime',
    width: 150,
    valueType: 'dateTime',
    showLabel: true,
    order: 1,
  },
  {
    dataIndex: 'msgType',
    width: 150,
    order: 4,
    valueEnum: {
      0: <FormattedMessage id="webOperation_dictionary_systemMessage_select_text" />,
      1: <FormattedMessage id="webOperation_dictionary_marketingMessage_select_text" />,
      2: <FormattedMessage id="webOperation_dictionary_deviceMessage_select_text" />,
    },
  },
  {
    dataIndex: 'commodityModel',
    width: 150,
    order: 3,
  },
  {
    dataIndex: 'totalSend',
    width: 150,
    hideInSearch: true,
  },
  {
    dataIndex: 'createTime',
    width: 150,
    valueType: 'dateTime',
    hideInSearch: true,
  },
];

export const detailColumns: typeColumns[] = [
  {
    dataIndex: 'msgRecordId',
    width: 170,
  },
  {
    dataIndex: 'msgType',
    width: 150,
    valueEnum: {
      0: <FormattedMessage id="webOperation_dictionary_systemMessage_select_text" />,
      1: <FormattedMessage id="webOperation_dictionary_marketingMessage_select_text" />,
      2: <FormattedMessage id="webOperation_dictionary_deviceMessage_select_text" />,
    },
  },
  {
    dataIndex: 'msgTitle',
    width: 150,
  },
  {
    dataIndex: 'msgContent',
    width: 150,
    hideInSearch: true,
  },
  {
    dataIndex: 'pushType',
    width: 150,
    hideInSearch: true,
    valueEnum: {
      POPUP: <FormattedMessage id="webOperation_dictionary_popup_select_text" />,
      MESSAGE: <FormattedMessage id="webOperation_dictionary_message_select_text" />,
      BANNER: <FormattedMessage id="webOperation_dictionary_banner_select_text" />,
      MAIL: <FormattedMessage id="webOperation_dictionary_mail_select_text" />,
      TOMBSTONE: <FormattedMessage id="webOperation_dictionary_tombstone_select_text" />,
      PHONE_VOICE: <FormattedMessage id="webOperation_dictionary_phoneVoice_select_text" />,
    },
  },
  {
    dataIndex: 'toUserId',
    width: 170,
  },
  {
    dataIndex: 'toUserPhoneNumber',
    width: 150,
  },
  {
    dataIndex: 'toUserRegion',
    width: 150,
  },
  {
    dataIndex: 'pushTime',
    width: 150,
    valueType: 'dateTime',
    hideInSearch: true,
  },
  {
    dataIndex: 'pushStartTime',
    valueType: 'dateTime',
    hideInTable: true,
    showLabel: true,
  },
  {
    dataIndex: 'pushEndTime',
    valueType: 'dateTime',
    hideInTable: true,
    showLabel: true,
  },
];
