import type { typeColumns } from '@/hooks/column';

export const listColumns: typeColumns[] = [
  {
    dataIndex: 'chargingId',
    width: 160,
    order: 4,
    langCode: 'webOperation_sms_chargingId_tableColumn_text',
  },
  {
    dataIndex: 'commodityModel',
    width: 160,
    order: 3,
    langCode: 'webOperation_sms_commodityModel_tableColumn_text',
  },
  {
    dataIndex: 'chargingStartTime',
    width: 150,
    valueType: 'date',
    showLabel: true,
    order: 2,
  },
  {
    dataIndex: 'chargingEndTime',
    width: 150,
    valueType: 'date',
    showLabel: true,
    order: 1,
  },
  {
    dataIndex: 'deviceCount',
    width: 150,
    hideInSearch: true,
  },
  // {
  //   dataIndex: 'totalPriceCny',
  //   width: 150,
  //   hideInSearch: true,
  //   hideInTable: lang === 'zh-CN' ? false : true,
  //   langCode: 'webOperation_flow_totalPrice_tableColumn_text',
  // },
  // {
  //   dataIndex: 'totalPriceUsd',
  //   width: 150,
  //   hideInSearch: true,
  //   hideInTable: lang === 'zh-CN' ? true : false,
  //   langCode: 'webOperation_flow_totalPrice_tableColumn_text',
  // },
  {
    dataIndex: 'totalFlow',
    width: 150,
    hideInSearch: true,
  },
  {
    dataIndex: 'createTime',
    width: 150,
    valueType: 'dateTime',
    hideInSearch: true,
    langCode: 'webCommon_page_createTime_tableColumn_text',
  },
];

export const firstColumns: typeColumns[] = [
  {
    dataIndex: 'deviceId',
    width: 170,
    order: 4,
  },
  {
    dataIndex: 'iccid',
    width: 150,
    order: 3,
  },
];
export const thirdColumns: typeColumns[] = [
  // {
  //   dataIndex: 'totalPriceCny',
  //   width: 150,
  //   hideInSearch: true,
  //   langCode: 'webOperation_flow_totalDevicePrice_tableColumn_text',
  //   hideInTable: lang === 'zh-CN' ? false : true,
  // },
  // {
  //   dataIndex: 'totalPriceUsd',
  //   width: 150,
  //   hideInSearch: true,
  //   langCode: 'webOperation_flow_totalDevicePrice_tableColumn_text',
  //   hideInTable: lang === 'zh-CN' ? true : false,
  // },
  {
    dataIndex: 'totalFlow',
    width: 200,
    hideInSearch: true,
    langCode: 'webOperation_flow_singleTotalFlow_tableColumn_text',
  },
];
