import React, { useState, useImperativeHandle, useRef, useEffect } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Access } from '@@/plugin-access/access';
import { ProFormInstance } from '@ant-design/pro-components';
import FormContainer from '@/components/Form/FormContainer';
import ResizableTable from '@/components/Table';
import { useColumn } from '@/hooks/column';
import { Button } from 'antd';
import { getFlowDetails, exportFlowDetails } from '@/services/fee';
import useExport from '@/hooks/useExport';
import { firstColumns, thirdColumns } from './columns/FlowList';
import type { typeColumns } from '@/hooks/column';
export interface RefType {
  show: (id: string, chargingStart: string | undefined, chargingEnd: string | undefined) => void;
}

const FlowDetail = React.forwardRef<RefType, APP.EditFormProps>((props, ref) => {
  const { createColumns } = useColumn();
  const [visible, setVisible] = useState<boolean>(false);
  const [chargingId, setChargingId] = useState<string>();
  const [chargingStartTime, setChargingStartTime] = useState<string>();
  const [chargingEndTime, setChargingEndTime] = useState<string>();
  const formRef = useRef<ProFormInstance>();
  const access = useAccess();
  const intl = useIntl();
  const { run: handleExport, loading: exportLoading } = useExport(
    exportFlowDetails,
    formRef,
    intl.formatMessage({ id: 'webOperation_sms_export_fileName_text' }),
    { chargingId },
  );
  const onClose = () => {
    setVisible(false);
  };
  useImperativeHandle(ref, () => ({
    show: (id: string, chargingStart: string | undefined, chargingEnd: string | undefined) => {
      setChargingId(id);
      setChargingStartTime(chargingStart);
      setChargingEndTime(chargingEnd);
      setVisible(true);
    },
  }));
  useEffect(() => {
    formRef.current?.setFieldsValue({
      chargingStartTime: chargingStartTime,
    });
    formRef.current?.setFieldsValue({
      chargingEndTime: chargingEndTime,
    });
    if (visible) {
      formRef.current?.submit();
    }
  }, [visible, chargingStartTime, chargingEndTime]);

  const secondColumns: typeColumns[] = [
    {
      dataIndex: 'chargingStartTime',
      width: 150,
      valueType: 'date',
      showLabel: true,
      order: 2,
    },
    {
      dataIndex: 'chargingEndTime',
      width: 150,
      valueType: 'date',
      showLabel: true,
      order: 1,
    },
  ];
  const allColumns = createColumns('flow', [...firstColumns, ...secondColumns, ...thirdColumns]);

  return (
    <FormContainer
      title={intl.formatMessage({ id: 'webOperation_flow_detail_modal_title' })}
      width="85%"
      onCancel={onClose}
      hiddenConfirm={true}
      open={visible}
      destroyOnClose={true}
    >
      <ResizableTable<API.FeeSms, API.FeeSmsPageParams>
        formRef={formRef}
        rowKey="deviceId"
        search={{
          labelWidth: 'auto',
        }}
        defaultSize="small"
        headerTitle={
          <Access accessible={access.canExportFlowDetails()}>
            <Button loading={exportLoading} type="primary" onClick={handleExport}>
              <FormattedMessage id="webCommon_page_export_button_text" />
            </Button>
          </Access>
        }
        scroll={{ x: 1300 }}
        params={{
          chargingId,
        }}
        request={getFlowDetails}
        columns={allColumns}
      />
    </FormContainer>
  );
});

export default FlowDetail;
