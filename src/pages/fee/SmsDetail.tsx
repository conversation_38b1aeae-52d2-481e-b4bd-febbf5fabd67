import React, { useState, useImperativeHandle, useRef } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Access } from '@@/plugin-access/access';
import { ProFormInstance } from '@ant-design/pro-components';
import FormContainer from '@/components/Form/FormContainer';
import ResizableTable from '@/components/Table';
import { useColumn } from '@/hooks/column';
import { Button } from 'antd';
import { getDetails, exportDetails } from '@/services/fee';
import useExport from '@/hooks/useExport';
import { detailColumns } from './columns/SmsList';

export interface RefType {
  show: (id: string, chargingStartTime: string, chargingEndTime: string) => void;
}

const SmsDetail = React.forwardRef<RefType>((props, ref) => {
  const { createColumns } = useColumn();
  const [visible, setVisible] = useState<boolean>(false);
  const [chargingId, setChargingId] = useState<string>();
  const [chargingStartTime, setChargingStartTime] = useState<string>();
  const [chargingEndTime, setChargingEndTime] = useState<string>();
  const formRef = useRef<ProFormInstance>();
  const access = useAccess();
  const intl = useIntl();
  const { run: handleExport, loading: exportLoading } = useExport(
    exportDetails,
    formRef,
    intl.formatMessage({ id: 'webOperation_sms_export_fileName_text' }),
    { chargingId, chargingStartTime, chargingEndTime },
  );
  const onClose = () => {
    setVisible(false);
  };
  useImperativeHandle(ref, () => ({
    show: (id: string, chargingStart: string, chargingEnd: string) => {
      setChargingId(id);
      setChargingStartTime(chargingStart);
      setChargingEndTime(chargingEnd);
      setVisible(true);
    },
  }));

  const allColumns = createColumns('sms', [...detailColumns]);

  return (
    <FormContainer
      title={intl.formatMessage({ id: 'webOperation_sms_detail_modal_title' })}
      width="85%"
      onCancel={onClose}
      hiddenConfirm={true}
      open={visible}
      destroyOnClose={true}
    >
      <ResizableTable<API.FeeSms, API.FeeSmsPageParams>
        formRef={formRef}
        rowKey="msgRecordId"
        search={{
          labelWidth: 'auto',
        }}
        defaultSize="small"
        headerTitle={
          <Access accessible={access.canExportSmsDetails()}>
            <Button loading={exportLoading} type="primary" onClick={handleExport}>
              <FormattedMessage id="webCommon_page_export_button_text" />
            </Button>
          </Access>
        }
        scroll={{ x: 1300 }}
        params={{ chargingId, chargingStartTime, chargingEndTime }}
        request={getDetails}
        columns={allColumns}
        beforeSearchSubmit={(params) => {
          return params;
        }}
      />
    </FormContainer>
  );
});

export default SmsDetail;
