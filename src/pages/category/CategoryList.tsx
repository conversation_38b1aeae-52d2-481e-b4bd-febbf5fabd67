import React, { useRef, useState } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Divider, Modal, Space, message } from 'antd';
import { useRequest } from '@@/plugin-request/request';
import { Access } from '@@/plugin-access/access';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import ResizableTable, { beforeSearchSubmit } from '@/components/Table';
import KeepAlive from '@/components/KeepAlive';
import DeleteResultModal, { RefType as ModalRefType } from '@/components/Modal/index';
import { showDeleteFailReason } from '@/hooks/action';
import { omit } from 'lodash-es';

import CategoryEdit, { RefType } from '@/pages/category/CategoryEdit';
import CategoryDetail, { RefType as DetailRef } from '@/pages/category/CategoryDetail';
import { deleteCategory, getList } from '@/services/category';
import { listColumns } from './columns/CategoryList';
import { useColumn } from '@/hooks/column';

const CategoryList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const editRef = useRef<RefType>(null);
  const viewRef = useRef<DetailRef>(null);
  const modalRef = useRef<ModalRefType>(null);
  const { createColumns } = useColumn();
  const intl = useIntl();
  const access = useAccess();
  const [categoryId, setCategoryId] = useState<string | undefined>();

  const { run: handleDelete, loading } = useRequest(
    async () => {
      if (!categoryId) return;
      try {
        await deleteCategory(categoryId);
        setCategoryId(undefined);
        message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
        actionRef.current?.reload();
      } catch (e: any) {
        setCategoryId(undefined);
        showDeleteFailReason(modalRef, '1090032003', e);
      }
    },
    { manual: true },
  );

  const actionColumn: ProColumns = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 200,
    fixed: 'right',
    render: (_, record) => [
      <Space key="edit" split={<Divider type="vertical" />}>
        <Access accessible={access.canViewCategory()}>
          <a
            key="view"
            onClick={() => {
              viewRef.current?.view(record);
            }}
          >
            <FormattedMessage id="webCommon_page_view_common_text" />
          </a>
        </Access>
        <Access accessible={access.canEditCategory()}>
          <a
            key="edit"
            onClick={() => {
              editRef.current?.edit(record);
            }}
          >
            <FormattedMessage id="webCommon_page_edit_common_text" />
          </a>
        </Access>
        <Access accessible={access.canDeleteCategory()}>
          <a
            key="delete"
            onClick={() => {
              setCategoryId(record.id);
            }}
          >
            <FormattedMessage id="webCommon_page_delete_tableButton_linkText" />
          </a>
        </Access>
      </Space>,
    ],
  };

  const initColumns = [...listColumns, actionColumn];
  const allColumns = createColumns('category', initColumns);

  return (
    <>
      <ResizableTable<API.CategoryItem, API.CategoryPageParams>
        actionRef={actionRef}
        rowKey="id"
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        headerTitle={
          <CategoryEdit
            ref={editRef}
            key="create"
            refresh={() => {
              return actionRef.current?.reload();
            }}
          />
        }
        scroll={{ x: 1300 }}
        request={getList}
        columns={allColumns}
        beforeSearchSubmit={(params) => {
          return {
            ...omit(beforeSearchSubmit(params), ['categoryName']),
            name: params.categoryName,
          };
        }}
      />
      <CategoryDetail ref={viewRef} key="view" />
      <Modal
        title={intl.formatMessage({ id: 'webCommon_page_confirmToDelete_modal_title' })}
        open={!!categoryId}
        onOk={handleDelete}
        onCancel={() => setCategoryId(undefined)}
        confirmLoading={loading}
      >
        <FormattedMessage id="webOperation_category_delete_modal_message" />
      </Modal>
      <DeleteResultModal ref={modalRef} />
    </>
  );
};
export default () => {
  return (
    <KeepAlive>
      <CategoryList />
    </KeepAlive>
  );
};
