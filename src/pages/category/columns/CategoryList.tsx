import type { typeColumns } from '@/hooks/column';
import { extra, dateFilter } from '@/hooks/column';
import { Image } from 'antd';

export const listColumns: typeColumns[] = [
  ...dateFilter,
  {
    dataIndex: 'id',
    width: 160,
  },
  {
    dataIndex: ['categoryName', 'message'],
    width: 150,
    required: true,
    langCode: 'webOperation_category_categoryName_tableColumn_text',
    hideInSearch: true,
  },
  {
    dataIndex: 'categoryName',
    width: 150,
    required: true,
    hideInTable: true,
  },
  {
    dataIndex: 'categoryIcon',
    width: 150,
    hideInSearch: true,
    required: true,
    valueType: 'image',
    render: (_, record) => <Image width={80} height={80} src={record.categoryIcon} />,
  },
  {
    dataIndex: 'langId',
    width: 160,
    render: (_, record) => record?.categoryName?.langId,
  },
  ...extra,
  {
    dataIndex: 'description',
    width: 150,
    hideInSearch: true,
    valueType: 'textarea',
  },
];

export const detailColumns: typeColumns[] = [
  {
    dataIndex: 'id',
    width: 160,
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: ['categoryName', 'message'],
    langCode: 'webOperation_category_categoryName_tableColumn_text',
    width: 150,
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: ['categoryName', 'langId'],
    langCode: 'webCommon_page_langId_common_text',
    width: 160,
    showInForm: true,
    readonly: true,
  },
];
