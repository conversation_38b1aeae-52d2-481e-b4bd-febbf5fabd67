// 框架依赖引入
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { Access } from '@@/plugin-access/access';
import React, { useRef, useState } from 'react';
import { Divider, Space, Spin } from 'antd';
import { FormattedMessage, useAccess } from 'umi';
// 公共自定义依赖引入
import ResizableTable from '@/components/Table';
import { useColumn } from '@/hooks/column';
import KeepAlive from '@/components/KeepAlive';
// 页面自定义依赖引入
import AppSortEdit, { RefType } from './AppSortEdit';
import { getAppItemList, getItemDetail } from '@/services/app';
import { listColumns } from './columns/AppSort';

const AppSort: React.FC = () => {
  const access = useAccess();
  const actionRef = useRef<ActionType>();
  const editRef = useRef<RefType>(null);
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const { createColumns } = useColumn();

  const editSort = async (code: string, mode: string) => {
    setActionLoading(true);
    try {
      const res = await getItemDetail(code);
      setActionLoading(false);
      editRef.current?.edit(res, mode);
    } catch (e) {
      setActionLoading(false);
    }
  };

  const actionColumn: ProColumns = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 100,
    render: (_, record) => (
      <Space key="option" split={<Divider type="vertical" />}>
        <Access accessible={access.canViewAppSort()}>
          <a key="view" onClick={() => editSort(record.itemCode, 'view')}>
            <FormattedMessage id="webCommon_page_view_common_text" />
          </a>
        </Access>
        <Access accessible={access.canEditAppSort()}>
          <a key="edit" onClick={() => editSort(record.itemCode, 'edit')}>
            <FormattedMessage id="webCommon_page_edit_common_text" />
          </a>
        </Access>
      </Space>
    ),
  };

  const initColumns = createColumns('appsort', [...listColumns, actionColumn]);

  return (
    <>
      <Spin spinning={actionLoading}>
        <ResizableTable<API.AppSortItem, API.PageParams>
          actionRef={actionRef}
          rowKey="itemCode"
          defaultSize="small"
          search={false}
          pagination={false}
          request={getAppItemList}
          columns={initColumns}
        />
        <AppSortEdit ref={editRef} key="sort" refresh={() => actionRef.current?.reload()} />
      </Spin>
    </>
  );
};
export default () => {
  return (
    <KeepAlive>
      <AppSort />
    </KeepAlive>
  );
};
