// 框架依赖引入
import type { ActionType } from '@ant-design/pro-components';
import { ProFormInstance } from '@ant-design/pro-components';
import type { typeColumns } from '@/hooks/column';
import { Button } from 'antd';
import { Access } from '@@/plugin-access/access';
import React, { useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
// 自定义公共依赖引入
import Modal, { RefType } from '@/components/Modal/index';
import ResizableTable from '@/components/Table';
import { useColumn } from '@/hooks/column';
import KeepAlive from '@/components/KeepAlive';
import useExport from '@/hooks/useExport';
// 页面自定义依赖引入
import { getUserList, exportUserList } from '@/services/app';
import { listColumns } from './columns/UserList';
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import { EMAIL_REGEX } from './constants';

type UserListItemWithDesensitizedFlag = API.UserItem & {
  /**
   * 是否显示脱敏
   */
  isDesensitized: boolean;
};

const UserList: React.FC = () => {
  const access = useAccess();
  const actionRef = useRef<ActionType>();
  const modalRef = useRef<RefType>(null);
  const formRef = useRef<ProFormInstance>();
  const intl = useIntl();

  const { createColumns } = useColumn();
  // 增加脱敏的标志，控制表格切换显示脱敏数据
  const [userListWithDesensitizedFlag, setUserListWithDesensitizedFlag] = useState<
    UserListItemWithDesensitizedFlag[]
  >([]);

  const { run: handleExport, loading: exportLoading } = useExport(
    exportUserList,
    formRef,
    intl.formatMessage({ id: 'webOperation_user_export_fileName_text' }),
  );

  const toDesensitizedColumns: typeColumns[] = [
    {
      dataIndex: 'email',
      width: 160,
      render: (_: any, record: UserListItemWithDesensitizedFlag) => {
        return record.isDesensitized
          ? record.email?.replace(
              EMAIL_REGEX,
              ($0: string, $1: string, $2: string) =>
                `${$1.charAt(0)}${'*'.repeat($1.length - 1)}${$2}`,
            )
          : record.email;
      },
    },
    {
      dataIndex: 'userId',
      width: 170,
      render: (_: any, record: UserListItemWithDesensitizedFlag) => {
        return record.isDesensitized
          ? record.userId?.replace(
              /^(\d{7})(\d+)(\d{8})$/,
              ($0: string, $1: string, $2: string, $3: string) =>
                `${$1}${'*'.repeat($2.length)}${$3}`,
            )
          : record.userId;
      },
    },
  ];

  const operationColumns: typeColumns[] = [
    {
      title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
      width: 50,
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      render: (_: any, record: UserListItemWithDesensitizedFlag) => {
        const onSwitchDesensitizedFlag = () => {
          let list = [...userListWithDesensitizedFlag];
          for (const item of list) {
            if (item.id === record.id) {
              item.isDesensitized = !item.isDesensitized;
              setUserListWithDesensitizedFlag(list);
              break;
            }
          }
        };
        return record.isDesensitized ? (
          <EyeOutlined onClick={onSwitchDesensitizedFlag} />
        ) : (
          <EyeInvisibleOutlined onClick={onSwitchDesensitizedFlag} />
        );
      },
    },
  ];

  const allColumns = [...toDesensitizedColumns, ...listColumns, ...operationColumns];
  const columns = createColumns('user', allColumns);

  return (
    <>
      <ResizableTable<API.UserItem, API.UserPageParams>
        actionRef={actionRef}
        formRef={formRef}
        rowKey="userId"
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        headerTitle={
          <Access accessible={access.canExportUserList()}>
            <Button
              loading={exportLoading}
              type="primary"
              onClick={async () => {
                const res = await handleExport();
                if (res.type === 'application/json') {
                  modalRef.current?.setProps({
                    okButtonProps: { style: { display: 'none' } },
                    cancelText: intl.formatMessage({
                      id: 'webCommon_page_confirmText_button_text',
                    }),
                  });
                  modalRef.current?.open({
                    title: 'webCommon_page_exportFailed_modal_title',
                    msg:
                      intl.formatMessage({ id: 'webOperation_userdevice_export_fileName_text' }) +
                      intl.formatMessage({ id: 'webCommon_page_exportFailed_modal_message' }),
                  });
                }
              }}
            >
              <FormattedMessage id="webCommon_page_export_button_text" />
            </Button>
          </Access>
        }
        scroll={{ x: 1300 }}
        request={async (params) => {
          const userList = await getUserList(params);
          const desensitizedDeviceList = userList.data.map(
            (item: UserListItemWithDesensitizedFlag) => ({
              ...item,
              isDesensitized: true,
            }),
          );
          setUserListWithDesensitizedFlag(desensitizedDeviceList);
          return {
            data: desensitizedDeviceList,
            success: true,
            total: userList.total,
          };
        }}
        columns={columns}
      />
      <Modal ref={modalRef} parentRef={actionRef} />
    </>
  );
};
export default () => {
  return (
    <KeepAlive>
      <UserList />
    </KeepAlive>
  );
};
