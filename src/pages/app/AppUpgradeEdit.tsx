import FormContainer from '@/components/Form/FormContainer';
import { len128, len36, len500 } from '@/utils/validate';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormInstance,
  ProFormText,
  ProFormTextArea,
  ProFormSelect,
} from '@ant-design/pro-components';
import { Button, message } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { createUpgradeVersion } from '@/services/upgrade';
const AppUpgradeEdit = React.forwardRef<APP.RefType<API.AppQuestionItem>, APP.EditFormProps>(
  (props, ref) => {
    const { refresh } = props;
    const intl = useIntl();
    const formRef = useRef<ProFormInstance<API.AppQuestionItem>>();
    const [visible, setVisible] = useState<boolean>(false);
    const [initialValues, setInitialValues] = useState<API.AppQuestionItem>({} as any);
    const access = useAccess();

    const { run: handleSubmit, loading } = useRequest(
      async (values) => {
        await createUpgradeVersion({
          version: values.version,
          name: values.name.message,
          updateContent: values.updateContent.message,
          businessType: values.businessType,
        });
        message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
        refresh?.();
        setVisible(false);
      },
      { manual: true },
    );

    const onClose = () => {
      setVisible(false);
    };

    useImperativeHandle(ref, () => ({
      edit: () => {
        console.error('error entry!');
      },
    }));

    const onCreate = () => {
      setInitialValues({} as any);
      setVisible(true);
    };

    return (
      <>
        <FormContainer
          title={intl.formatMessage({ id: 'webCommon_page_add_button_text' })}
          width="50%"
          onCancel={onClose}
          onConfirm={() => formRef.current?.submit()}
          open={visible}
          destroyOnClose={true}
          loading={loading}
        >
          <ProForm
            initialValues={initialValues}
            onFinish={handleSubmit}
            formRef={formRef}
            labelCol={{ flex: '150px' }}
            layout="horizontal"
            onReset={onClose}
            submitter={false}
          >
            <ProFormSelect
              name="businessType"
              label={intl.formatMessage({
                id: 'webOperation_appUpgrade_appName_tableColumn_text',
              })}
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({
                    id: 'webOperation_appUpgrade_appName_select_placeholder',
                  }),
                },
              ]}
              placeholder={intl.formatMessage({
                id: 'webOperation_appUpgrade_appName_select_placeholder',
              })}
              valueEnum={{
                1: intl.formatMessage({ id: 'webOperation_dictionary_EGOConnect_select_text' }),
                2: intl.formatMessage({ id: 'webOperation_dictionary_fleetService_select_text' }),
              }}
            />
            <ProFormText
              name="version"
              label={intl.formatMessage({
                id: 'webOperation_appUpgrade_version_input_text',
              })}
              rules={[
                {
                  required: true,
                  message: (
                    <FormattedMessage id="webOperation_appUpgrade_version_input_placeholder" />
                  ),
                },
                {
                  pattern: /^((0|[1-9][0-9]{0,2})\.){2}(0|[1-9][0-9]{0,2})$/,
                  message: (
                    <FormattedMessage id="webOperation_appUpgrade_version_validator_message" />
                  ),
                },
                len36,
              ]}
              placeholder={intl.formatMessage({
                id: 'webOperation_appUpgrade_version_input_placeholder',
              })}
            />

            <ProFormText
              name={['name', 'message']}
              label={intl.formatMessage({
                id: 'webOperation_appUpgrade_name_input_text',
              })}
              placeholder={intl.formatMessage({
                id: 'webOperation_appUpgrade_name_input_placeholder',
              })}
              rules={[
                {
                  required: true,
                  message: <FormattedMessage id="webOperation_appUpgrade_name_input_placeholder" />,
                },
                len128,
              ]}
            />

            <ProFormTextArea
              name={['updateContent', 'message']}
              label={intl.formatMessage({
                id: 'webOperation_appUpgrade_changelog_input_text',
              })}
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({
                    id: 'webOperation_appUpgrade_changelog_input_placeholder',
                  }),
                },
                len500,
              ]}
            />
          </ProForm>
        </FormContainer>
        <Access accessible={access.canCreateAppVersion()}>
          <Button type="primary" onClick={onCreate}>
            <PlusOutlined />
            <FormattedMessage id="webCommon_page_add_button_text" />
          </Button>
        </Access>
      </>
    );
  },
);

export default AppUpgradeEdit;
