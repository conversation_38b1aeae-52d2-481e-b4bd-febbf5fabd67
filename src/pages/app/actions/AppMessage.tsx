import { message } from 'antd';
import {
  deleteSysMessage,
  applyReleaseSysMessage,
  cancelApplyReleaseSysMessage,
  applyStopReleaseSysMessage,
  cancelApplyStopReleaseSysMessage,
  viewRefuseReleaseReasonSysMessage,
  viewRefuseStopReleaseReasonSysMessage,
  viewRefuseTestReasonSysMessage,
  deleteMarketingMessage,
  applyReleaseMarketingMessage,
  cancelApplyReleaseMarketingMessage,
  applyStopReleaseMarketingMessage,
  cancelApplyStopReleaseMarketingMessage,
  viewRefuseReleaseReasonMarketingMessage,
  viewRefuseStopReleaseReasonMarketingMessage,
  viewRefuseTestReasonMarketingMessage,
} from '@/services/app';
import { OptionColumn, showRefuseReason, showTwiceConfirmModal } from '@/hooks/action';
import { RefObject, MutableRefObject } from 'react';
import { ProFormInstance } from '@ant-design/pro-components';
import { RefType } from '@/components/Modal/index';

export const buttonList = (
  modalRef: RefObject<RefType>,
  formRef: MutableRefObject<ProFormInstance | undefined>,
  type: string,
): OptionColumn[] => {
  return [
    // 详情
    {
      name: 'canView',
      module: type === 'system' ? 'SysMsgApply' : 'MarketingMsgApply',
      langCode: 'webCommon_page_detail_common_text',
    },
    // 推送结果
    {
      name: 'canViewPushResult',
      module: type === 'system' ? 'SysMsgApply' : 'MarketingMsgApply',
      langCode: 'webCommon_page_pushResult_tableButton_linkText',
    },
    // 申请发布
    {
      name: 'canApplyRelease',
      module: type === 'system' ? 'SysMsgApply' : 'MarketingMsgApply',
      onClick: async (id, msg) => {
        type === 'system'
          ? await applyReleaseSysMessage(id)
          : await applyReleaseMarketingMessage(id);
        message.success(msg);
      },
    },
    // 编辑
    {
      name: 'canUpdate',
      module: type === 'system' ? 'SysMsgApply' : 'MarketingMsgApply',
      langCode: 'webCommon_page_edit_common_text',
    },
    // 撤回发布申请
    {
      name: 'canCancelApplyRelease',
      module: type === 'system' ? 'SysMsgApply' : 'MarketingMsgApply',
      onClick: async (id, msg) => {
        type === 'system'
          ? await cancelApplyReleaseSysMessage(id)
          : await cancelApplyReleaseMarketingMessage(id);
        message.success(msg);
      },
    },
    // 删除
    {
      name: 'canDelete',
      module: type === 'system' ? 'SysMsgApply' : 'MarketingMsgApply',
      langCode: 'webCommon_page_delete_tableButton_linkText',
      onClick: (id: string) => {
        showTwiceConfirmModal(
          { modalRef, formRef },
          { api: type === 'system' ? deleteSysMessage : deleteMarketingMessage, id },
          'appMessage_delete',
        );
      },
    },
    // 申请停止发布
    {
      name: 'canApplyStopRelease',
      module: type === 'system' ? 'SysMsgApply' : 'MarketingMsgApply',
      onClick: async (id, msg) => {
        type === 'system'
          ? await applyStopReleaseSysMessage(id)
          : await applyStopReleaseMarketingMessage(id);
        message.success(msg);
      },
    },
    // 撤回停止发布申请
    {
      name: 'canCancelApplyStopRelease',
      module: type === 'system' ? 'SysMsgApply' : 'MarketingMsgApply',
      onClick: async (id, msg) => {
        type === 'system'
          ? await cancelApplyStopReleaseSysMessage(id)
          : await cancelApplyStopReleaseMarketingMessage(id);
        message.success(msg);
      },
    },
    // 发布被驳回原因
    {
      name: 'canViewRefuseReleaseReason',
      module: type === 'system' ? 'SysMsgApply' : 'MarketingMsgApply',
      onClick: async (id: string) => {
        showRefuseReason(
          type === 'system'
            ? viewRefuseReleaseReasonSysMessage
            : viewRefuseReleaseReasonMarketingMessage,
          modalRef,
          id,
        );
      },
    },
    // 测试被驳回原因
    {
      name: 'canViewRefuseTestReason',
      module: type === 'system' ? 'SysMsgApply' : 'MarketingMsgApply',
      onClick: async (id: string) => {
        showRefuseReason(
          type === 'system' ? viewRefuseTestReasonSysMessage : viewRefuseTestReasonMarketingMessage,
          modalRef,
          id,
        );
      },
    },
    // 停止发布被驳回原因
    {
      name: 'canViewRefuseStopReleaseReason',
      module: type === 'system' ? 'SysMsgApply' : 'MarketingMsgApply',
      onClick: async (id: string) => {
        showRefuseReason(
          type === 'system'
            ? viewRefuseStopReleaseReasonSysMessage
            : viewRefuseStopReleaseReasonMarketingMessage,
          modalRef,
          id,
        );
      },
    },
    // 复制消息
    {
      name: 'canCopy',
      module: type === 'system' ? 'SysMsgApply' : 'MarketingMsgApply',
      langCode: 'webCommon_page_copyMessage_button_linkText',
    },
  ];
};
