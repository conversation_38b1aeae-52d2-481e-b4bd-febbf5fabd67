import { RefObject, MutableRefObject } from 'react';
import { ProFormInstance } from '@ant-design/pro-components';
import { message } from 'antd';

import { RefType } from '@/components/Modal/index';
import { OptionColumn, showRefuseReason, showTwiceConfirmModal } from '@/hooks/action';
import {
  applyProtocolRelease,
  applyStopRelease,
  cancelProtocolApply,
  cancelApplyStopRelease,
  deleteProtocol,
  viewRefuseReleaseReason,
  viewRefuseStopReleaseReason,
  viewRefuseTestReason,
} from '@/services/app';

export const buttonList = (
  modalRef: RefObject<RefType>,
  formRef: MutableRefObject<ProFormInstance | undefined>,
): OptionColumn[] => {
  return [
    {
      name: 'canView',
      module: 'AppProtocol',
      langCode: 'webCommon_page_view_common_text',
    },
    {
      name: 'canApplyRelease',
      module: 'AppProtocol',
      onClick: async (id, msg) => {
        await applyProtocolRelease(id);
        message.success(msg);
      },
    },
    {
      name: 'canCancelApplyRelease',
      module: 'AppProtocol',
      onClick: async (id, msg) => {
        await cancelProtocolApply(id);
        message.success(msg);
      },
    },
    {
      name: 'canAddNewVersion',
      module: 'AppProtocol',
      langCode: 'webOperation_protocol_addNewVersion_tableButton_linkText',
    },
    {
      name: 'canUpdate',
      module: 'AppProtocol',
      langCode: 'webCommon_page_edit_common_text',
    },
    {
      name: 'canCopy',
      module: 'AppProtocol',
      langCode: 'webCommon_page_copy_button_linkText',
    },
    {
      name: 'canApplyStopRelease',
      module: 'AppProtocol',
      onClick: async (id, msg) => {
        await applyStopRelease(id);
        message.success(msg);
      },
    },
    {
      name: 'canCancelApplyStopRelease',
      module: 'AppProtocol',
      onClick: async (id, msg) => {
        await cancelApplyStopRelease(id);
        message.success(msg);
      },
    },
    {
      name: 'canViewRefuseReleaseReason',
      module: 'AppProtocol',
      onClick: (id: string) => {
        showRefuseReason(viewRefuseReleaseReason, modalRef, id);
      },
    },
    {
      name: 'canViewRefuseTestReason',
      module: 'AppProtocol',
      onClick: (id: string) => {
        showRefuseReason(viewRefuseTestReason, modalRef, id);
      },
    },
    {
      name: 'canViewRefuseStopReleaseReason',
      module: 'AppProtocol',
      onClick: (id: string) => {
        showRefuseReason(viewRefuseStopReleaseReason, modalRef, id);
      },
    },
    {
      name: 'canDelete',
      module: 'AppProtocol',
      langCode: 'webCommon_page_delete_tableButton_linkText',
      onClick: (id: string) => {
        showTwiceConfirmModal(
          { modalRef, formRef },
          { api: deleteProtocol, id },
          'protocol_delete',
        );
      },
    },
  ];
};
