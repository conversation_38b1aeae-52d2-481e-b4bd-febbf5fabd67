import React, { useImperativeHandle, useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { Space, message, Button } from 'antd';
import {
  ProForm,
  ProFormInstance,
  ProFormFieldSet,
  ProFormDependency,
  ProFormRadio,
} from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { omit, split, map, isArray } from 'lodash-es';
import { PlusOutlined } from '@ant-design/icons';

import ResizableTable from '@/components/Table';
import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';
import type { typeColumns } from '@/hooks/column';
import GroupList from '@/pages/product/components/GroupList';

import { startColumns, endColumns, newColumns, editColumns, pushColumns } from './columns/Message';
import {
  addSysMessage,
  editSysMessage,
  addMarketingMessage,
  editMarketingMessage,
} from '@/services/app';

export interface RefType {
  edit: (id: string, record: API.AppMessageItem, dataType: string) => void;
}

export interface FormProps {
  refresh?: () => Promise<void> | undefined;
  type?: string;
  dict: { [key: string]: any };
}

const MessageConfig = React.forwardRef<RefType, FormProps>((props, ref) => {
  const { refresh, type = 'system', dict } = props;
  const intl = useIntl();
  const formRef = useRef<ProFormInstance<API.AppMessageItem>>();
  const [visible, setVisible] = useState<boolean>(false);
  const [msgId, setMsgId] = useState<string | undefined>(undefined);
  const [mode, setMode] = useState<string>('new');
  const [prdGroupList, setPrdGroupList] = useState<API.GroupItem[]>([]);
  const [testGroupList, setTestGroupList] = useState<API.GroupItem[]>([]);
  const [initialValues, setInitialValues] = useState<API.AppMessageItem>({} as any);
  const [formColumns, setFormColumns] = useState<typeColumns[]>(newColumns);
  const access = useAccess();

  const { run: handleSubmit, loading } = useRequest(
    async (values) => {
      if (type === 'system' && msgId) {
        values.sysMsgId = msgId;
      }
      if (type === 'marketing' && msgId) {
        values.marketingMsgId = msgId;
        delete values.sysMsgId;
      }
      if (values.startType === '2') {
        values.startTime = values.startDate + ' ' + values.startTime;
      }
      if (values.endType === '2') {
        values.endTime = values.endDate + ' ' + values.endTime;
      }
      values.startType = Number(values.startType);
      values.endType = Number(values.endType);
      const formData = omit(values, ['startDate', 'endDate']);
      if (mode === 'new') {
        type === 'system' ? await addSysMessage(formData) : await addMarketingMessage(formData);
      }
      if (mode === 'copy') {
        type === 'system' ? await addSysMessage(formData) : await addMarketingMessage(formData);
      }
      if (mode === 'edit') {
        type === 'system' ? await editSysMessage(formData) : await editMarketingMessage(formData);
      }
      setVisible(false);
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      refresh?.();
    },
    { manual: true },
  );

  const onClose = () => {
    setVisible(false);
  };

  const deleteProductGroup = (index: number) => {
    const oldValue = [...prdGroupList];
    oldValue.splice(index, 1);
    setPrdGroupList(oldValue);
    formRef.current?.setFieldsValue({ prdGroup: oldValue.map((item) => item.groupName) });
  };

  const deleteTestGroup = (index: number) => {
    const oldValue = [...testGroupList];
    oldValue.splice(index, 1);
    setTestGroupList(oldValue);
    formRef.current?.setFieldsValue({ testGroup: oldValue.map((item) => item.groupName) });
  };

  const productColumns: ProColumns[] = [
    {
      title: <FormattedMessage id="webOperation_group_groupName_tableColumn_text" />,
      dataIndex: 'groupName',
      width: 350,
    },
    {
      title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
      dataIndex: 'option',
      valueType: 'option',
      width: 100,
      render: (_, record, index) => (
        <Access
          accessible={
            type === 'system'
              ? access.canDeleteProGroupSysMsgApply()
              : access.canDeleteProGroupMarketingMsgApply()
          }
        >
          <a
            key="delete"
            onClick={() => {
              deleteProductGroup(index);
            }}
          >
            <FormattedMessage id="webCommon_page_delete_tableButton_linkText" />
          </a>
        </Access>
      ),
    },
  ];

  const testColumns: ProColumns[] = [
    {
      title: <FormattedMessage id="webOperation_group_groupName_tableColumn_text" />,
      dataIndex: 'groupName',
      width: 350,
    },
    {
      title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
      dataIndex: 'option',
      valueType: 'option',
      width: 100,
      render: (_, record, index) => (
        <Access
          accessible={
            type === 'system'
              ? access.canDeleteTestGroupSysMsgApply()
              : access.canDeleteTestGroupMarketingMsgApply()
          }
        >
          <a
            key="delete"
            onClick={() => {
              deleteTestGroup(index);
            }}
          >
            <FormattedMessage id="webCommon_page_delete_tableButton_linkText" />
          </a>
        </Access>
      ),
    },
  ];

  useImperativeHandle(ref, () => ({
    edit: (id: string, record: API.AppMessageItem, dataType: string) => {
      setTestGroupList([]);
      setPrdGroupList([]);
      /* 获取到配置详情后，数据回显 */
      // 开始时间
      if (record.startType === 2) {
        const start = split(record.startTime, ' ');
        record.startDate = start[0];
        record.startTime = start[1];
      } else {
        record.startZone = undefined;
        record.startDate = undefined;
        record.startTime = undefined;
      }
      // 结束时间
      if (record.endType === 2) {
        const end = split(record.endTime, ' ');
        record.endDate = end[0];
        record.endTime = end[1];
      } else {
        record.endZone = undefined;
        record.endDate = undefined;
        record.endTime = undefined;
      }
      record.startType = String(record.startType);
      record.endType = String(record.endType);
      // 测试分组
      if (isArray(record.testGroup)) {
        formRef.current?.setFieldsValue({
          testGroup: record.testGroup,
        });
        setTestGroupList(
          map(record.testGroup, (item: string) => ({
            groupName: item,
          })),
        );
      }
      // 生产分组
      if (isArray(record.prdGroup)) {
        formRef.current?.setFieldsValue({
          prdGroup: record.prdGroup,
        });
        setPrdGroupList(
          map(record.prdGroup, (item: string) => ({
            groupName: item,
          })),
        );
      }
      setMode(dataType);
      setInitialValues(record || {});
      if (dataType === 'new' || dataType === 'copy') {
        setFormColumns(newColumns);
      } else if (dataType === 'edit') {
        setFormColumns(editColumns);
        setMsgId(id);
      }
      setVisible(true);
    },
  }));

  const updatePrdGroup = (list: API.GroupItem[]) => {
    const newValues = [...list, ...prdGroupList];
    setPrdGroupList(newValues);
    formRef.current?.setFieldsValue({ prdGroup: newValues.map((item) => item.groupName) });
  };

  const updateTestGroup = (list: API.GroupItem[]) => {
    const newValues = [...list, ...testGroupList];
    setTestGroupList(newValues);
    formRef.current?.setFieldsValue({ testGroup: newValues.map((item) => item.groupName) });
  };

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: `webOperation_message_${mode}_drawer_title` })}
        width="50%"
        onCancel={onClose}
        onConfirm={() => formRef.current?.submit()}
        open={visible}
        destroyOnClose={true}
        loading={loading}
      >
        <ProForm
          initialValues={initialValues}
          onFinish={handleSubmit}
          formRef={formRef}
          layout="horizontal"
          labelCol={{ flex: '150px' }}
          onReset={onClose}
          submitter={false}
          className="operation-ant-edit-form"
        >
          <FormFields columns={formColumns} pageName="appMessage" />
          <ProForm.Item
            name="prdGroup"
            label={intl.formatMessage({ id: 'webOperation_release_prdGroup_tableColumn_text' })}
          >
            <ResizableTable<API.GroupItem, API.PageParams>
              rowKey={(record) => record.groupName}
              defaultSize="small"
              search={false}
              options={false}
              columns={productColumns}
              pagination={false}
              headerTitle={
                <GroupList
                  key="prdGroup"
                  moduleName={type === 'system' ? 'ProSysMsgApply' : 'ProMarketingMsgApply'}
                  selectedKeys={prdGroupList}
                  setData={updatePrdGroup}
                />
              }
              dataSource={prdGroupList}
            />
          </ProForm.Item>
          <ProForm.Item
            name="testGroup"
            label={intl.formatMessage({ id: 'webOperation_release_testGroup_tableColumn_text' })}
            rules={[
              {
                required: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_release_testGroup_tableColumn_text' }),
              },
            ]}
          >
            <ResizableTable<API.GroupItem, API.PageParams>
              rowKey={(record) => record.groupName}
              defaultSize="small"
              search={false}
              options={false}
              columns={testColumns}
              pagination={false}
              headerTitle={
                <GroupList
                  key="testGroup"
                  moduleName={type === 'system' ? 'TestSysMsgApply' : 'TestMarketingMsgApply'}
                  selectedKeys={testGroupList}
                  setData={updateTestGroup}
                />
              }
              dataSource={testGroupList}
            />
          </ProForm.Item>
          <FormFields columns={pushColumns} pageName="appMessage" />
          <ProFormFieldSet
            name="start"
            label={intl.formatMessage({ id: 'webOperation_release_start_tableColumn_text' })}
            type="group"
            initialValue={mode === 'new' ? undefined : 0}
            rules={[
              {
                required: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_release_start_tableColumn_text' }),
              },
            ]}
            transform={(value: any) => ({})}
          >
            <Space>
              <ProFormRadio.Group name="startType" label={false} options={dict['startType']} />
              <ProFormDependency name={['startType']}>
                {({ startType }) =>
                  startType === '2' ? (
                    <ProFormFieldSet name="startGroup" type="group" label={false}>
                      <FormFields columns={startColumns} pageName="release" hideLabel={true} />
                    </ProFormFieldSet>
                  ) : null
                }
              </ProFormDependency>
            </Space>
          </ProFormFieldSet>
          <ProFormFieldSet
            name="end"
            label={intl.formatMessage({ id: 'webOperation_release_end_tableColumn_text' })}
            type="group"
            initialValue={mode === 'new' ? undefined : 0}
            rules={[
              {
                required: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_release_end_tableColumn_text' }),
              },
            ]}
            transform={(value: any) => ({})}
          >
            <Space>
              <ProFormRadio.Group name="endType" label={false} options={dict['endType']} />
              <ProFormDependency name={['endType']}>
                {({ endType }) =>
                  endType === '2' ? (
                    <ProFormFieldSet name="endGroup" type="group" label={false}>
                      <FormFields columns={endColumns} pageName="release" hideLabel={true} />
                    </ProFormFieldSet>
                  ) : null
                }
              </ProFormDependency>
            </Space>
          </ProFormFieldSet>
        </ProForm>
      </FormContainer>
      <Access
        accessible={
          type === 'system' ? access.canCreateSysMsgApply() : access.canCreateMarketingMsgApply()
        }
      >
        <Button
          type="primary"
          onClick={() => {
            // 页面表单功能为添加消息
            setMode('new');
            // 创建时的表单字段
            setFormColumns(newColumns);
            setInitialValues({} as any);
            setVisible(true);
            setMsgId(undefined);
            setPrdGroupList([]);
            setTestGroupList([]);
          }}
        >
          <PlusOutlined />
          <FormattedMessage id="webOperation_message_new_drawer_title" />
        </Button>
      </Access>
    </>
  );
});

export default MessageConfig;
