import { ProForm } from '@ant-design/pro-components';
import React, { useImperativeHandle, useState } from 'react';
import { useIntl } from 'umi';

import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';
import { detailColumns } from './columns/Message';

export interface DetailProps {
  dict: { [key: string]: any };
}

export interface RefType {
  show: (record: API.AppMessageItem) => void;
}

const MessageDetail = React.forwardRef<RefType, DetailProps>((props, ref) => {
  const { dict } = props;
  const intl = useIntl();
  const [visible, setVisible] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState<API.AppMessageItem>({});

  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    show: (record: API.AppMessageItem) => {
      try {
        // 开始时间的转化
        if (record.startType === 2) {
          record.startTime = record.startZone + ' ' + record.startTime;
        } else {
          const start = dict['startType'].find(
            (item: API.selectOptions) => item.value === String(record.startType),
          );
          record.startTime = start.label;
        }
        // 结束时间的转化
        if (record.endType === 2) {
          record.endTime = record.endZone + ' ' + record.endTime;
        } else {
          const end = dict['endType'].find(
            (item: API.selectOptions) => item.value === String(record.endType),
          );
          record.endTime = end.label;
        }
      } catch (e) {
        console.log(e);
      }
      setInitialValues(record);
      setVisible(true);
    },
  }));

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: 'webOperation_message_detail_drawer_title' })}
        width="50%"
        onCancel={onClose}
        hiddenConfirm={true}
        open={visible}
        destroyOnClose={true}
      >
        <ProForm
          initialValues={initialValues}
          labelCol={{ span: 4 }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
          className="operation-ant-view-form"
        >
          <FormFields columns={detailColumns} pageName="appMessage" />
        </ProForm>
      </FormContainer>
    </>
  );
});

export default MessageDetail;
