import { ProForm, ProFormInstance } from '@ant-design/pro-components';
import React, { useImperativeHandle, useState, useRef } from 'react';
import { useIntl } from 'umi';

import ResizableTable from '@/components/Table';
import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';
import { useColumn } from '@/hooks/column';
import type { typeColumns } from '@/hooks/column';
import { resultColumns } from './columns/Message';
import { getMarketingMessageResultList, getSysMessageResultList } from '@/services/app';

export interface ResultProps {
  type: string;
}

export interface RefType {
  show: (record: API.AppMessageItem) => void;
}

const MessageResult = React.forwardRef<RefType, ResultProps>((props, ref) => {
  const { type } = props;
  const intl = useIntl();
  const [visible, setVisible] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState<API.AppMessageItem>({});
  const formRef = useRef<ProFormInstance>();
  const { createColumns } = useColumn();
  const [tableColumns, setTableColumns] = useState<typeColumns[]>([]);

  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    show: (record: API.AppMessageItem) => {
      record.sysMsgId = record.sysMsgId ?? record.marketingMsgId;
      setInitialValues(record);
      setVisible(true);
      const columns = createColumns('appMessage', resultColumns);
      setTableColumns(columns);
    },
  }));

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: 'webOperation_message_result_drawer_title' })}
        width="70%"
        onCancel={onClose}
        hiddenConfirm={true}
        open={visible}
        destroyOnClose={true}
      >
        <ProForm
          initialValues={initialValues}
          labelCol={{ span: 3 }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
        >
          <FormFields columns={resultColumns} pageName="appMessage" />
        </ProForm>
        <ResizableTable<API.AppMessageResultItem, API.AppMessageResultPageParams>
          formRef={formRef}
          rowKey="id"
          defaultSize="small"
          search={{
            labelWidth: 'auto',
          }}
          params={{ systemMessageId: initialValues.sysMsgId }}
          request={type === 'system' ? getSysMessageResultList : getMarketingMessageResultList}
          columns={tableColumns}
        />
      </FormContainer>
    </>
  );
});

export default MessageResult;
