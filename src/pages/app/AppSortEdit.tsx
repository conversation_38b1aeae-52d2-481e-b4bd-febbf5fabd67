// 框架依赖引入
import React, { useImperativeHandle, useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { message } from 'antd';
import {
  DragSortTable,
  ProForm,
  ProFormInstance,
  ProFormFieldSet,
} from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { omit } from 'lodash-es';
// 公共自定义依赖引入
import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';
// 页面自定义依赖引入
import { editAppList } from '@/services/app';
import { listColumns } from './columns/AppSort';
import './index.less';

export interface RefType {
  edit: (record: API.AppSortItem, method: string) => void;
}

const AppSortEdit = React.forwardRef<RefType, APP.EditFormProps>((props, ref) => {
  const { refresh } = props;
  const intl = useIntl();
  const formRef = useRef<ProFormInstance<API.AppSortItem>>();
  const [visible, setVisible] = useState<boolean>(false);
  const [method, setMethod] = useState<string>('edit');
  const [itemList, setItemList] = useState<API.ElementItem[]>([]);
  const [initialValues, setInitialValues] = useState<API.AppSortItem>({} as any);
  const [showConfirm, setShowConfirm] = useState({} as any);
  const access = useAccess();

  const { run: handleSubmit, loading } = useRequest(
    async (values) => {
      values.elements = values.elements?.map((item: API.ElementItem) => omit(item, 'element'));
      await editAppList(values);
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setVisible(false);
      refresh?.();
    },
    { manual: true },
  );

  const onClose = () => {
    setVisible(false);
  };

  const changeHiddenStatus = (index: number) => {
    const list = itemList;
    list[index].isShow = 1 - list[index].isShow!;
    handleDragSortEnd(list);
  };

  const handleDragSortEnd = (newDataSource: API.ElementItem[]) => {
    setItemList(newDataSource);
    formRef.current?.setFieldsValue({ elements: newDataSource });
  };

  const itemColumns: ProColumns[] = [
    {
      title: <FormattedMessage id="webCommon_page_sort_tableColumn_text" />,
      dataIndex: 'sort',
      width: 100,
      hideInTable: method !== 'edit',
    },
    {
      title: <FormattedMessage id="webOperation_appsort_elementCode_tableColumn_text" />,
      dataIndex: 'element',
      width: 250,
    },
    {
      title: <FormattedMessage id="webOperation_appsort_display_tableColumn_text" />,
      dataIndex: 'option',
      valueType: 'option',
      width: 100,
      render: (_, record, index) =>
        method === 'edit' ? (
          <Access accessible={access.canEditStatusAppSort()}>
            <a
              key="edit"
              onClick={() => {
                changeHiddenStatus(index);
              }}
            >
              <FormattedMessage
                id={`webOperation_appsort_isShow${record.isShow}_tableButton_text`}
              />
            </a>
          </Access>
        ) : (
          <FormattedMessage
            id={`webOperation_appsort_isShow${1 - record.isShow}_tableButton_text`}
          />
        ),
    },
  ];

  useImperativeHandle(ref, () => ({
    edit: (record: API.AppSortItem, mode: string) => {
      // 设置页面表单功能模式(edit:编辑； view:查看)
      setMethod(mode);
      // 编辑显示确定按钮，查看隐藏确定按钮
      if (mode === 'edit') {
        setShowConfirm({ onConfirm: () => formRef.current?.submit() });
      } else if (mode === 'view') {
        setShowConfirm({ hiddenConfirm: true });
      }
      // 设置表单字段初始值
      // record.elements = record.elements?.map((item) => omit(item, 'element'));
      setInitialValues(record || {});
      setItemList(record.elements || []);
      setVisible(true);
    },
  }));

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: `webOperation_appsort_${method}_drawer_title` })}
        width="50%"
        onCancel={onClose}
        {...showConfirm}
        open={visible}
        destroyOnClose={true}
        loading={loading}
      >
        <ProForm
          initialValues={initialValues}
          onFinish={handleSubmit}
          formRef={formRef}
          layout="horizontal"
          labelCol={{ span: 5 }}
          onReset={onClose}
          submitter={false}
        >
          <FormFields columns={listColumns} pageName="appsort" />
          <ProFormFieldSet
            name="elements"
            type="group"
            label={intl.formatMessage({ id: 'webOperation_appsort_item_tableColumn_text' })}
          >
            <DragSortTable<API.ElementItem, API.PageParams>
              rowKey="elementCode"
              defaultSize="small"
              search={false}
              options={false}
              columns={itemColumns}
              pagination={false}
              dataSource={itemList}
              dragSortKey="sort"
              onDragSortEnd={handleDragSortEnd}
            />
          </ProFormFieldSet>
        </ProForm>
      </FormContainer>
    </>
  );
});

export default AppSortEdit;
