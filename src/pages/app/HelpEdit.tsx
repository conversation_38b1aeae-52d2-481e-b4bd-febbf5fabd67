import FormContainer from '@/components/Form/FormContainer';
import { EditorItem } from '@/components/Form/EditorItem';
import { len128, len5000 } from '@/utils/validate';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Button, Divider, message } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { ProFormDependency } from '@ant-design/pro-form';
import { LocaleID } from '@/components/Locale';
import { createAppQuestion, updateAppQuestion, viewAppQuestionTemplate } from '@/services/app';
import { assign, join, replace, split } from 'lodash-es';

const HelpEdit = React.forwardRef<APP.RefType<API.AppQuestionItem>, APP.EditFormProps>(
  (props, ref) => {
    const { refresh } = props;
    const intl = useIntl();
    const formRef = useRef<ProFormInstance<API.AppQuestionItem>>();
    const [visible, setVisible] = useState<boolean>(false);
    const [readonly, setReadonly] = useState<boolean>(false);
    const [initialValues, setInitialValues] = useState<API.AppQuestionItem>({});
    const access = useAccess();
    const { run: handleSubmit, loading } = useRequest(
      async (values: any) => {
        const models = split(replace(values.model, /(\n|\r\n)/g, ';'), ';');

        if (!readonly) {
          if (initialValues.helpFaqId) {
            await updateAppQuestion({
              helpFaqId: initialValues.helpFaqId!,
              answer: values.answer.message,
              title: values.title.message,
              model: models,
            });
          } else {
            await createAppQuestion({
              answer: values.answer.message,
              title: values.title.message,
              model: models,
            });
          }
          message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
          refresh?.();
        }
        setVisible(false);
      },
      { manual: true },
    );

    const onClose = () => {
      setVisible(false);
    };

    useImperativeHandle(ref, () => ({
      edit: (item: API.AppQuestionItem, r) => {
        setReadonly(!!r);
        if (!access.canUpdateAppQuestion()) {
          console.warn(intl.formatMessage({ id: 'webCommon_page_noAccess_toast_text' }));
          return;
        }
        setInitialValues({ ...item, model: join(item.model, ';') });
        setVisible(true);

        viewAppQuestionTemplate(item.helpFaqId!).then((res) => {
          const data = assign({}, res.data, { model: join(res.data?.model, ';') });
          setInitialValues(data);
          formRef.current?.setFieldsValue(data);
        });
      },
    }));

    const onCreate = () => {
      setReadonly(false);
      setInitialValues({} as any);
      setVisible(true);
    };

    return (
      <>
        <FormContainer
          title={
            readonly
              ? intl.formatMessage({ id: 'webOperation_help_view_drawer_title' })
              : initialValues.helpFaqId
              ? intl.formatMessage({ id: 'webOperation_help_update_drawer_title' })
              : intl.formatMessage({ id: 'webOperation_help_create_button_text' })
          }
          width="50%"
          onCancel={onClose}
          onConfirm={() => formRef.current?.submit()}
          open={visible}
          destroyOnClose={true}
          loading={loading}
          hiddenConfirm={readonly}
        >
          <ProForm
            initialValues={initialValues}
            onFinish={handleSubmit}
            formRef={formRef}
            labelCol={{ flex: '150px' }}
            layout="horizontal"
            onReset={onClose}
            submitter={false}
            readonly={readonly}
          >
            {initialValues.helpFaqId ? (
              <ProFormText
                label={intl.formatMessage({ id: 'webOperation_help_id_tableColumn_text' })}
                name="helpFaqId"
                readonly
              />
            ) : null}

            <ProFormText
              name={['title', 'message']}
              label={intl.formatMessage({
                id: 'webOperation_help_title_tableColumn_text',
              })}
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({
                    id: 'webOperation_help_title_input_placeholder',
                  }),
                },
                len128,
              ]}
              placeholder={intl.formatMessage({
                id: 'webOperation_help_title_input_placeholder',
              })}
              extra={
                readonly ? null : (
                  <ProFormDependency name={['title', 'langId']}>
                    {({ title = {} }) => <LocaleID id={title?.langId} />}
                  </ProFormDependency>
                )
              }
            />

            <ProFormTextArea
              name="model"
              label={intl.formatMessage({
                id: 'webOperation_help_model_tableColumn_text',
              })}
              placeholder={intl.formatMessage({
                id: 'webOperation_help_model_input_placeholder',
              })}
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({
                    id: 'webOperation_help_model_input_rule',
                  }),
                },
                {
                  type: 'string',
                  max: 500,
                  message: intl.formatMessage({ id: 'webCommon_page_len500_validator_message' }),
                },
              ]}
            />

            <ProForm.Item
              name={['answer', 'message']}
              label={intl.formatMessage({
                id: 'webOperation_help_answer_tableColumn_text',
              })}
              validateTrigger="onBlur"
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({
                    id: 'webOperation_help_answerRule_tableColumn_text',
                  }),
                },
                len5000,
              ]}
            >
              <EditorItem readonly={readonly} />
            </ProForm.Item>

            {readonly ? (
              <>
                <Divider />
                <ProFormText
                  name={['readCount']}
                  label={intl.formatMessage({
                    id: 'webOperation_help_readCount_tableColumn_text',
                  })}
                  readonly
                />
                <ProFormText
                  name={['praiseCount']}
                  label={intl.formatMessage({
                    id: 'webOperation_help_praiseCount_tableColumn_text',
                  })}
                  readonly
                />
                <ProFormSelect
                  name={['appShowCode']}
                  label={intl.formatMessage({
                    id: 'webOperation_help_appShowCode_tableColumn_text',
                  })}
                  valueEnum={{
                    0: intl.formatMessage({ id: 'webOperation_dictionary_hide_select_text' }),
                    1: intl.formatMessage({ id: 'webOperation_dictionary_show_select_text' }),
                  }}
                  readonly
                />
              </>
            ) : null}
          </ProForm>
        </FormContainer>

        <Access accessible={access.canCreateAppQuestion()}>
          <Button type="primary" onClick={onCreate}>
            <PlusOutlined />
            <FormattedMessage id="webOperation_help_create_button_text" />
          </Button>
        </Access>
      </>
    );
  },
);

export default HelpEdit;
