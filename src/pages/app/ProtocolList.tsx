// 框架依赖引入
import React, { useRef, useState } from 'react';
import { FormattedMessage, useIntl } from 'umi';
import { Spin, message } from 'antd';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProFormInstance } from '@ant-design/pro-components';
import { map } from 'lodash-es';
// 公共自定义依赖引入
import ResizableTable from '@/components/Table';
import { useAction } from '@/hooks/action';
import { useColumn } from '@/hooks/column';
import Modal, { RefType as modalRefType } from '@/components/Modal/index';
import KeepAlive from '@/components/KeepAlive';
// 页面自定义依赖引入
import ProtocolEdit, { RefType } from './ProtocolEdit';
import { getProtocolList, getProtocolDetail, copyProtocol } from '@/services/app';
import { buttonList } from './actions/ProtocolList';
import { listColumns } from './columns/ProtocolList';

const ProtocolList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const modalFormRef = useRef<ProFormInstance>();
  const modalRef = useRef<modalRefType>(null);
  const editRef = useRef<RefType>(null);
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const intl = useIntl();
  const { createColumns } = useColumn();
  const { createActions } = useAction('appAgreementContentId');

  const editProtocol = async (id: string, record: API.ProtocolItem) => {
    setActionLoading(true);
    try {
      const res = await getProtocolDetail(id);
      setActionLoading(false);
      editRef.current?.edit(res, 'edit', record.canUpdateTypeAndTitle);
    } catch (e) {
      setActionLoading(false);
    }
  };

  const addNewProtocol = async (id: string) => {
    setActionLoading(true);
    try {
      const res = await getProtocolDetail(id);
      setActionLoading(false);
      editRef.current?.edit(res, 'new');
    } catch (e) {
      setActionLoading(false);
    }
  };

  const onCopyProtocol = async (id: string) => {
    setActionLoading(true);
    try {
      await copyProtocol(id);
      setActionLoading(false);
      message.success(intl.formatMessage({ id: 'webCommon_page_copySuccessed_toast_text' }));
      actionRef.current?.reload();
    } catch (e) {
      setActionLoading(false);
    }
  };

  const showProtocolDetail = async (id: string) => {
    setActionLoading(true);
    try {
      const res = await getProtocolDetail(id);
      setActionLoading(false);
      editRef.current?.edit(res, 'detail');
    } catch (e) {
      setActionLoading(false);
    }
  };

  const handleAction = async (id: string, action?: APP.FnType) => {
    setActionLoading(true);
    try {
      await action?.(id, intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setActionLoading(false);
      actionRef.current?.reload();
    } catch (e) {
      setActionLoading(false);
    }
  };

  const actionColumn: ProColumns = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 295,
    fixed: 'right',
    render: (_, record) => {
      record['canCopy'] = true;
      const columns = buttonList(modalRef, modalFormRef);
      map(columns, (item) => {
        const action = item.onClick;
        switch (item.name) {
          case 'canCopy':
            item.onClick = onCopyProtocol;
            break;
          case 'canUpdate':
            item.onClick = editProtocol;
            break;
          case 'canAddNewVersion':
            item.onClick = addNewProtocol;
            break;
          case 'canView':
            item.onClick = showProtocolDetail;
            break;
          case 'canApplyRelease':
          case 'canApplyStopRelease':
          case 'canCancelApplyRelease':
          case 'canCancelApplyStopRelease':
            item.onClick = (id) => handleAction(id, action);
            break;
        }
      });
      return createActions(columns, record, 'product');
    },
  };

  const initColumns = [...listColumns, actionColumn];
  const allColumns = createColumns('protocol', initColumns);

  return (
    <>
      <Spin spinning={actionLoading}>
        <ResizableTable<API.ProtocolItem, API.ProtocolPageParams>
          actionRef={actionRef}
          formRef={formRef}
          rowKey="appAgreementContentId"
          defaultSize="small"
          search={{
            defaultCollapsed: false,
            labelWidth: 'auto',
          }}
          headerTitle={
            <ProtocolEdit
              ref={editRef}
              key="create"
              refresh={() => {
                return actionRef.current?.reload();
              }}
            />
          }
          scroll={{ x: 1300 }}
          request={getProtocolList}
          columns={allColumns}
        />
      </Spin>
      <Modal ref={modalRef} parentRef={actionRef} formRef={modalFormRef} />
    </>
  );
};
export default () => {
  return (
    <KeepAlive>
      <ProtocolList />
    </KeepAlive>
  );
};
