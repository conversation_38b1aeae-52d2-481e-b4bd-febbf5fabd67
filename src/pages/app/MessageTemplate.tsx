// 框架依赖引入
import React, { useRef, useState } from 'react';
import { FormattedMessage, useIntl } from 'umi';
import { Spin } from 'antd';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProFormInstance } from '@ant-design/pro-components';
import { map } from 'lodash-es';
// 公共自定义依赖引入
import ResizableTable from '@/components/Table';
import Modal, { RefType as modalRefType } from '@/components/Modal/index';
import { useAction } from '@/hooks/action';
import { useColumn, extra } from '@/hooks/column';
// 页面自定义依赖引入
import MessageConfig, { RefType } from './MessageConfig';
import MessageDetail, { RefType as DetailRefType } from './MessageDetail';
import MessageResult, { RefType as ResultRefType } from './MessageResult';
import {
  getSysMessageList,
  getSysMessageDetail,
  getMarketingMessageList,
  getMarketingMessageDetail,
} from '@/services/app';
import { buttonList } from './actions/AppMessage';
import { listColumns } from './columns/Message';

export interface TemplateProps {
  type: string;
}

const MessageTemplate: React.FC<TemplateProps> = ({ type = 'system' }) => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const modalRef = useRef<modalRefType>(null);
  const modalFormRef = useRef<ProFormInstance>();
  const configRef = useRef<RefType>(null);
  const detailRef = useRef<DetailRefType>(null);
  const resultRef = useRef<ResultRefType>(null);
  const intl = useIntl();
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const { createColumns } = useColumn();
  const { createActions } = useAction(type === 'system' ? 'sysMsgId' : 'marketingMsgId');

  const showGroupConfig = async (id: string) => {
    setActionLoading(true);
    try {
      const res =
        type === 'system' ? await getSysMessageDetail(id) : await getMarketingMessageDetail(id);
      setActionLoading(false);
      res.sysMsgId = res.sysMsgId || res.marketingMsgId;
      configRef.current?.edit(id, res, 'edit');
    } catch (e) {
      setActionLoading(false);
    }
  };

  const showCopyConfig = async (id: string) => {
    setActionLoading(true);
    try {
      const res =
        type === 'system' ? await getSysMessageDetail(id) : await getMarketingMessageDetail(id);
      setActionLoading(false);
      res.sysMsgId = res.sysMsgId || res.marketingMsgId;
      configRef.current?.edit(id, res, 'copy');
    } catch (e) {
      setActionLoading(false);
    }
  };

  const showMessageDetail = async (id: string) => {
    setActionLoading(true);
    try {
      const res =
        type === 'system' ? await getSysMessageDetail(id) : await getMarketingMessageDetail(id);
      setActionLoading(false);
      res.sysMsgId = res.sysMsgId || res.marketingMsgId;
      detailRef.current?.show(res);
    } catch (e) {
      console.log(e);
      setActionLoading(false);
    }
  };

  const showPushResult = (record: API.FirmwareReleaseConfig) => {
    resultRef.current?.show(record);
  };

  const handleAction = async (id: string, action?: APP.FnType) => {
    setActionLoading(true);
    try {
      await action?.(id, intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setActionLoading(false);
      actionRef.current?.reload();
    } catch (e) {
      setActionLoading(false);
    }
  };

  const actionColumn: ProColumns<API.AppMessageItem> = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 250,
    fixed: 'right',
    render: (_, record) => {
      record['canViewPushResult'] = true;
      const columns = buttonList(modalRef, modalFormRef, type);
      map(columns, (item) => {
        const action = item.onClick;
        switch (item.name) {
          case 'canUpdate':
            item.onClick = showGroupConfig;
            break;
          case 'canCopy':
            item.onClick = showCopyConfig;
            break;
          case 'canView':
            item.onClick = showMessageDetail;
            break;
          case 'canViewPushResult':
            item.onClick = () => showPushResult(record);
            break;
          case 'canApplyRelease':
          case 'canApplyStopRelease':
          case 'canCancelApplyRelease':
          case 'canCancelApplyStopRelease':
            item.onClick = (id) => handleAction(id, action);
            break;
        }
      });
      return createActions(columns, record, 'product');
    },
  };

  const dict = {
    startType: [
      {
        label: <FormattedMessage id="webOperation_dictionary_immediately_select_text" />,
        value: 1,
      },
      {
        label: <FormattedMessage id="webOperation_dictionary_timeing_select_text" />,
        value: 2,
      },
    ],
    endType: [
      {
        label: <FormattedMessage id="webOperation_dictionary_neverExpires_select_text" />,
        value: 1,
      },
      {
        label: <FormattedMessage id="webOperation_dictionary_timeing_select_text" />,
        value: 2,
      },
    ],
  };

  const initColumns = [...listColumns(dict), actionColumn];
  const allColumns = createColumns('appMessage', [...initColumns, ...extra]);

  return (
    <>
      <Spin spinning={actionLoading}>
        <ResizableTable<API.AppMessageItem, API.AppMessagePageParams>
          actionRef={actionRef}
          formRef={formRef}
          rowKey={type === 'system' ? 'sysMsgId' : 'marketingMsgId'}
          defaultSize="small"
          search={{
            labelWidth: 'auto',
          }}
          headerTitle={
            <MessageConfig
              dict={dict}
              ref={configRef}
              type={type}
              key="config"
              refresh={() => {
                return actionRef.current?.reload();
              }}
            />
          }
          scroll={{ x: 1300 }}
          request={type === 'system' ? getSysMessageList : getMarketingMessageList}
          columns={allColumns}
        />
      </Spin>
      <MessageDetail ref={detailRef} key="detail" dict={dict} />
      <MessageResult ref={resultRef} key="result" type={type} />
      <Modal ref={modalRef} parentRef={actionRef} formRef={modalFormRef} />
    </>
  );
};
export default MessageTemplate;
