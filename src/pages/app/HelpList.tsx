import React, { useRef } from 'react';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { FormattedMessage } from '@@/plugin-locale/localeExports';
import { useIntl } from 'umi';
import styles from './index.less';
import Space from '@/components/Space';
import { AccessButton, AccessLink } from '@/components/Button/AccessButton';
import { rbac } from '@/access';
import { Space as AntSpace } from 'antd';
import ResizableTable from '@/components/Table';
import HelpEdit from './HelpEdit';
import {
  deleteAppQuestion,
  downloadAppQuestionTemplate,
  listAppQuestion,
  syncAppQuestion,
  updateAppQuestionVisible,
} from '@/services/app';
import { get, join, omit } from 'lodash-es';
import dayjs from 'dayjs';
import { createKAC } from '@/components/KeepAlive';
import { compose, timeParams } from '@/hooks/column';
import ImportTemplate from '@/components/Import/index';
const HelpList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const editRef = useRef<APP.RefType<API.AppQuestionItem>>(null);
  const intl = useIntl();

  const columns: ProColumns<API.AppQuestionItem>[] = [
    {
      title: <FormattedMessage id="webOperation_help_id_tableColumn_text" />,
      dataIndex: 'helpFaqId',
      fixed: 'left',
      order: 8,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_help_title_tableColumn_text" />,
      dataIndex: ['title', 'message'],
      order: 7,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_help_model_tableColumn_text" />,
      dataIndex: 'model',
      order: 6,
      width: 150,
      render: (_, record) => join(record.model, ';'),
    },
    {
      title: <FormattedMessage id="webOperation_help_from_tableColumn_text" />,
      dataIndex: 'sourceCode',
      order: 5,
      width: 150,
      valueEnum: {
        1: intl.formatMessage({ id: 'webOperation_dictionary_platformAddition_select_text' }),
        2: intl.formatMessage({ id: 'webOperation_dictionary_salesforceSync_select_text' }),
      },
    },
    {
      title: <FormattedMessage id="webOperation_help_read_tableColumn_text" />,
      dataIndex: 'readCount',
      hideInSearch: true,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_help_like_tableColumn_text" />,
      dataIndex: 'praiseCount',
      hideInSearch: true,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_help_appShowStatus_tableColumn_text" />,
      dataIndex: 'appShowCode',
      order: 4,
      width: 150,
      formItemProps: {
        labelCol: { span: 200 },
      },
      valueEnum: {
        0: intl.formatMessage({ id: 'webOperation_dictionary_hide_select_text' }),
        1: intl.formatMessage({ id: 'webOperation_dictionary_show_select_text' }),
      },
    },
    {
      title: <FormattedMessage id="webOperation_help_sync_tableColumn_text" />,
      dataIndex: 'syncTime',
      valueType: 'dateTime',
      hideInSearch: true,
      width: 150,
    },
    {
      title: <FormattedMessage id="webCommon_page_createBy_tableColumn_text" />,
      dataIndex: 'createBy',
      order: 3,
      width: 150,
    },
    {
      title: <FormattedMessage id="webCommon_page_createTime_tableColumn_text" />,
      dataIndex: 'createTime',
      valueType: 'dateRange',
      order: 10,
      width: 150,
      render: (_, record) =>
        record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') : _,
    },
    {
      title: <FormattedMessage id="webCommon_page_updateBy_tableColumn_text" />,
      dataIndex: 'updateBy',
      order: 2,
      width: 150,
    },
    {
      title: <FormattedMessage id="webCommon_page_updateTime_tableColumn_text" />,
      dataIndex: 'updateTime',
      valueType: 'dateRange',
      order: 9,
      width: 150,
      render: (_, record) =>
        record.updateTime ? dayjs(record.updateTime).format('YYYY-MM-DD HH:mm:ss') : _,
    },
    {
      title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
      width: 200,
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      render: (_: any, record) => (
        <Space>
          {record.appShowCode === '1' ? (
            <AccessLink
              text="webOperation_help_hide_tableColumn_text"
              code={rbac.HELP.VISIBLE}
              onClick={async () => {
                await updateAppQuestionVisible(record.helpFaqId!, false);
                actionRef.current?.reload();
              }}
            />
          ) : (
            <AccessLink
              text="webOperation_help_show_tableColumn_text"
              code={rbac.HELP.VISIBLE}
              onClick={async () => {
                await updateAppQuestionVisible(record.helpFaqId!, true);
                actionRef.current?.reload();
              }}
            />
          )}
          <AccessLink
            text="webCommon_page_detail_common_text"
            code={rbac.HELP.VIEW}
            onClick={() => {
              editRef.current?.edit(record, true);
            }}
          />
          {record.sourceCode === '2' ? null : (
            <AccessLink
              text="webCommon_page_edit_common_text"
              code={rbac.HELP.UPDATE}
              onClick={() => {
                editRef.current?.edit(record);
              }}
            />
          )}
          {record.sourceCode === '2' ? null : (
            <AccessLink
              text="webCommon_page_delete_tableButton_linkText"
              code={rbac.HELP.DELETE}
              onClick={async () => {
                await deleteAppQuestion(record.helpFaqId!);
                actionRef.current?.reload();
              }}
              modal={{
                title: 'webCommon_page_confirmToDelete_modal_title',
                content: (
                  <div style={{ marginBottom: '10px' }}>
                    <div>
                      <FormattedMessage id="webOperation_help_deleteLine1_modal_message" />
                    </div>
                    <div>
                      <FormattedMessage id="webOperation_help_deleteLine2_modal_message" />
                    </div>
                  </div>
                ),
              }}
            />
          )}
        </Space>
      ),
    },
  ];

  const handleSync = async () => {
    await syncAppQuestion();
    actionRef.current?.reload();
  };

  return (
    <ResizableTable<API.AppQuestionItem, API.AppQuestionPageParams>
      rowKey="helpFaqId"
      actionRef={actionRef}
      defaultSize="small"
      scroll={{ x: 150 * 14 + 50 }}
      request={listAppQuestion}
      headerTitle={
        <AntSpace>
          <AccessButton
            text="webOperation_help_update_button_text"
            code={rbac.HELP.SYNC}
            onClick={handleSync}
          />
          <ImportTemplate
            key="import"
            pageName="help"
            buttonName="webCommon_page_import_button_text"
            getTemplate={downloadAppQuestionTemplate}
            url="/operation-platform/help/faq/import"
            urlParam="file"
            refresh={() => {
              return actionRef.current?.reload();
            }}
          />
          <HelpEdit ref={editRef} refresh={() => actionRef.current?.reload()} />
        </AntSpace>
      }
      columns={columns}
      className={styles.tableContent}
      beforeSearchSubmit={(args) => {
        return compose(args, timeParams, (p) => {
          return {
            ...omit(p, 'title'),
            title: get(args, ['title', 'message']),
          };
        });
      }}
    />
  );
};

export default createKAC(HelpList);
