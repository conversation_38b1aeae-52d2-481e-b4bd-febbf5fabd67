import React, { useRef } from 'react';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { FormattedMessage } from '@@/plugin-locale/localeExports';
import styles from './index.less';
import { AccessLink } from '@/components/Button/AccessButton';
import { rbac } from '@/access';
import { Space as AntSpace } from 'antd';
import ResizableTable from '@/components/Table';
import { get, omit } from 'lodash-es';
import { useIntl } from 'umi';
import dayjs from 'dayjs';
import { createKAC } from '@/components/KeepAlive';
import { deleteUpgradeVersion, listUpgradeVersion } from '@/services/upgrade';
import AppUpgradeEdit from '@/pages/app/AppUpgradeEdit';

const AppUpgradeList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const editRef = useRef<APP.RefType<API.AppUpgradeItem>>(null);
  const intl = useIntl();

  const columns: ProColumns<API.AppUpgradeItem>[] = [
    {
      title: <FormattedMessage id="webOperation_appUpgrade_appName_tableColumn_text" />,
      dataIndex: 'businessType',
      order: 8,
      width: 170,
      valueEnum: {
        1: intl.formatMessage({ id: 'webOperation_dictionary_EGOConnect_select_text' }),
        2: intl.formatMessage({ id: 'webOperation_dictionary_fleetService_select_text' }),
      },
    },
    {
      title: <FormattedMessage id="webOperation_appUpgrade_id_tableColumn_text" />,
      dataIndex: 'androidVersionId',
      order: 8,
      width: 170,
    },
    {
      title: <FormattedMessage id="webOperation_appUpgrade_version_tableColumn_text" />,
      dataIndex: 'version',
      order: 7,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_appUpgrade_versionName_tableColumn_text" />,
      dataIndex: ['name', 'message'],
      order: 6,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_appUpgrade_changelog_tableColumn_text" />,
      dataIndex: ['updateContent', 'message'],
      hideInSearch: true,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_appUpgrade_changelogLangId_tableColumn_text" />,
      dataIndex: ['updateContent', 'langId'],
      hideInSearch: true,
      width: 170,
    },
    {
      title: <FormattedMessage id="webCommon_page_createBy_tableColumn_text" />,
      dataIndex: 'createBy',
      hideInSearch: true,
      width: 150,
    },
    {
      title: <FormattedMessage id="webCommon_page_createTime_tableColumn_text" />,
      dataIndex: 'createTime',
      valueType: 'dateRange',
      order: 10,
      width: 150,
      render: (_, record) =>
        record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') : _,
    },
    {
      title: <FormattedMessage id="webCommon_page_updateBy_tableColumn_text" />,
      dataIndex: 'updateBy',
      hideInSearch: true,
      width: 150,
    },
    {
      title: <FormattedMessage id="webCommon_page_updateTime_tableColumn_text" />,
      dataIndex: 'updateTime',
      valueType: 'dateRange',
      order: 9,
      width: 150,
      render: (_, record) =>
        record.updateTime ? dayjs(record.updateTime).format('YYYY-MM-DD HH:mm:ss') : _,
    },
    {
      title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
      width: 150,
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      // align: 'center',
      render: (_: any, record) => (
        <AccessLink
          text="webCommon_page_delete_tableButton_linkText"
          code={rbac.APP_VERSION.DELETE}
          onClick={async () => {
            await deleteUpgradeVersion(record.androidVersionId!);
            actionRef.current?.reload();
          }}
          modal={{
            title: 'webCommon_page_delete_tableButton_linkText',
            content: (
              <div style={{ marginBottom: '10px' }}>
                <div>
                  <FormattedMessage id="webOperation_appUpgrade_deleteLine1_modal_message" />
                </div>
                <div>
                  <FormattedMessage id="webOperation_appUpgrade_deleteLine2_modal_message" />
                </div>
              </div>
            ),
          }}
        />
      ),
    },
  ];

  return (
    <ResizableTable<API.AppUpgradeItem, API.AppUpgradePageParams>
      rowKey="androidVersionId"
      actionRef={actionRef}
      defaultSize="small"
      scroll={{ x: 150 * 10 + 50 }}
      request={listUpgradeVersion}
      headerTitle={
        <AntSpace>
          <AppUpgradeEdit ref={editRef} refresh={() => actionRef.current?.reload()} />
        </AntSpace>
      }
      columns={columns}
      className={styles.tableContent}
      beforeSearchSubmit={(args) => {
        return {
          ...omit(args, ['createTime', 'updateTime', 'androidVersionId', 'name']),
          createStartTime: get(args, ['createTime', 0]),
          createEndTime: get(args, ['createTime', 1]),
          updateStartTime: get(args, ['updateTime', 0]),
          updateEndTime: get(args, ['updateTime', 1]),
          androidVersionId: args.androidVersionId,
          name: get(args, ['name', 'message']),
        };
      }}
    />
  );
};

export default createKAC(AppUpgradeList);
