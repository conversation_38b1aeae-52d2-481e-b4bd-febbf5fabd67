// 框架依赖引入
import React, { useImperativeHandle, useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { Button, message } from 'antd';
import { ProForm, ProFormInstance, ProFormFieldSet, ProFormText } from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-table';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { PlusOutlined } from '@ant-design/icons';
// 公共自定义依赖引入
import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';
import GroupList from '@/pages/product/components/GroupList';
import type { typeColumns } from '@/hooks/column';
import { EditorItem } from '@/components/Form/EditorItem';
import Modal, { RefType as modalRefType } from '@/components/Modal/index';

// 页面自定义依赖引入
import { createProtocol, editProtocol, addNewProtocol } from '@/services/app';
import { addColumns, newColumns, detailColumns } from './columns/ProtocolList';
import { replace } from 'lodash-es';
export interface RefType {
  edit: (record: API.ProtocolItem, method: string, canUpdateTypeAndTitle?: boolean) => void;
}

const ProtocolEdit = React.forwardRef<RefType, APP.EditFormProps>((props, ref) => {
  const { refresh } = props;
  const intl = useIntl();
  const formRef = useRef<ProFormInstance<API.ProtocolItem>>();
  const modalRef = useRef<modalRefType>(null);
  const [visible, setVisible] = useState<boolean>(false);
  const [isReadonly, setIsReadonly] = useState<boolean>(false);
  const [canEditTypeAndTitle, setCanEditTypeAndTitle] = useState<boolean>(true);
  const [method, setMethod] = useState<string>('create');
  const [columns, setColumns] = useState<typeColumns[]>([]);
  const [prdGroupList, setPrdGroupList] = useState<API.GroupItem[]>([]);
  const [testGroupList, setTestGroupList] = useState<API.GroupItem[]>([]);
  const [initialValues, setInitialValues] = useState<API.ProtocolItem>({} as any);
  const [showConfirm, setShowConfirm] = useState({} as any);
  const access = useAccess();

  const { run: handleSubmit, loading } = useRequest(
    async (values) => {
      switch (method) {
        case 'create':
          await createProtocol(values);
          break;
        case 'new':
          values.fromId = initialValues.appAgreementContentId;
          values.appAgreementContentId = initialValues.appAgreementContentId;
          delete values.titleLangId;
          delete values.contentLangId;
          delete values.appAgreementId;
          await addNewProtocol(values);
          break;
        case 'edit':
          values.fromId = initialValues.appAgreementContentId;
          values.appAgreementContentId = initialValues.appAgreementContentId;
          delete values.titleLangId;
          delete values.contentLangId;
          delete values.appAgreementId;
          await editProtocol(values);
          break;
        default:
          break;
      }
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setVisible(false);
      refresh?.();
    },
    { manual: true },
  );

  const onClose = () => {
    setVisible(false);
  };

  const submit = () => {
    const content = formRef.current?.getFieldValue('content');
    // 通过校验contentInfo字段来校检所输的协议内容不为空
    formRef.current?.setFieldsValue({ contentInfo: content });
    formRef.current?.submit();
  };
  const deleteProductGroup = (index: number) => {
    const oldValue = [...prdGroupList];
    oldValue.splice(index, 1);
    setPrdGroupList(oldValue);
    formRef.current?.setFieldsValue({ prdGroup: oldValue.map((item) => item.groupName) });
  };

  const deleteTestGroup = (index: number) => {
    const oldValue = [...testGroupList];
    oldValue.splice(index, 1);
    setTestGroupList(oldValue);
    formRef.current?.setFieldsValue({ testGroup: oldValue.map((item) => item.groupName) });
  };

  const productColumns: ProColumns[] = [
    {
      title: <FormattedMessage id="webOperation_group_groupName_tableColumn_text" />,
      dataIndex: 'groupName',
      width: 350,
    },
    {
      title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
      dataIndex: 'option',
      valueType: 'option',
      width: 100,
      render: (_, record, index) => (
        <Access accessible={access.canDeleteProGroupAppProtocol()}>
          <a
            key="delete"
            onClick={() => {
              deleteProductGroup(index);
            }}
          >
            <FormattedMessage id="webCommon_page_delete_tableButton_linkText" />
          </a>
        </Access>
      ),
    },
  ];

  const testColumns: ProColumns[] = [
    {
      title: <FormattedMessage id="webOperation_group_groupName_tableColumn_text" />,
      dataIndex: 'groupName',
      width: 350,
    },
    {
      title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
      dataIndex: 'option',
      valueType: 'option',
      width: 100,
      render: (_, record, index) => (
        <Access accessible={access.canDeleteTestGroupAppProtocol()}>
          <a
            key="delete"
            onClick={() => {
              deleteTestGroup(index);
            }}
          >
            <FormattedMessage id="webCommon_page_delete_tableButton_linkText" />
          </a>
        </Access>
      ),
    },
  ];
  const editColumns: typeColumns[] = [
    {
      dataIndex: 'appAgreementId',
      langCode: 'webOperation_protocol_appAgreementContentId_tableColumn_text',
      showInForm: true,
      readonly: true,
    },
    {
      dataIndex: 'typeCode',
      showInForm: true,
      required: true,
      valueType: 'select',
      readonly: !canEditTypeAndTitle,
      valueEnum: {
        secret: intl.formatMessage({
          id: 'webOperation_dictionary_secret_select_text',
        }),
        user: intl.formatMessage({
          id: 'webOperation_dictionary_user_select_text',
        }),
      },
    },
    {
      dataIndex: 'title',
      showInForm: true,
      required: true,
      readonly: !canEditTypeAndTitle,
    },
    {
      dataIndex: 'titleLangId',
      showInForm: true,
      readonly: true,
    },
    {
      dataIndex: 'version',
      showInForm: true,
      required: true,
    },
    {
      dataIndex: 'businessType',
      showInForm: true,
      required: true,
      valueType: 'select',
      valueEnum: {
        2: intl.formatMessage({
          id: 'webOperation_dictionary_FleetAPP_select_text',
        }),
        3: intl.formatMessage({
          id: 'webOperation_dictionary_FleetWEB_select_text',
        }),
        1: intl.formatMessage({
          id: 'webOperation_dictionary_EGOAPP_select_text',
        }),
      },
    },
  ];

  useImperativeHandle(ref, () => ({
    edit: (record: API.ProtocolItem, mode: string, canUpdateTypeAndTitle?: boolean) => {
      // 设置页面表单功能模式
      setMethod(mode);
      // 转换成编辑表单中分组表列格式数据
      const proGroup = record.prdGroup?.map((item) => ({ groupName: item }));
      const testGroup = record.testGroup?.map((item) => ({ groupName: item }));
      setPrdGroupList(proGroup || []);
      setTestGroupList(testGroup || []);
      switch (mode) {
        // 编辑
        case 'edit':
          setShowConfirm({ onConfirm: submit });
          setColumns(editColumns);
          setIsReadonly(false);
          setCanEditTypeAndTitle(canUpdateTypeAndTitle!);
          break;
        // 添加新版本协议
        case 'new':
          // record.content = '';
          // record.version = '';
          // IOT-12771修复
          record.content = formatRichText(record.content!);
          setShowConfirm({ onConfirm: submit });
          setColumns(newColumns);
          setIsReadonly(false);
          setCanEditTypeAndTitle(true);
          break;
        // 协议详情
        case 'detail':
          record.prdGroupList = record.prdGroup?.join('，');
          record.testGroupList = record.testGroup?.join('，');
          record.content = formatRichText(record.content!);
          // 查看隐藏确定按钮
          setShowConfirm({ hiddenConfirm: true });
          setColumns(detailColumns);
          setIsReadonly(true);
          setCanEditTypeAndTitle(false);
          break;
      }
      record.businessType = String(record.businessType);
      // 设置表单字段初始值
      setInitialValues(record || {});
      setVisible(true);
    },
  }));

  const formatRichText = (content: string) => {
    let res = replace(
      content,
      /<strong><span style="color:black">.<\/span><\/strong>/gi,
      '<span style="color:black">.</span>',
    );
    res = replace(res, /<strong> <\/strong>/gi, '');
    return res;
  };
  const updatePrdGroup = (list: API.GroupItem[]) => {
    const newList = [...list, ...prdGroupList];
    setPrdGroupList(newList);
    formRef.current?.setFieldsValue({ prdGroup: newList.map((item) => item.groupName) });
  };

  const updateTestGroup = (list: API.GroupItem[]) => {
    const newList = [...list, ...testGroupList];
    setTestGroupList([...list, ...testGroupList]);
    formRef.current?.setFieldsValue({ testGroup: newList.map((item) => item.groupName) });
  };

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: `webOperation_protocol_${method}_drawer_title` })}
        width="50%"
        onCancel={onClose}
        {...showConfirm}
        open={visible}
        destroyOnClose={true}
        loading={loading}
      >
        <ProForm
          initialValues={initialValues}
          onFinish={handleSubmit}
          formRef={formRef}
          layout="horizontal"
          labelCol={{ span: 5 }}
          onReset={onClose}
          submitter={false}
        >
          <FormFields columns={columns} pageName="protocol" />
          {method !== 'detail' ? (
            <ProForm.Item
              name="prdGroup"
              label={intl.formatMessage({ id: 'webOperation_release_prdGroup_tableColumn_text' })}
            >
              <ProTable<API.GroupItem, API.PageParams>
                rowKey={(record) => record.groupName}
                defaultSize="small"
                search={false}
                options={false}
                columns={productColumns}
                pagination={false}
                headerTitle={
                  <GroupList
                    key="prdGroup"
                    moduleName="ProAppProtocol"
                    selectedKeys={prdGroupList}
                    setData={updatePrdGroup}
                  />
                }
                dataSource={prdGroupList}
              />
            </ProForm.Item>
          ) : null}
          {method !== 'detail' ? (
            <ProForm.Item
              name="testGroup"
              label={intl.formatMessage({ id: 'webOperation_release_testGroup_tableColumn_text' })}
              rules={[
                {
                  required: true,
                  message:
                    intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                    intl.formatMessage({ id: 'webOperation_release_testGroup_tableColumn_text' }),
                },
              ]}
            >
              <ProTable<API.GroupItem, API.PageParams>
                rowKey={(record) => record.groupName}
                defaultSize="small"
                search={false}
                options={false}
                columns={testColumns}
                pagination={false}
                headerTitle={
                  <GroupList
                    key="testGroup"
                    moduleName="TestAppProtocol"
                    selectedKeys={testGroupList}
                    setData={updateTestGroup}
                  />
                }
                dataSource={testGroupList}
              />
            </ProForm.Item>
          ) : null}
          <ProFormFieldSet
            name="contentInfo"
            label={intl.formatMessage({
              id: 'webOperation_protocol_content_tableColumn_text',
            })}
            rules={
              method !== 'detail'
                ? [
                    {
                      required: true,
                      message:
                        intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                        intl.formatMessage({
                          id: 'webOperation_protocol_content_tableColumn_text',
                        }),
                    },
                  ]
                : []
            }
            transform={(value: any) => ({})}
          >
            <ProForm.Item name="content" label={false}>
              <EditorItem readonly={isReadonly} />
            </ProForm.Item>
          </ProFormFieldSet>
          {method === 'edit' || method === 'detail' ? (
            <ProFormText
              name="contentLangId"
              label={intl.formatMessage({
                id: 'webOperation_protocol_contentLangId_tableColumn_text',
              })}
              readonly={true}
            />
          ) : null}
        </ProForm>
      </FormContainer>
      <Access accessible={access.canCreateAppProtocol()}>
        <Button
          type="primary"
          onClick={() => {
            // 页面表单功能为添加新协议
            setMethod('create');
            // 创建时的表单字段
            setColumns(addColumns);
            // 富文本组件可编辑
            setIsReadonly(false);
            setInitialValues({} as any);
            // form表单提交方法
            setShowConfirm({ onConfirm: submit });
            setVisible(true);
            setCanEditTypeAndTitle(true);
            setPrdGroupList([]);
            setTestGroupList([]);
          }}
        >
          <PlusOutlined />
          <FormattedMessage id="webOperation_protocol_create_button_text" />
        </Button>
      </Access>
      <Modal ref={modalRef} />
    </>
  );
});

export default ProtocolEdit;
