// 框架依赖引入
import type { ActionType } from '@ant-design/pro-components';
import { ProFormInstance } from '@ant-design/pro-components';
import type { typeColumns } from '@/hooks/column';
import { Button } from 'antd';
import { Access } from '@@/plugin-access/access';
import React, { useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
// 自定义公共依赖引入
import Modal, { RefType } from '@/components/Modal/index';
import ResizableTable from '@/components/Table';
import { useColumn } from '@/hooks/column';
import { useCategories, useBrands } from '@/hooks/selectHooks';
import KeepAlive from '@/components/KeepAlive';
import useExport from '@/hooks/useExport';
// 页面自定义依赖引入
import { getUserDeviceList, exportUserDevices } from '@/services/app';
import { listColumns } from './columns/UserDevice';
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import { EMAIL_REGEX } from './constants';

type UserDeviceItemWithDesensitizedFlag = API.UserDeviceItem & {
  /**
   * 是否显示脱敏
   */
  isDesensitized: boolean;
};

const UserDevice: React.FC = () => {
  const access = useAccess();
  const actionRef = useRef<ActionType>();
  const modalRef = useRef<RefType>(null);
  const formRef = useRef<ProFormInstance>();
  const intl = useIntl();
  const { valueEnum: categoryValueEnum } = useCategories();
  const { valueEnum: brandValueEnum } = useBrands();
  const { createColumns } = useColumn();
  // 增加脱敏的标志，控制表格切换显示脱敏数据
  const [deviceListWithDesensitizedFlag, setDeviceListWithDesensitizedFlag] = useState<
    UserDeviceItemWithDesensitizedFlag[]
  >([]);

  const { run: handleExport, loading: exportLoading } = useExport(
    exportUserDevices,
    formRef,
    intl.formatMessage({ id: 'webOperation_userdevice_export_fileName_text' }),
  );

  const toDesensitizedColumns: typeColumns[] = [
    {
      dataIndex: 'email',
      width: 160,
      render: (_: any, record: UserDeviceItemWithDesensitizedFlag) => {
        return record.isDesensitized
          ? record.email?.replace(
              EMAIL_REGEX,
              ($0: string, $1: string, $2: string) =>
                `${$1.charAt(0)}${'*'.repeat($1.length - 1)}${$2}`,
            )
          : record.email;
      },
    },
    {
      dataIndex: 'userId',
      width: 170,
      render: (_: any, record: UserDeviceItemWithDesensitizedFlag) => {
        return record.isDesensitized
          ? record.userId?.replace(
              /^(\d{7})(\d+)(\d{8})$/,
              ($0: string, $1: string, $2: string, $3: string) =>
                `${$1}${'*'.repeat($2.length)}${$3}`,
            )
          : record.userId;
      },
    },
  ];

  const operationColumns: typeColumns[] = [
    {
      title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
      width: 50,
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      render: (_: any, record: UserDeviceItemWithDesensitizedFlag) => {
        const onSwitchDesensitizedFlag = () => {
          let list = [...deviceListWithDesensitizedFlag];
          for (const item of list) {
            if (item.id === record.id) {
              item.isDesensitized = !item.isDesensitized;
              setDeviceListWithDesensitizedFlag(list);
              break;
            }
          }
        };
        return record.isDesensitized ? (
          <EyeOutlined onClick={onSwitchDesensitizedFlag} />
        ) : (
          <EyeInvisibleOutlined onClick={onSwitchDesensitizedFlag} />
        );
      },
    },
  ];

  const allColumns = [
    ...toDesensitizedColumns,
    ...listColumns({ categoryValueEnum, brandValueEnum }),
    ...operationColumns,
  ];

  const columns = createColumns('user', allColumns);

  return (
    <>
      <ResizableTable<UserDeviceItemWithDesensitizedFlag, API.UserDevicePageParams>
        actionRef={actionRef}
        formRef={formRef}
        rowKey="id"
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        headerTitle={
          <Access accessible={access.canExportUserDevice()}>
            <Button
              loading={exportLoading}
              type="primary"
              onClick={async () => {
                const res = await handleExport();
                if (res.type === 'application/json') {
                  modalRef.current?.setProps({
                    okButtonProps: { style: { display: 'none' } },
                    cancelText: intl.formatMessage({
                      id: 'webCommon_page_confirmText_button_text',
                    }),
                  });
                  modalRef.current?.open({
                    title: 'webCommon_page_exportFailed_modal_title',
                    msg:
                      intl.formatMessage({ id: 'webOperation_userdevice_export_fileName_text' }) +
                      intl.formatMessage({ id: 'webCommon_page_exportFailed_modal_message' }),
                  });
                }
              }}
            >
              <FormattedMessage id="webCommon_page_export_button_text" />
            </Button>
          </Access>
        }
        scroll={{ x: 1300 }}
        request={async (params) => {
          const deviceList = await getUserDeviceList(params);
          const desensitizedDeviceList = deviceList.data.map(
            (item: UserDeviceItemWithDesensitizedFlag) => ({
              ...item,
              isDesensitized: true,
            }),
          );
          setDeviceListWithDesensitizedFlag(desensitizedDeviceList);
          return {
            data: desensitizedDeviceList,
            success: true,
            total: deviceList.total,
          };
        }}
        columns={columns}
      />
      <Modal ref={modalRef} parentRef={actionRef} />
    </>
  );
};
export default () => {
  return (
    <KeepAlive>
      <UserDevice />
    </KeepAlive>
  );
};
