import type { typeColumns } from '@/hooks/column';
import { dateFilter, creatorFilter, fieldProps } from '@/hooks/column';
import { ProFormDependency } from '@ant-design/pro-components';
import { FormattedMessage, getIntl } from '@@/plugin-locale/localeExports';
import { zone } from '@/utils/constant';

export const listColumns = (dict: { [key: string]: any }): typeColumns[] => [
  ...dateFilter,
  {
    dataIndex: 'pushStartTime',
    valueType: 'dateRange',
    hideInTable: true,
    showLabel: true,
    langCode: 'webOperation_appMessage_startTime_tableColumn_text',
    fieldProps,
  },
  {
    dataIndex: 'pushEndTime',
    valueType: 'dateRange',
    hideInTable: true,
    showLabel: true,
    fieldProps,
    langCode: 'webOperation_appMessage_endTime_tableColumn_text',
  },
  {
    dataIndex: 'sysMsgId',
    width: 170,
    hideInSearch: true,
    render: (_, record) => record.sysMsgId ?? record.marketingMsgId,
  },
  {
    dataIndex: 'msgId',
    hideInTable: true,
    langCode: 'webOperation_appMessage_sysMsgId_tableColumn_text',
  },
  {
    dataIndex: 'title',
    width: 150,
  },
  {
    dataIndex: 'pushTypeCodes',
    width: 150,
    valueEnum: {
      POPUP: <FormattedMessage id="webOperation_dictionary_popup_select_text" />,
      MESSAGE: <FormattedMessage id="webOperation_dictionary_message_select_text" />,
      BANNER: <FormattedMessage id="webOperation_dictionary_banner_select_text" />,
      MAIL: <FormattedMessage id="webOperation_dictionary_mail_select_text" />,
      TOMBSTONE: <FormattedMessage id="webOperation_dictionary_tombstone_select_text" />,
      PHONE_VOICE: <FormattedMessage id="webOperation_dictionary_phoneVoice_select_text" />,
    },
  },
  {
    dataIndex: 'startTime',
    width: 170,
    hideInSearch: true,
    render: (_, record) => {
      if (record.startType === 1) {
        const start = dict['startType']?.find(
          (item: API.selectOptions) => item.value === String(record.startType),
        );
        return start?.label || '';
      } else {
        return record.startZone ? record.startZone + ' ' + record.startTime : '';
      }
    },
  },
  {
    dataIndex: 'endTime',
    width: 170,
    hideInSearch: true,
    render: (_, record) => {
      if (record.endType === 1) {
        const end = dict['endType']?.find(
          (item: API.selectOptions) => item.value === String(record.endType),
        );
        return end?.label || '';
      } else {
        return record.endZone ? record.endZone + ' ' + record.endTime : '';
      }
    },
  },
  {
    dataIndex: 'statusCode',
    width: 150,
    valueEnum: {
      release_refused: getIntl().formatMessage({
        id: 'webOperation_dictionary_releaseRefused_select_text',
      }),
      stop_released: getIntl().formatMessage({
        id: 'webOperation_dictionary_stopReleased_select_text',
      }),
      will_release: getIntl().formatMessage({
        id: 'webOperation_dictionary_willRelease_select_text',
      }),
      release_verify_ing: getIntl().formatMessage({
        id: 'webOperation_dictionary_releaseVerifyIng_select_text',
      }),
      test_check_ing: getIntl().formatMessage({
        id: 'webOperation_dictionary_testCheckIng_select_text',
      }),
      stop_release_verify_ing: getIntl().formatMessage({
        id: 'webOperation_dictionary_stopReleaseVerifyIng_select_text',
      }),
      stop_release_refused: getIntl().formatMessage({
        id: 'webOperation_dictionary_stopReleaseRefused_select_text',
      }),
      test_refused: getIntl().formatMessage({
        id: 'webOperation_dictionary_testRefused_select_text',
      }),
      released: getIntl().formatMessage({
        id: 'webOperation_dictionary_released_select_text',
      }),
    },
  },
  ...creatorFilter,
];
export const newColumns: typeColumns[] = [
  {
    dataIndex: 'title',
    showInForm: true,
    required: true,
  },
  {
    dataIndex: 'content',
    showInForm: true,
    valueType: 'textarea',
  },
  {
    dataIndex: 'rutePath',
    showInForm: true,
  },
];

export const editColumns: typeColumns[] = [
  {
    dataIndex: 'sysMsgId',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'title',
    showInForm: true,
    required: true,
    addonRender: (
      <ProFormDependency name={['titleLangId']}>
        {({ titleLangId }) => {
          return (
            <span>
              <FormattedMessage id="webCommon_page_langId_common_text" />
              {titleLangId}
            </span>
          );
        }}
      </ProFormDependency>
    ),
  },
  {
    dataIndex: 'content',
    showInForm: true,
    valueType: 'textarea',
    extraRender: (
      <ProFormDependency name={['titleLangId']}>
        {({ titleLangId }) => {
          return (
            <span>
              <FormattedMessage id="webCommon_page_langId_common_text" />
              {titleLangId}
            </span>
          );
        }}
      </ProFormDependency>
    ),
  },
  {
    dataIndex: 'rutePath',
    showInForm: true,
  },
];

export const pushColumns: typeColumns[] = [
  {
    dataIndex: 'pushTypeCodes',
    showInForm: true,
    required: true,
    valueType: 'checkbox',
    valueEnum: {
      POPUP: <FormattedMessage id="webOperation_dictionary_popup_select_text" />,
      MESSAGE: <FormattedMessage id="webOperation_dictionary_message_select_text" />,
      BANNER: <FormattedMessage id="webOperation_dictionary_banner_select_text" />,
      MAIL: <FormattedMessage id="webOperation_dictionary_mail_select_text" />,
      TOMBSTONE: <FormattedMessage id="webOperation_dictionary_tombstone_select_text" />,
      PHONE_VOICE: <FormattedMessage id="webOperation_dictionary_phoneVoice_select_text" />,
    },
  },
  {
    dataIndex: 'pushRateCode',
    showInForm: true,
    required: true,
    valueType: 'radio',
    valueEnum: {
      0: <FormattedMessage id="webOperation_dictionary_onlyOnce_select_text" />,
      1: <FormattedMessage id="webOperation_dictionary_onceAfterAppLaunch_select_text" />,
      2: <FormattedMessage id="webOperation_dictionary_onceADay_select_text" />,
    },
  },
];

export const detailColumns: typeColumns[] = [
  {
    dataIndex: 'sysMsgId',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'title',
    showInForm: true,
    readonly: true,
    extraRender: (
      <ProFormDependency name={['titleLangId']}>
        {({ titleLangId }) => {
          return (
            <span>
              <FormattedMessage id="webCommon_page_langId_common_text" />
              {titleLangId}
            </span>
          );
        }}
      </ProFormDependency>
    ),
  },
  {
    dataIndex: 'content',
    showInForm: true,
    readonly: true,
    extraRender: (
      <ProFormDependency name={['contentLangId']}>
        {({ contentLangId }) => {
          return (
            <span>
              <FormattedMessage id="webCommon_page_langId_common_text" />
              {contentLangId}
            </span>
          );
        }}
      </ProFormDependency>
    ),
  },
  {
    dataIndex: 'rutePath',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'prdGroup',
    langCode: 'webOperation_release_prdGroup_tableColumn_text',
    showInForm: true,
    readonly: true,
    valueType: 'select',
  },
  {
    dataIndex: 'testGroup',
    langCode: 'webOperation_release_testGroup_tableColumn_text',
    showInForm: true,
    readonly: true,
    valueType: 'select',
  },
  {
    dataIndex: 'pushTypeCodes',
    showInForm: true,
    readonly: true,
    valueType: 'select',
    valueEnum: {
      POPUP: <FormattedMessage id="webOperation_dictionary_popup_select_text" />,
      MESSAGE: <FormattedMessage id="webOperation_dictionary_message_select_text" />,
      BANNER: <FormattedMessage id="webOperation_dictionary_banner_select_text" />,
      MAIL: <FormattedMessage id="webOperation_dictionary_mail_select_text" />,
      TOMBSTONE: <FormattedMessage id="webOperation_dictionary_tombstone_select_text" />,
      PHONE_VOICE: <FormattedMessage id="webOperation_dictionary_phoneVoice_select_text" />,
    },
  },
  {
    dataIndex: 'pushRateCode',
    showInForm: true,
    readonly: true,
    valueType: 'select',
    valueEnum: {
      0: <FormattedMessage id="webOperation_dictionary_onlyOnce_select_text" />,
      1: <FormattedMessage id="webOperation_dictionary_onceAfterAppLaunch_select_text" />,
      2: <FormattedMessage id="webOperation_dictionary_onceADay_select_text" />,
    },
  },
  {
    dataIndex: 'startTime',
    langCode: 'webOperation_release_start_tableColumn_text',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'endTime',
    langCode: 'webOperation_release_end_tableColumn_text',
    showInForm: true,
    readonly: true,
  },
];

export const startColumns: typeColumns[] = [
  {
    dataIndex: 'startZone',
    langCode: 'webOperation_release_zone_tableColumn_text',
    width: 150,
    showInForm: true,
    required: true,
    valueType: 'select',
    valueEnum: zone,
  },
  {
    dataIndex: 'startDate',
    langCode: 'webOperation_release_date_tableColumn_text',
    width: 150,
    showInForm: true,
    required: true,
    valueType: 'date',
  },
  {
    dataIndex: 'startTime',
    langCode: 'webOperation_release_time_tableColumn_text',
    width: 150,
    showInForm: true,
    required: true,
    valueType: 'time',
  },
];

export const endColumns: typeColumns[] = [
  {
    dataIndex: 'endZone',
    langCode: 'webOperation_release_zone_tableColumn_text',
    width: 150,
    showInForm: true,
    required: true,
    valueType: 'select',
    valueEnum: zone,
  },
  {
    dataIndex: 'endDate',
    langCode: 'webOperation_release_date_tableColumn_text',
    width: 150,
    showInForm: true,
    required: true,
    valueType: 'date',
  },
  {
    dataIndex: 'endTime',
    langCode: 'webOperation_release_time_tableColumn_text',
    width: 150,
    showInForm: true,
    required: true,
    valueType: 'time',
  },
];

export const resultColumns: typeColumns[] = [
  {
    dataIndex: 'sysMsgId',
    showInForm: true,
    readonly: true,
    hideInSearch: true,
    hideInTable: true,
  },
  {
    dataIndex: 'title',
    showInForm: true,
    readonly: true,
    hideInSearch: true,
    hideInTable: true,
  },
  {
    dataIndex: 'pushType',
    width: 150,
    langCode: 'webOperation_message_pushType_tableColumn_text',
    valueEnum: {
      POPUP: <FormattedMessage id="webOperation_dictionary_popup_select_text" />,
      MESSAGE: <FormattedMessage id="webOperation_dictionary_message_select_text" />,
      BANNER: <FormattedMessage id="webOperation_dictionary_banner_select_text" />,
      MAIL: <FormattedMessage id="webOperation_dictionary_mail_select_text" />,
      TOMBSTONE: <FormattedMessage id="webOperation_dictionary_tombstone_select_text" />,
      PHONE_VOICE: <FormattedMessage id="webOperation_dictionary_phoneVoice_select_text" />,
    },
  },
  {
    dataIndex: 'pushTime',
    width: 150,
    valueType: 'dateTime',
    hideInSearch: true,
  },
  {
    dataIndex: 'userId',
    width: 170,
    langCode: 'webOperation_message_userId_tableColumn_text',
  },
  {
    dataIndex: 'pushResult',
    width: 150,
    valueType: 'select',
    langCode: 'webOperation_message_result_tableColumn_text',
    valueEnum: {
      true: <FormattedMessage id="webOperation_dictionary_true_select_text" />,
      false: <FormattedMessage id="webOperation_dictionary_false_select_text" />,
    },
  },
];
