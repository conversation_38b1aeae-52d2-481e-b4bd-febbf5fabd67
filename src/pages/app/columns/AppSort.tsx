import type { typeColumns } from '@/hooks/column';
import { getIntl } from 'umi';

export const listColumns: typeColumns[] = [
  {
    dataIndex: 'itemCode',
    showInForm: true,
    readonly: true,
    valueType: 'select',
    valueEnum: {
      1: getIntl().formatMessage({ id: 'webOperation_dictionary_category_select_text' }),
      2: getIntl().formatMessage({ id: 'webOperation_dictionary_mode_select_text' }),
      3: getIntl().formatMessage({
        id: 'webOperation_dictionary_fleetServiceCategory_select_text',
      }),
      4: getIntl().formatMessage({ id: 'webOperation_dictionary_fleetServiceMode_select_text' }),
    },
  },
  {
    dataIndex: 'updateBy',
    langCode: 'webCommon_page_updateBy_tableColumn_text',
  },
  {
    dataIndex: 'updateTime',
    langCode: 'webCommon_page_updateTime_tableColumn_text',
    valueType: 'dateTime',
  },
];
