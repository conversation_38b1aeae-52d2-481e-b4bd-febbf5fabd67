import type { typeColumns } from '@/hooks/column';
import { map } from 'lodash-es';
import { getIntl } from 'umi';

interface Parmas {
  categoryValueEnum: Record<string, string>;
  brandValueEnum: Record<string, string>;
}
export const listColumns = ({ categoryValueEnum, brandValueEnum }: Parmas): typeColumns[] => [
  {
    dataIndex: 'bindDeviceId',
    width: 170,
    ellipsis: {
      showTitle: false,
    },
    render: (_, record) =>
      map(record.bindDeviceId, (item, index) => (
        <a
          key={item + index}
          onClick={() => {
            window.router.push({ path: `/iot/device/${item}` });
          }}
          style={{ display: 'block' }}
        >
          {item}
        </a>
      )),
  },
  {
    dataIndex: 'deviceSource',
    width: 90,
    isRender: true,
    valueEnum: {
      app: getIntl().formatMessage({ id: 'webOperation_dictionary_app_select_text' }),
      other: getIntl().formatMessage({ id: 'webOperation_dictionary_other_select_text' }),
      web: getIntl().formatMessage({ id: 'webOperation_dictionary_web_select_text' }),
    },
  },
  {
    dataIndex: 'bindDeviceSn',
    width: 150,
    ellipsis: {
      showTitle: false,
    },
    render: (_, record) => map(record.bindDeviceSn, (item) => <div>{item ? item : '-'}</div>),
  },
  {
    dataIndex: 'bindDeviceCategoryId',
    width: 180,
    isRender: true,
    valueEnum: categoryValueEnum,
  },
  {
    dataIndex: 'bindDeviceBrandId',
    width: 120,
    isRender: true,
    valueEnum: brandValueEnum,
  },
  {
    dataIndex: 'model',
    width: 150,
    ellipsis: {
      showTitle: false,
    },
    render: (_, record) => map(record.model, (item) => <div>{item ? item : '-'}</div>),
  },
  {
    dataIndex: 'shareType',
    width: 120,
    isRender: true,
    valueEnum: {
      0: getIntl().formatMessage({ id: 'webOperation_dictionary_none_select_text' }),
      1: getIntl().formatMessage({ id: 'webOperation_dictionary_mainAccount_select_text' }),
      2: getIntl().formatMessage({ id: 'webOperation_dictionary_subAccount_select_text' }),
    },
    hideInSearch: true,
  },
  {
    dataIndex: 'commodityModel',
    width: 210,
    ellipsis: {
      showTitle: false,
    },
    render: (_, record) => map(record.commodityModel, (item) => <div>{item ? item : '-'}</div>),
  },
];
