import type { typeColumns } from '@/hooks/column';
import { getIntl } from 'umi';

export const listColumns: typeColumns[] = [
  {
    dataIndex: 'appPresenceCode',
    width: 150,
    valueEnum: {
      offline: getIntl().formatMessage({ id: 'webOperation_dictionary_offline_select_text' }),
      online: getIntl().formatMessage({ id: 'webOperation_dictionary_online_select_text' }),
    },
  },
  {
    dataIndex: 'lastLoginTime',
    width: 150,
    hideInSearch: true,
    valueType: 'dateTime',
  },
  {
    dataIndex: 'ip',
    width: 150,
    hideInSearch: true,
  },
  {
    dataIndex: 'phoneModel',
    width: 150,
  },
  {
    dataIndex: 'phoneOsVersion',
    width: 150,
    hideInSearch: true,
  },
  {
    dataIndex: 'appTypeCode',
    width: 150,
    valueEnum: {
      android: getIntl().formatMessage({ id: 'webOperation_dictionary_android_select_text' }),
      ios: getIntl().formatMessage({ id: 'webOperation_dictionary_ios_select_text' }),
    },
  },
  {
    dataIndex: 'appVersion',
    width: 150,
  },
  {
    dataIndex: 'userTypeCode',
    width: 150,
    valueEnum: {
      new: getIntl().formatMessage({ id: 'webOperation_dictionary_newUser_select_text' }),
      old: getIntl().formatMessage({ id: 'webOperation_dictionary_oldUser_select_text' }),
    },
  },
  {
    dataIndex: 'userSourceCode',
    width: 150,
    valueEnum: {
      sf: getIntl().formatMessage({
        id: 'webOperation_dictionary_overseasOfficialWebsite_select_text',
      }),
      ego: getIntl().formatMessage({ id: 'webOperation_dictionary_EGOAPP_select_text' }),
    },
  },
  {
    dataIndex: 'registerTime',
    width: 150,
    hideInSearch: true,
    valueType: 'dateTime',
  },
];
