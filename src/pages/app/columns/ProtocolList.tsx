import type { typeColumns } from '@/hooks/column';
import { extra } from '@/hooks/column';
import { getIntl } from 'umi';

export const listColumns: typeColumns[] = [
  {
    dataIndex: 'appAgreementId',
    langCode: 'webOperation_protocol_appAgreementContentId_tableColumn_text',
    width: 180,
    hideInSearch: true,
    showInForm: true,
  },
  {
    dataIndex: 'title',
    width: 150,
    showInForm: true,
  },
  {
    dataIndex: 'titleLangId',
    hideInTable: true,
    hideInSearch: true,
    showInForm: true,
  },
  {
    dataIndex: 'version',
    width: 150,
    showInForm: true,
  },
  {
    dataIndex: 'typeCode',
    width: 150,
    showInForm: true,
    valueType: 'select',
    valueEnum: {
      secret: getIntl().formatMessage({
        id: 'webOperation_dictionary_secret_select_text',
      }),
      user: getIntl().formatMessage({
        id: 'webOperation_dictionary_user_select_text',
      }),
    },
  },
  {
    dataIndex: 'businessType',
    width: 150,
    showInForm: true,
    valueType: 'select',
    valueEnum: {
      2: getIntl().formatMessage({
        id: 'webOperation_dictionary_FleetAPP_select_text',
      }),
      3: getIntl().formatMessage({
        id: 'webOperation_dictionary_FleetWEB_select_text',
      }),
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_EGOAPP_select_text',
      }),
    },
  },
  {
    dataIndex: 'statusCode',
    width: 150,
    showInForm: true,
    hideInSearch: true,
    readonly: true,
    valueEnum: {
      release_refused: getIntl().formatMessage({
        id: 'webOperation_dictionary_releaseRefused_select_text',
      }),
      stop_released: getIntl().formatMessage({
        id: 'webOperation_dictionary_stopReleased_select_text',
      }),
      will_release: getIntl().formatMessage({
        id: 'webOperation_dictionary_willRelease_select_text',
      }),
      release_verify_ing: getIntl().formatMessage({
        id: 'webOperation_dictionary_releaseVerifyIng_select_text',
      }),
      test_check_ing: getIntl().formatMessage({
        id: 'webOperation_dictionary_testCheckIng_select_text',
      }),
      stop_release_verify_ing: getIntl().formatMessage({
        id: 'webOperation_dictionary_stopReleaseVerifyIng_select_text',
      }),
      stop_release_refused: getIntl().formatMessage({
        id: 'webOperation_dictionary_stopReleaseRefused_select_text',
      }),
      test_refused: getIntl().formatMessage({
        id: 'webOperation_dictionary_testRefused_select_text',
      }),
      released: getIntl().formatMessage({
        id: 'webOperation_dictionary_released_select_text',
      }),
    },
  },
  ...extra,
  {
    dataIndex: 'prdGroup',
    langCode: 'webOperation_release_prdGroup_tableColumn_text',
    showInForm: true,
    hideInTable: true,
    hideInSearch: true,
    readonly: true,
  },
  {
    dataIndex: 'testGroup',
    langCode: 'webOperation_release_testGroup_tableColumn_text',
    showInForm: true,
    hideInTable: true,
    hideInSearch: true,
    readonly: true,
  },
];

export const addColumns: typeColumns[] = [
  {
    dataIndex: 'typeCode',
    showInForm: true,
    required: true,
    valueType: 'select',
    valueEnum: {
      secret: getIntl().formatMessage({
        id: 'webOperation_dictionary_secret_select_text',
      }),
      user: getIntl().formatMessage({
        id: 'webOperation_dictionary_user_select_text',
      }),
    },
  },
  {
    dataIndex: 'title',
    showInForm: true,
    required: true,
  },
  {
    dataIndex: 'version',
    showInForm: true,
    required: true,
  },
  {
    dataIndex: 'businessType',
    showInForm: true,
    required: true,
    valueType: 'select',
    valueEnum: {
      2: getIntl().formatMessage({
        id: 'webOperation_dictionary_FleetAPP_select_text',
      }),
      3: getIntl().formatMessage({
        id: 'webOperation_dictionary_FleetWEB_select_text',
      }),
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_EGOAPP_select_text',
      }),
    },
  },
];

export const editColumns: typeColumns[] = [
  {
    dataIndex: 'appAgreementId',
    langCode: 'webOperation_protocol_appAgreementContentId_tableColumn_text',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'typeCode',
    showInForm: true,
    required: true,
    valueType: 'select',
    readonly: true,
    valueEnum: {
      secret: getIntl().formatMessage({
        id: 'webOperation_dictionary_secret_select_text',
      }),
      user: getIntl().formatMessage({
        id: 'webOperation_dictionary_user_select_text',
      }),
    },
  },
  {
    dataIndex: 'title',
    showInForm: true,
    required: true,
    readonly: true,
  },
  {
    dataIndex: 'titleLangId',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'version',
    showInForm: true,
    required: true,
  },
  {
    dataIndex: 'businessType',
    showInForm: true,
    required: true,
    valueType: 'select',
    valueEnum: {
      2: getIntl().formatMessage({
        id: 'webOperation_dictionary_FleetAPP_select_text',
      }),
      3: getIntl().formatMessage({
        id: 'webOperation_dictionary_FleetWEB_select_text',
      }),
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_EGOAPP_select_text',
      }),
    },
  },
];

export const detailColumns: typeColumns[] = [
  {
    dataIndex: 'appAgreementId',
    langCode: 'webOperation_protocol_appAgreementContentId_tableColumn_text',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'typeCode',
    showInForm: true,
    readonly: true,
    valueType: 'select',
    valueEnum: {
      secret: getIntl().formatMessage({
        id: 'webOperation_dictionary_secret_select_text',
      }),
      user: getIntl().formatMessage({
        id: 'webOperation_dictionary_user_select_text',
      }),
    },
  },
  {
    dataIndex: 'title',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'titleLangId',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'version',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'businessType',
    showInForm: true,
    readonly: true,
    valueType: 'select',
    valueEnum: {
      2: getIntl().formatMessage({
        id: 'webOperation_dictionary_FleetAPP_select_text',
      }),
      3: getIntl().formatMessage({
        id: 'webOperation_dictionary_FleetWEB_select_text',
      }),
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_EGOAPP_select_text',
      }),
    },
  },
  {
    dataIndex: 'prdGroupList',
    langCode: 'webOperation_release_prdGroup_tableColumn_text',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'testGroupList',
    langCode: 'webOperation_release_testGroup_tableColumn_text',
    showInForm: true,
    readonly: true,
  },
];

export const newColumns: typeColumns[] = [
  {
    dataIndex: 'appAgreementId',
    langCode: 'webOperation_protocol_appAgreementContentId_tableColumn_text',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'typeCode',
    showInForm: true,
    readonly: true,
    valueType: 'select',
    valueEnum: {
      secret: getIntl().formatMessage({
        id: 'webOperation_dictionary_secret_select_text',
      }),
      user: getIntl().formatMessage({
        id: 'webOperation_dictionary_user_select_text',
      }),
    },
  },
  {
    dataIndex: 'title',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'titleLangId',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'version',
    showInForm: true,
    required: true,
  },
  {
    dataIndex: 'businessType',
    showInForm: true,
    required: true,
    valueType: 'select',
    readonly: true,
    valueEnum: {
      2: getIntl().formatMessage({
        id: 'webOperation_dictionary_FleetAPP_select_text',
      }),
      3: getIntl().formatMessage({
        id: 'webOperation_dictionary_FleetWEB_select_text',
      }),
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_EGOAPP_select_text',
      }),
    },
  },
];

export const extraColumns: typeColumns[] = [
  {
    dataIndex: 'applyUserName',
    width: 150,
    hideInSearch: true,
  },
  {
    dataIndex: 'applyTime',
    width: 150,
    valueType: 'dateTime',
    hideInSearch: true,
  },
  {
    dataIndex: 'approveUserName',
    width: 150,
    hideInSearch: true,
  },
  {
    dataIndex: 'approveTime',
    width: 150,
    valueType: 'dateTime',
    hideInSearch: true,
  },
  {
    dataIndex: 'operationRemark',
    width: 150,
    hideInSearch: true,
  },
];
