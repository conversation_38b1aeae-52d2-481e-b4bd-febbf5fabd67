import { ActionType, ProColumns, ProForm, ProFormInstance } from '@ant-design/pro-components';
import { Spin } from 'antd';
import React, { useRef, useState } from 'react';
import { FormattedMessage, useIntl } from 'umi';
import { map } from 'lodash-es';
import Modal, { RefType } from '@/components/Modal/index';
import ResizableTable from '@/components/Table';
import { useAction } from '@/hooks/action';
import {
  operatorFilter,
  releaseColumns,
  releaseFilter,
  typeColumns,
  useColumn,
} from '@/hooks/column';
import KeepAlive from '@/components/KeepAlive';
import { getProtocolDetail, getAppProtocolList } from '@/services/app';
import { buttonList } from './actions/AppProtocol';
import AppProtocolDetail, { RefType as DetailRefType } from '@/pages/release/AppProtocolDetail';
import { EditorItem } from '@/components/Form/EditorItem';

const AppProtocol: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const modalRef = useRef<RefType>(null);
  const formRef = useRef<ProFormInstance>();
  const modalFormRef = useRef<ProFormInstance>();
  const intl = useIntl();
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const { createColumns } = useColumn();

  const { createActions } = useAction('appAgreementContentId');
  const detailRef = useRef<DetailRefType>(null);

  const showProtocolDetail = async (id: string) => {
    setActionLoading(true);
    try {
      const res = await getProtocolDetail(id);
      setActionLoading(false);
      detailRef.current?.show(res);
    } catch (e) {
      setActionLoading(false);
    }
  };

  const actionColumn: ProColumns<API.ProtocolItem> = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 310,
    fixed: 'right',
    render: (_, record) => {
      const columns = buttonList(modalRef, modalFormRef);
      map(columns, (item) => {
        if (item.name === 'canView') {
          item.onClick = showProtocolDetail;
        }
      });
      return createActions(columns, record, 'product');
    },
  };

  const listColumns: typeColumns[] = [
    ...releaseFilter,
    {
      dataIndex: 'appAgreementId',
      langCode: 'webOperation_protocol_appAgreementContentId_tableColumn_text',
      width: 170,
      hideInSearch: true,
      showInForm: true,
    },
    {
      dataIndex: 'title',
      width: 150,
      showInForm: true,
    },
    {
      dataIndex: 'titleLangId',
      hideInTable: true,
      hideInSearch: true,
      showInForm: true,
    },
    {
      dataIndex: 'version',
      width: 150,
      showInForm: true,
    },
    {
      dataIndex: 'typeCode',
      width: 150,
      showInForm: true,
      valueType: 'select',
      valueEnum: {
        secret: intl.formatMessage({
          id: 'webOperation_dictionary_secret_select_text',
        }),
        user: intl.formatMessage({
          id: 'webOperation_dictionary_user_select_text',
        }),
      },
    },
    {
      dataIndex: 'statusCode',
      width: 150,
      valueEnum: {
        release_refused: intl.formatMessage({
          id: 'webOperation_dictionary_releaseRefused_select_text',
        }),
        stop_released: intl.formatMessage({
          id: 'webOperation_dictionary_stopReleased_select_text',
        }),
        will_release: intl.formatMessage({
          id: 'webOperation_dictionary_willRelease_select_text',
        }),
        release_verify_ing: intl.formatMessage({
          id: 'webOperation_dictionary_releaseVerifyIng_select_text',
        }),
        test_check_ing: intl.formatMessage({
          id: 'webOperation_dictionary_testCheckIng_select_text',
        }),
        stop_release_verify_ing: intl.formatMessage({
          id: 'webOperation_dictionary_stopReleaseVerifyIng_select_text',
        }),
        stop_release_refused: intl.formatMessage({
          id: 'webOperation_dictionary_stopReleaseRefused_select_text',
        }),
        test_refused: intl.formatMessage({
          id: 'webOperation_dictionary_testRefused_select_text',
        }),
        released: intl.formatMessage({
          id: 'webOperation_dictionary_released_select_text',
        }),
      },
    },
    {
      dataIndex: 'prdGroup',
      langCode: 'webOperation_release_prdGroup_tableColumn_text',
      showInForm: true,
      hideInTable: true,
      hideInSearch: true,
    },
    {
      dataIndex: 'testGroup',
      langCode: 'webOperation_release_testGroup_tableColumn_text',
      showInForm: true,
      hideInTable: true,
      hideInSearch: true,
    },
    {
      dataIndex: 'content',
      hideInTable: true,
      hideInSearch: true,
      showInForm: true,
      renderInEditForm: () => {
        return (
          <ProForm.Item
            name="content"
            key="content"
            label={intl.formatMessage({
              id: 'webOperation_protocol_content_tableColumn_text',
            })}
          >
            <EditorItem readonly={true} />
          </ProForm.Item>
        );
      },
    },
    {
      dataIndex: 'contentLangId',
      hideInTable: true,
      hideInSearch: true,
      showInForm: true,
    },
    ...operatorFilter,
  ];
  const extraColumns: typeColumns[] = [
    ...releaseColumns,
    {
      dataIndex: 'operationRemark',
      width: 150,
      hideInSearch: true,
    },
  ];

  const initColumns = createColumns('protocol', [...listColumns, actionColumn]);

  const afterColumns = createColumns('product', extraColumns);

  return (
    <>
      <Spin spinning={actionLoading}>
        <ResizableTable<API.ProtocolItem, API.ProtocolPageParams>
          actionRef={actionRef}
          formRef={formRef}
          rowKey="appAgreementContentId"
          defaultSize="small"
          search={{
            labelWidth: 'auto',
          }}
          scroll={{ x: 1300 }}
          request={getAppProtocolList}
          columns={[...initColumns, ...afterColumns]}
        />
      </Spin>
      <AppProtocolDetail ref={detailRef} key="detail" />
      <Modal ref={modalRef} parentRef={actionRef} formRef={modalFormRef} />
    </>
  );
};

export default () => {
  return (
    <KeepAlive>
      <AppProtocol />
    </KeepAlive>
  );
};
