// 框架依赖引入
import React, { useRef } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Button } from 'antd';
import { ProFormInstance } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { Access } from '@@/plugin-access/access';
import { map } from 'lodash-es';
// 公共自定义依赖引入
import ResizableTable from '@/components/Table';
import Modal, { RefType } from '@/components/Modal/index';
import { useCategories, useBrands } from '@/hooks/selectHooks';
import { useAction } from '@/hooks/action';
import { operatorFilter, releaseFilter, typeColumns, useColumn } from '@/hooks/column';
import KeepAlive from '@/components/KeepAlive';
import useExport from '@/hooks/useExport';
// 页面自定义依赖引入
import FirmwareReleaseDetail, {
  RefType as FirmwareRefType,
} from '@/pages/product/manage/FirmwareReleaseDetail';
import FirmwareUpgradeResult, {
  RefType as ResultRefType,
} from '@/pages/product/manage/FirmwareUpgradeResult';
import { getFirmwareList, exportFirmwares } from '@/services/release';
import { buttonList } from './actions/FirmwareList';

const FirmwareList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const modalRef = useRef<RefType>(null);
  const formRef = useRef<ProFormInstance>();
  const modalFormRef = useRef<ProFormInstance>();
  const firmwareRef = useRef<FirmwareRefType>(null);
  const resultRef = useRef<ResultRefType>(null);
  const access = useAccess();
  const { createActions } = useAction('id');
  const { createColumns } = useColumn();
  const intl = useIntl();
  const { valueEnum: categoryValueEnum } = useCategories();
  const { valueEnum: brandValueEnum } = useBrands();

  const { run: handleExport, loading: exportLoading } = useExport(
    exportFirmwares,
    formRef,
    intl.formatMessage({ id: 'webOperation_firmware_export_filename_text' }),
  );

  const goReleaseDetail = (id: string) => {
    firmwareRef.current?.show(id);
  };

  const goUpgradeResult = (id: string) => {
    resultRef.current?.show(id);
  };

  const actionColumn: ProColumns<API.MultiCodeItem> = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 350,
    fixed: 'right',
    render: (_, record) => {
      const columns = buttonList(modalRef, modalFormRef);
      map(columns, (item) => {
        switch (item.name) {
          case 'canViewReleaseDetail':
            item.onClick = goReleaseDetail;
            break;
          case 'canViewOtaResult':
            item.onClick = goUpgradeResult;
            break;
        }
      });
      return createActions(columns, record, 'product');
    },
  };

  const productColumns: typeColumns[] = [
    ...releaseFilter,
    {
      dataIndex: 'productId',
      langCode: 'webOperation_product_id_tableColumn_text',
      width: 170,
    },
    {
      dataIndex: 'categoryName',
      langCode: 'webOperation_product_categoryId_tableColumn_text',
      width: 150,
      hideInSearch: true,
    },
    {
      dataIndex: 'categoryId',
      width: 150,
      hideInTable: true,
      valueEnum: categoryValueEnum,
    },
    {
      dataIndex: 'productModel',
      langCode: 'webOperation_product_model_tableColumn_text',
      width: 150,
    },
    {
      dataIndex: 'brandName',
      langCode: 'webOperation_product_brandId_tableColumn_text',
      width: 150,
      hideInSearch: true,
    },
    {
      dataIndex: 'brandId',
      langCode: 'webOperation_product_brandId_tableColumn_text',
      hideInTable: true,
      valueEnum: brandValueEnum,
    },
    {
      dataIndex: 'commodityModel',
      width: 150,
    },
  ];

  const listColumns: typeColumns[] = [
    {
      dataIndex: 'jobId',
      width: 180,
      render: (_, record) => record.id,
    },
    {
      dataIndex: 'packageCount',
      width: 150,
      hideInSearch: true,
    },
    {
      dataIndex: 'releaseStatus',
      width: 150,
      valueEnum: {
        RELEASE_REFUSED: intl.formatMessage({
          id: 'webOperation_dictionary_releaseRejected_select_text',
        }),
        NULLIFY_REJECTED: intl.formatMessage({
          id: 'webOperation_dictionary_nullifyRejected_select_text',
        }),
        NULLIFIED: intl.formatMessage({
          id: 'webOperation_dictionary_nullified_select_text',
        }),
        RELEASE_TEST_REFUSED: intl.formatMessage({
          id: 'webOperation_dictionary_testRejected_select_text',
        }),
        RELEASING: intl.formatMessage({
          id: 'webOperation_dictionary_releasing_select_text',
        }),
        STOP_REFUSED: intl.formatMessage({
          id: 'webOperation_dictionary_stopRefused_select_text',
        }),
        OVER: intl.formatMessage({
          id: 'webOperation_dictionary_over_select_text',
        }),
        RELEASE_READY: intl.formatMessage({
          id: 'webOperation_dictionary_releaseReady_select_text',
        }),
        TESTING: intl.formatMessage({
          id: 'webOperation_dictionary_testingAndVerification_select_text',
        }),
        INIT: intl.formatMessage({
          id: 'webOperation_dictionary_init_select_text',
        }),
        RELEASED: intl.formatMessage({
          id: 'webOperation_dictionary_published_select_text',
        }),
        STOPPED: intl.formatMessage({
          id: 'webOperation_dictionary_stopped_select_text',
        }),
        NULLIFY_IN_REVIEW: intl.formatMessage({
          id: 'webOperation_dictionary_nullifyInReview_select_text',
        }),
        RELEASE_WAITING: intl.formatMessage({
          id: 'webOperation_dictionary_releaseWaiting_select_text',
        }),
        STOPPING: intl.formatMessage({
          id: 'webOperation_dictionary_stopping_select_text',
        }),
      },
    },
    ...operatorFilter,
  ];

  const extraColumns: typeColumns[] = [
    {
      dataIndex: 'applyBy',
      langCode: 'webOperation_product_applyUserName_tableColumn_text',
      width: 150,
      hideInSearch: true,
    },
    {
      dataIndex: 'applyTime',
      width: 150,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      dataIndex: 'ensuredBy',
      langCode: 'webOperation_product_approveUserName_tableColumn_text',
      width: 150,
      hideInSearch: true,
    },
    {
      dataIndex: 'ensuredTime',
      langCode: 'webOperation_product_approveTime_tableColumn_text',
      width: 150,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      dataIndex: 'description',
      width: 150,
      langCode: 'webCommon_page_remark_common_text',
      hideInSearch: true,
    },
  ];

  const preColumns = createColumns('product', productColumns);
  const initColumns = createColumns('firmware', listColumns);
  const afterColumns = createColumns('product', [...extraColumns, actionColumn]);

  return (
    <>
      <ResizableTable<API.FirmwareReleaseItem, API.FirmwareReleasePageParams>
        actionRef={actionRef}
        formRef={formRef}
        rowKey="id"
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        headerTitle={
          <Access accessible={access.canExportOtaRelease()}>
            <Button loading={exportLoading} type="primary" onClick={handleExport}>
              <FormattedMessage id="webCommon_page_export_button_text" />
            </Button>
          </Access>
        }
        scroll={{ x: 2350 }}
        request={getFirmwareList}
        columns={[...preColumns, ...initColumns, ...afterColumns]}
      />
      <Modal ref={modalRef} parentRef={actionRef} formRef={modalFormRef} />
      <FirmwareReleaseDetail ref={firmwareRef} key="detail" />
      <FirmwareUpgradeResult ref={resultRef} key="result" />
    </>
  );
};

export default () => {
  return (
    <KeepAlive>
      <FirmwareList />
    </KeepAlive>
  );
};
