import { ProForm } from '@ant-design/pro-components';
import React, { useImperativeHandle, useState } from 'react';
import { useIntl } from 'umi';
import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';
import { operatorFilter, releaseFilter, typeColumns } from '@/hooks/column';
import { EditorItem } from '@/components/Form/EditorItem';

export interface RefType {
  show: (record: API.ProtocolItem) => void;
}

const AppProtocolDetail = React.forwardRef<RefType>((props, ref) => {
  const intl = useIntl();
  const [visible, setVisible] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState<API.ProtocolItem>({});

  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    show: (record: API.ProtocolItem) => {
      setInitialValues(record);
      setVisible(true);
    },
  }));
  const listColumns: typeColumns[] = [
    ...releaseFilter,
    {
      dataIndex: 'appAgreementId',
      langCode: 'webOperation_protocol_appAgreementContentId_tableColumn_text',
      width: 170,
      hideInSearch: true,
      showInForm: true,
      readonly: true,
    },
    {
      dataIndex: 'title',
      width: 150,
      showInForm: true,
      readonly: true,
    },
    {
      dataIndex: 'titleLangId',
      hideInTable: true,
      hideInSearch: true,
      showInForm: true,
      readonly: true,
    },
    {
      dataIndex: 'version',
      width: 150,
      showInForm: true,
      readonly: true,
    },
    {
      dataIndex: 'typeCode',
      width: 150,
      showInForm: true,
      readonly: true,
      valueType: 'select',
      valueEnum: {
        secret: intl.formatMessage({
          id: 'webOperation_dictionary_secret_select_text',
        }),
        user: intl.formatMessage({
          id: 'webOperation_dictionary_user_select_text',
        }),
      },
    },
    {
      dataIndex: 'statusCode',
      width: 150,
      readonly: true,
      valueEnum: {
        release_refused: intl.formatMessage({
          id: 'webOperation_dictionary_releaseRefused_select_text',
        }),
        stop_released: intl.formatMessage({
          id: 'webOperation_dictionary_stopReleased_select_text',
        }),
        will_release: intl.formatMessage({
          id: 'webOperation_dictionary_willRelease_select_text',
        }),
        release_verify_ing: intl.formatMessage({
          id: 'webOperation_dictionary_releaseVerifyIng_select_text',
        }),
        test_check_ing: intl.formatMessage({
          id: 'webOperation_dictionary_testCheckIng_select_text',
        }),
        stop_release_verify_ing: intl.formatMessage({
          id: 'webOperation_dictionary_stopReleaseVerifyIng_select_text',
        }),
        stop_release_refused: intl.formatMessage({
          id: 'webOperation_dictionary_stopReleaseRefused_select_text',
        }),
        test_refused: intl.formatMessage({
          id: 'webOperation_dictionary_testRefused_select_text',
        }),
        released: intl.formatMessage({
          id: 'webOperation_dictionary_released_select_text',
        }),
      },
    },
    {
      dataIndex: 'prdGroup',
      langCode: 'webOperation_release_prdGroup_tableColumn_text',
      showInForm: true,
      hideInTable: true,
      hideInSearch: true,
      readonly: true,
    },
    {
      dataIndex: 'testGroup',
      langCode: 'webOperation_release_testGroup_tableColumn_text',
      showInForm: true,
      hideInTable: true,
      hideInSearch: true,
      readonly: true,
    },
    {
      dataIndex: 'content',
      hideInTable: true,
      hideInSearch: true,
      showInForm: true,
      readonly: true,
      renderInEditForm: () => {
        return (
          <ProForm.Item
            name="content"
            key="content"
            label={intl.formatMessage({
              id: 'webOperation_protocol_content_tableColumn_text',
            })}
          >
            <EditorItem readonly={true} />
          </ProForm.Item>
        );
      },
    },
    {
      dataIndex: 'contentLangId',
      hideInTable: true,
      hideInSearch: true,
      showInForm: true,
      readonly: true,
    },
    ...operatorFilter,
  ];

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: 'webOperation_product_canView_tableButton_text' })}
        width="50%"
        onCancel={onClose}
        hiddenConfirm={true}
        open={visible}
        destroyOnClose={true}
      >
        <ProForm
          initialValues={initialValues}
          labelCol={{ span: 5 }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
        >
          <FormFields columns={listColumns} pageName="protocol" />
        </ProForm>
      </FormContainer>
    </>
  );
});

export default AppProtocolDetail;
