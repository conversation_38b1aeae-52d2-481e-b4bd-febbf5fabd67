import type { typeColumns } from '@/hooks/column';

export const extraColumns: typeColumns[] = [
  {
    dataIndex: 'applyBy',
    langCode: 'webOperation_product_applyUserName_tableColumn_text',
    width: 150,
    hideInSearch: true,
  },
  {
    dataIndex: 'applyTime',
    width: 150,
    valueType: 'dateTime',
    hideInSearch: true,
  },
  {
    dataIndex: 'ensuredBy',
    langCode: 'webOperation_product_approveUserName_tableColumn_text',
    width: 150,
    hideInSearch: true,
  },
  {
    dataIndex: 'ensuredTime',
    langCode: 'webOperation_product_approveTime_tableColumn_text',
    width: 150,
    valueType: 'dateTime',
    hideInSearch: true,
  },
  {
    dataIndex: 'description',
    width: 150,
    langCode: 'webCommon_page_remark_common_text',
    hideInSearch: true,
  },
];
