import type { typeColumns } from '@/hooks/column';
import { ProFormDependency } from '@ant-design/pro-components';
import { FormattedMessage } from '@@/plugin-locale/localeExports';

export const newColumns: typeColumns[] = [
  {
    dataIndex: 'title',
    showInForm: true,
    required: true,
  },
  {
    dataIndex: 'content',
    showInForm: true,
    valueType: 'textarea',
  },
  {
    dataIndex: 'rutePath',
    showInForm: true,
  },
];

export const editColumns: typeColumns[] = [
  {
    dataIndex: 'sysMsgId',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'title',
    showInForm: true,
    required: true,
    addonRender: (
      <ProFormDependency name={['titleLangId']}>
        {({ titleLangId }) => {
          return (
            <span>
              <FormattedMessage id="webCommon_page_langId_common_text" />
              {titleLangId}
            </span>
          );
        }}
      </ProFormDependency>
    ),
  },
  {
    dataIndex: 'content',
    showInForm: true,
    valueType: 'textarea',
    extraRender: (
      <ProFormDependency name={['titleLangId']}>
        {({ titleLangId }) => {
          return (
            <span>
              <FormattedMessage id="webCommon_page_langId_common_text" />
              {titleLangId}
            </span>
          );
        }}
      </ProFormDependency>
    ),
  },
  {
    dataIndex: 'rutePath',
    showInForm: true,
  },
];
