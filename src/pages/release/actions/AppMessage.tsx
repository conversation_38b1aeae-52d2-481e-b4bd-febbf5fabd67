import {
  ensureReleaseSysMsg,
  refuseReleaseSysMsg,
  ensureTestSysMsg,
  refuseTestSysMsg,
  ensureStopReleaseSysMsg,
  refuseStopReleaseSysMsg,
  viewRefuseReleaseReasonSysMessage,
  viewRefuseTestReasonSysMessage,
  viewRefuseStopReleaseReasonSysMessage,
  ensureReleaseMarketingMsg,
  refuseReleaseMarketingMsg,
  ensureTestMarketingMsg,
  refuseTestMarketingMsg,
  ensureStopReleaseMarketingMsg,
  refuseStopReleaseMarketingMsg,
  viewRefuseReleaseReasonMarketingMessage,
  viewRefuseTestReasonMarketingMessage,
  viewRefuseStopReleaseReasonMarketingMessage,
} from '@/services/app';
import { OptionColumn, showRefuseReason, showRefuseForm, showConfirmModal } from '@/hooks/action';
import { RefObject, MutableRefObject } from 'react';
import { ProFormInstance } from '@ant-design/pro-components';
import { RefType } from '@/components/Modal/index';

export const buttonList = (
  modalRef: RefObject<RefType>,
  formRef: MutableRefObject<ProFormInstance | undefined>,
  type: string,
): OptionColumn[] => {
  return [
    // 详情
    {
      name: 'canView',
      module: 'SysMsg',
      langCode: 'webCommon_page_detail_common_text',
    },
    // 确认发布
    {
      name: 'canEnsureRelease',
      module: 'SysMsg',
      langCode: 'webOperation_product_canEnsureApply_tableButton_text',
      onClick: (id: string) => {
        showConfirmModal(
          modalRef,
          { api: type === 'system' ? ensureReleaseSysMsg : ensureReleaseMarketingMsg, id },
          'appMessage_ensureRelease',
        );
      },
    },
    // 测试通过
    {
      name: 'canEnsureTest',
      module: 'SysMsg',
      onClick: (id: string) => {
        showConfirmModal(
          modalRef,
          { api: type === 'system' ? ensureTestSysMsg : ensureTestMarketingMsg, id },
          'appMessage_test',
        );
      },
    },
    // 确认停止发布
    {
      name: 'canEnsureStopRelease',
      module: 'SysMsg',
      onClick: (id: string) => {
        showConfirmModal(
          modalRef,
          { api: type === 'system' ? ensureStopReleaseSysMsg : ensureStopReleaseMarketingMsg, id },
          'appMessage_stop',
        );
      },
    },
    // 发布驳回
    {
      name: 'canRefuseRelease',
      module: 'SysMsg',
      langCode: 'webOperation_product_canRefuseApply_tableButton_text',
      onClick: (id: string) => {
        showRefuseForm(
          { modalRef, formRef },
          type === 'system' ? refuseReleaseSysMsg : refuseReleaseMarketingMsg,
          id,
        );
      },
    },
    // 测试驳回
    {
      name: 'canRefuseTest',
      module: 'SysMsg',
      onClick: (id: string) => {
        showRefuseForm(
          { modalRef, formRef },
          type === 'system' ? refuseTestSysMsg : refuseTestMarketingMsg,
          id,
        );
      },
    },
    // 停止发布驳回
    {
      name: 'canRefuseStopRelease',
      module: 'SysMsg',
      onClick: (id: string) => {
        showRefuseForm(
          { modalRef, formRef },
          type === 'system' ? refuseStopReleaseSysMsg : refuseStopReleaseMarketingMsg,
          id,
        );
      },
    },
    // 发布被驳回原因
    {
      name: 'canViewRefuseReleaseReason',
      module: 'SysMsg',
      onClick: async (id: string) => {
        showRefuseReason(
          type === 'system'
            ? viewRefuseReleaseReasonSysMessage
            : viewRefuseReleaseReasonMarketingMessage,
          modalRef,
          id,
        );
      },
    },
    // 测试被驳回原因
    {
      name: 'canViewRefuseTestReason',
      module: 'SysMsg',
      onClick: async (id: string) => {
        showRefuseReason(
          type === 'system' ? viewRefuseTestReasonSysMessage : viewRefuseTestReasonMarketingMessage,
          modalRef,
          id,
        );
      },
    },
    // 停止发布被驳回原因
    {
      name: 'canViewRefuseStopReleaseReason',
      module: 'SysMsg',
      onClick: async (id: string) => {
        showRefuseReason(
          type === 'system'
            ? viewRefuseStopReleaseReasonSysMessage
            : viewRefuseStopReleaseReasonMarketingMessage,
          modalRef,
          id,
        );
      },
    },
  ];
};
