import { RefObject, MutableRefObject } from 'react';
import { ProFormInstance } from '@ant-design/pro-components';

import { RefType } from '@/components/Modal/index';
import { OptionColumn, showRefuseReason, showRefuseForm, showConfirmModal } from '@/hooks/action';
import {
  ensureRelease,
  ensureStopRelease,
  ensureTest,
  refuseRelease,
  refuseStopRelease,
  refuseTest,
  viewRefuseReleaseReason,
  viewRefuseStopReleaseReason,
  viewRefuseTestReason,
} from '@/services/app';
export const buttonList = (
  modalRef: RefObject<RefType>,
  formRef: MutableRefObject<ProFormInstance | undefined>,
): OptionColumn[] => {
  return [
    {
      name: 'canView',
      module: 'AppProtocolRelease',
    },
    {
      name: 'canEnsureRelease',
      module: 'AppProtocolRelease',
      langCode: 'webOperation_product_canEnsureApply_tableButton_text',
      onClick: (id: string) => {
        showConfirmModal(modalRef, { api: ensureRelease, id }, 'protocol_release');
      },
    },
    {
      name: 'canEnsureTest',
      module: 'AppProtocolRelease',
      onClick: (id: string) => {
        showConfirmModal(modalRef, { api: ensureTest, id }, 'protocol_test');
      },
    },
    {
      name: 'canEnsureStopRelease',
      module: 'AppProtocolRelease',
      onClick: (id: string) => {
        showConfirmModal(modalRef, { api: ensureStopRelease, id }, 'protocol_off');
      },
    },
    {
      name: 'canRefuseRelease',
      module: 'AppProtocolRelease',
      langCode: 'webOperation_product_canRefuseApply_tableButton_text',
      onClick: (id: string) => {
        showRefuseForm({ modalRef, formRef }, refuseRelease, id);
      },
    },
    {
      name: 'canRefuseTest',
      module: 'AppProtocolRelease',
      onClick: (id: string) => {
        showRefuseForm({ modalRef, formRef }, refuseTest, id);
      },
    },
    {
      name: 'canRefuseStopRelease',
      module: 'AppProtocolRelease',
      onClick: (id: string) => {
        showRefuseForm({ modalRef, formRef }, refuseStopRelease, id);
      },
    },
    {
      name: 'canViewRefuseReleaseReason',
      module: 'AppProtocolRelease',
      onClick: (id: string) => {
        showRefuseReason(viewRefuseReleaseReason, modalRef, id);
      },
    },
    {
      name: 'canViewRefuseTestReason',
      module: 'AppProtocolRelease',
      onClick: (id: string) => {
        showRefuseReason(viewRefuseTestReason, modalRef, id);
      },
    },
    {
      name: 'canViewRefuseStopReleaseReason',
      module: 'AppProtocolRelease',
      onClick: (id: string) => {
        showRefuseReason(viewRefuseStopReleaseReason, modalRef, id);
      },
    },
  ];
};
