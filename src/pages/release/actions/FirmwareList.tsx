import {
  passFirmwareRelease,
  passFirmwareTest,
  passFirmwareStop,
  refuseFirmwareRelease,
  refuseFirmwareTest,
  refuseFirmwareStop,
  passFirmwareNullify,
  refuseFirmwareNullify,
} from '@/services/release';
import {
  viewRefuseFirmwareReason,
  viewRefuseFirmwareTestReason,
  viewRefuseFirmwareStopReason,
  viewRefuseFirmwareNullifyReason,
} from '@/services/product';
import { OptionColumn, showRefuseReason, showRefuseForm, showConfirmModal } from '@/hooks/action';
import { RefObject, MutableRefObject } from 'react';
import { ProFormInstance } from '@ant-design/pro-components';
import { RefType } from '@/components/Modal/index';

export const buttonList = (
  modalRef: RefObject<RefType>,
  formRef: MutableRefObject<ProFormInstance | undefined>,
): OptionColumn[] => {
  return [
    // 发布详情
    {
      name: 'canViewReleaseDetail',
      module: 'OtaRelease',
      langCode: 'webOperation_firmware_releaseDetail_common_text',
    },
    // 确认发布
    {
      name: 'canEnsureRelease',
      module: 'OtaRelease',
      langCode: 'webOperation_product_canEnsureApply_tableButton_text',
      onClick: (id: string) => {
        showConfirmModal(modalRef, { api: passFirmwareRelease, id }, 'firmware_confirmRelease');
      },
    },
    // 测试通过
    {
      name: 'canTestEnsureRelease',
      module: 'OtaRelease',
      langCode: 'webOperation_product_canEnsureTest_tableButton_text',
      onClick: (id: string) => {
        showConfirmModal(modalRef, { api: passFirmwareTest, id }, 'firmware_test');
      },
    },
    // 确认停止发布
    {
      name: 'canEnsureStopRelease',
      module: 'OtaRelease',
      onClick: (id: string) => {
        showConfirmModal(modalRef, { api: passFirmwareStop, id }, 'firmware_stop');
      },
    },
    // 确认作废
    {
      name: 'canNullifyPassed',
      module: 'OtaRelease',
      onClick: (id: string) => {
        showConfirmModal(modalRef, { api: passFirmwareNullify, id }, 'firmware_nullify');
      },
    },
    // 发布驳回
    {
      name: 'canRefuseRelease',
      module: 'OtaRelease',
      langCode: 'webOperation_product_canRefuseApply_tableButton_text',
      onClick: (id: string) => {
        showRefuseForm({ modalRef, formRef }, refuseFirmwareRelease, id);
      },
    },
    // 测试驳回
    {
      name: 'canTestRefuseRelease',
      module: 'OtaRelease',
      langCode: 'webOperation_product_canRefuseTest_tableButton_text',
      onClick: (id: string) => {
        showRefuseForm({ modalRef, formRef }, refuseFirmwareTest, id);
      },
    },
    // 停止发布驳回
    {
      name: 'canRefuseStopRelease',
      module: 'OtaRelease',
      onClick: (id: string) => {
        showRefuseForm({ modalRef, formRef }, refuseFirmwareStop, id);
      },
    },
    // 作废驳回
    {
      name: 'canNullifyRefused',
      module: 'OtaRelease',
      onClick: (id: string) => {
        showRefuseForm({ modalRef, formRef }, refuseFirmwareNullify, id);
      },
    },
    // 发布被驳回原因
    {
      name: 'canViewApplyRefuseReason',
      module: 'OtaRelease',
      langCode: 'webOperation_product_canViewRefuseReleaseReason_tableButton_text',
      onClick: async (id: string) => {
        showRefuseReason(viewRefuseFirmwareReason, modalRef, id);
      },
    },
    // 测试被驳回原因
    {
      name: 'canViewTestRefuseReason',
      module: 'OtaRelease',
      langCode: 'webOperation_product_canViewRefuseTestReason_tableButton_text',
      onClick: async (id: string) => {
        showRefuseReason(viewRefuseFirmwareTestReason, modalRef, id);
      },
    },
    // 停止发布被驳回原因
    {
      name: 'canViewStopRefuseReason',
      module: 'OtaRelease',
      langCode: 'webOperation_product_canViewRefuseStopReleaseReason_tableButton_text',
      onClick: async (id: string) => {
        showRefuseReason(viewRefuseFirmwareStopReason, modalRef, id);
      },
    },
    // 作废被驳回原因
    {
      name: 'canViewNullifyRefusedReason',
      module: 'OtaRelease',
      onClick: async (id: string) => {
        showRefuseReason(viewRefuseFirmwareNullifyReason, modalRef, id);
      },
    },
    // 升级结果
    {
      name: 'canViewOtaResult',
      module: 'OtaRelease',
      langCode: 'webOperation_upgrade_result_common_text',
    },
  ];
};
