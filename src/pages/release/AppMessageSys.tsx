import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProFormInstance } from '@ant-design/pro-components';
import { Spin } from 'antd';
import React, { useRef, useState } from 'react';
import { FormattedMessage, useIntl } from 'umi';
import { map } from 'lodash-es';
import Modal, { RefType } from '@/components/Modal/index';
import ResizableTable from '@/components/Table';
import { useAction } from '@/hooks/action';
import {
  useColumn,
  releaseColumns,
  operatorFilter,
  fieldProps,
  releaseFilter,
  typeColumns,
} from '@/hooks/column';
import { createKAC } from '@/components/KeepAlive';
import { getSysMessageReleaseList, getSysMessageDetail } from '@/services/app';
import { buttonList } from './actions/AppMessage';
import MessageDetail, { RefType as DetailRefType } from '@/pages/app/MessageDetail';

const AppMessageSys: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const modalRef = useRef<RefType>(null);
  const formRef = useRef<ProFormInstance>();
  const modalFormRef = useRef<ProFormInstance>();
  const intl = useIntl();
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const { createColumns } = useColumn();

  const { createActions } = useAction('sysMsgId');
  const detailRef = useRef<DetailRefType>(null);

  const showMessageDetail = async (id: string) => {
    setActionLoading(true);
    try {
      const res = await getSysMessageDetail(id);
      setActionLoading(false);
      detailRef.current?.show(res);
    } catch (e) {
      setActionLoading(false);
    }
  };

  const actionColumn: ProColumns<API.AppMessageItem> = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 310,
    fixed: 'right',
    render: (_, record) => {
      const columns = buttonList(modalRef, modalFormRef, 'system');
      map(columns, (item) => {
        if (item.name === 'canView') {
          item.onClick = showMessageDetail;
        }
      });
      return createActions(columns, record, 'product');
    },
  };

  const dict = {
    rnStartType: [
      {
        label: intl.formatMessage({ id: 'webOperation_dictionary_immediately_select_text' }),
        value: '1',
      },
      {
        label: intl.formatMessage({ id: 'webOperation_dictionary_timeing_select_text' }),
        value: '2',
      },
    ],
    rnEndType: [
      {
        label: intl.formatMessage({ id: 'webOperation_dictionary_neverExpires_select_text' }),
        value: '1',
      },
      {
        label: intl.formatMessage({ id: 'webOperation_dictionary_timeing_select_text' }),
        value: '2',
      },
    ],
  };
  const listColumns: typeColumns[] = [
    ...releaseFilter,
    {
      dataIndex: 'pushStartTime',
      valueType: 'dateRange',
      hideInTable: true,
      showLabel: true,
      fieldProps,
      langCode: 'webOperation_appMessage_startTime_tableColumn_text',
    },
    {
      dataIndex: 'pushEndTime',
      valueType: 'dateRange',
      hideInTable: true,
      showLabel: true,
      fieldProps,
      langCode: 'webOperation_appMessage_endTime_tableColumn_text',
    },
    {
      dataIndex: 'sysMsgId',
      width: 170,
      hideInSearch: true,
      render: (_, record) => record.sysMsgId ?? record.marketingMsgId,
    },
    {
      dataIndex: 'msgId',
      hideInTable: true,
      langCode: 'webOperation_appMessage_sysMsgId_tableColumn_text',
    },
    {
      dataIndex: 'title',
      width: 150,
    },
    {
      dataIndex: 'pushTypeCodes',
      width: 150,
      valueEnum: {
        POPUP: intl.formatMessage({ id: 'webOperation_dictionary_popup_select_text' }),
        MESSAGE: intl.formatMessage({ id: 'webOperation_dictionary_message_select_text' }),
        BANNER: intl.formatMessage({ id: 'webOperation_dictionary_banner_select_text' }),
        MAIL: intl.formatMessage({ id: 'webOperation_dictionary_mail_select_text' }),
        TOMBSTONE: intl.formatMessage({ id: 'webOperation_dictionary_tombstone_select_text' }),
        PHONE_VOICE: intl.formatMessage({ id: 'webOperation_dictionary_phoneVoice_select_text' }),
      },
    },
    {
      dataIndex: 'startTime',
      width: 170,
      hideInSearch: true,
      render: (_, record) => {
        if (record.startType === 1) {
          const start = dict['startType']?.find(
            (item: API.selectOptions) => item.value === String(record.startType),
          );
          return start?.label || '';
        } else {
          return record.startZone ? record.startZone + ' ' + record.startTime : '';
        }
      },
    },
    {
      dataIndex: 'endTime',
      width: 170,
      hideInSearch: true,
      render: (_, record) => {
        if (record.endType === 1) {
          const end = dict['endType']?.find(
            (item: API.selectOptions) => item.value === String(record.endType),
          );
          return end?.label || '';
        } else {
          return record.endZone ? record.endZone + ' ' + record.endTime : '';
        }
      },
    },
    {
      dataIndex: 'statusCode',
      width: 150,
      valueEnum: {
        release_refused: intl.formatMessage({
          id: 'webOperation_dictionary_releaseRefused_select_text',
        }),
        stop_released: intl.formatMessage({
          id: 'webOperation_dictionary_stopReleased_select_text',
        }),
        will_release: intl.formatMessage({
          id: 'webOperation_dictionary_willRelease_select_text',
        }),
        release_verify_ing: intl.formatMessage({
          id: 'webOperation_dictionary_releaseVerifyIng_select_text',
        }),
        test_check_ing: intl.formatMessage({
          id: 'webOperation_dictionary_testCheckIng_select_text',
        }),
        stop_release_verify_ing: intl.formatMessage({
          id: 'webOperation_dictionary_stopReleaseVerifyIng_select_text',
        }),
        stop_release_refused: intl.formatMessage({
          id: 'webOperation_dictionary_stopReleaseRefused_select_text',
        }),
        test_refused: intl.formatMessage({
          id: 'webOperation_dictionary_testRefused_select_text',
        }),
        released: intl.formatMessage({
          id: 'webOperation_dictionary_released_select_text',
        }),
      },
    },
    ...operatorFilter,
  ];

  const initColumns = createColumns('appMessage', [...listColumns, actionColumn]);

  const afterColumns = createColumns('product', releaseColumns);

  return (
    <>
      <Spin spinning={actionLoading}>
        <ResizableTable<API.AppMessageItem, API.AppMessagePageParams>
          actionRef={actionRef}
          formRef={formRef}
          rowKey="sysMsgId"
          defaultSize="small"
          search={{
            labelWidth: 'auto',
          }}
          scroll={{ x: 1300 }}
          request={getSysMessageReleaseList}
          columns={[...initColumns, ...afterColumns]}
        />
      </Spin>
      <MessageDetail ref={detailRef} key="detail" dict={dict} />
      <Modal ref={modalRef} parentRef={actionRef} formRef={modalFormRef} />
    </>
  );
};
export default createKAC(AppMessageSys);
