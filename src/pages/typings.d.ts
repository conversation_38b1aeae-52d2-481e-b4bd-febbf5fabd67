declare namespace APP {
  export interface RefType<T = any> {
    edit: (item: T, mode?: boolean, part?: boolean) => void;
  }

  export interface EditFormProps {
    refresh?: () => Promise<void> | undefined;
    dict?: Record<string, any>;
  }

  export interface ViewRefType<T = string> {
    open: (id: T) => void;
  }

  export interface DetailProps {
    dictionary?: { [key: string]: any };
  }

  export type FnType<T = any, R = T> = (...arg: T[]) => R;
}
