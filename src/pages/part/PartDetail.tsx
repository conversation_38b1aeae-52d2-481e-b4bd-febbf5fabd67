import { Card, Col, Row, Spin, Tooltip } from 'antd';
import styles from './part.less';
import { assign, get, map } from 'lodash-es';
import { FormattedMessage } from '@@/plugin-locale/localeExports';
import React, { useCallback, useMemo } from 'react';
import { useIntl, useParams } from 'umi';
import { useRequest } from '@@/plugin-request/request';
import {
  getPart,
  updatePartDetail,
  createGuide,
  createManual,
  deleteGuide,
  deleteManual,
  downloadGuide,
  downloadManual,
  listGuide,
  listManual,
  updateGuide,
  updateManual,
  listFaq,
  deleteFaq,
  createFaq,
  updateFaq,
  sortFaq,
  sortGuide,
} from '@/services/part';
import LinkTable from '@/pages/part/components/LinkTable';
import Description from '@/components/Description';
import { rbac } from '@/access';
import GuideList from '@/pages/product/manage/faq/GuideList';
import FaqList from '@/pages/product/manage/faq/FaqList';
import { DefinitionContext } from '@/utils/context';
import { getS3File } from '@/services/common';

const BasicInfo: React.FC<{ detail: API.PartItem }> = ({ detail = {} }) => {
  const intl = useIntl();

  const partsTypeEnums = {
    battery_pack_charger: intl.formatMessage({
      id: 'webOperation_dictionary_batteryCharger_select_text',
    }),
    common_parts_type: intl.formatMessage({
      id: 'webOperation_dictionary_generalAccessories_select_text',
    }),
    special_parts_type: intl.formatMessage({
      id: 'webOperation_dictionary_specializedAccessories_select_text',
    }),
  };

  const data = [
    {
      label: <FormattedMessage id="webOperation_part_id_tableColumn_text" />,
      value: detail.partsId,
    },
    {
      label: <FormattedMessage id="webOperation_part_commodityModel_tableColumn_text" />,
      value: detail.commodityModel,
    },
    {
      label: <FormattedMessage id="webOperation_part_name_tableColumn_text" />,
      value: get(detail, ['name']),
    },
    {
      label: <FormattedMessage id="webOperation_part_type1_tableColumn_text" />,
      value: detail.type1 ? partsTypeEnums[detail.type1] : '',
    },
    {
      label: <FormattedMessage id="webOperation_part_type2_tableColumn_text" />,
      value: detail.type2 ? partsTypeEnums[detail.type2] : '',
    },
    {
      label: <FormattedMessage id="webOperation_part_partsIcon_tableColumn_text" />,
      value: detail.iconUrl ? (
        <img alt="" src={detail.iconUrl} className={styles.partIcon} />
      ) : null,
    },
  ];

  return (
    <Card style={{ margin: 20 }}>
      <Row gutter={16}>
        {map(data, (item, index) => {
          return (
            <Col span={8} key={index} className={styles.iot_ant_detail_col}>
              <span>{item.label}</span>：
              <span className={styles.iot_ant_detail_value}>
                <Tooltip placement="topLeft" title={item.value}>
                  {item.value}
                </Tooltip>
              </span>
            </Col>
          );
        })}
      </Row>
    </Card>
  );
};

export default () => {
  const { id } = useParams<{ id: string }>();
  const { data: detail = {}, loading, refresh } = useRequest(() => getPart(id));
  const intl = useIntl();
  const handleSubmit = useCallback(
    (type: string) => {
      return async (values: API.MultiLanguage) => {
        await updatePartDetail(type, { item: values.message!, partsId: id });
        refresh();
      };
    },
    [id, refresh],
  );

  const DefinitionContextProps = useMemo(() => {
    return {
      id,
    };
  }, [id]);

  return (
    <div style={{ background: '#fff', overflow: 'hidden' }}>
      <Spin spinning={loading}>
        <BasicInfo detail={detail} />
        <Description
          label="webOperation_partDetail_brief_input_text"
          value={detail.shortDescription}
          onSubmit={handleSubmit('ShortDesc')}
          code={rbac.PART.EDITSHORT}
          copyCode={rbac.PART.COPYSHORT}
        />
        <Description
          label="webOperation_partDetail_description_input_text"
          value={detail.longDescription}
          onSubmit={handleSubmit('LongDesc')}
          code={rbac.PART.EDITLONG}
          copyCode={rbac.PART.COPYLONG}
        />
        <Description
          label="webOperation_partDetail_spec_input_text"
          value={detail.technicalSpecification}
          onSubmit={handleSubmit('TechSpec')}
          code={rbac.PART.EDITTECH}
          copyCode={rbac.PART.COPYTECH}
        />

        <Card
          style={{ margin: 20 }}
          title={intl.formatMessage({ id: 'webOperation_partDetail_link_table_text' })}
          size="small"
        >
          <LinkTable />
        </Card>

        <Card
          style={{ margin: 20 }}
          title={intl.formatMessage({ id: 'webOperation_partDetail_manual_table_text' })}
          size="small"
        >
          <GuideList
            accept=".pdf"
            text={<FormattedMessage id="webOperation_partDetail_addFile_button_text" />}
            listFn={async () => await listManual({ req: id })}
            deleteFn={async (data: API.ManualItem) => deleteManual({ req: data.partsManualId })}
            createFn={async (data: API.ManualItem) => {
              if (String(data.type) === '2') {
                const { key } = await getS3File(data.url as string);
                data.s3key = key;
              }
              await createManual(assign({}, data, { partsId: id }));
            }}
            updateFn={async (data: API.ManualItem) => {
              if (String(data.type) === '2') {
                const { key } = await getS3File(data.url as string);
                data.s3key = key;
              }
              await updateManual(assign({}, data, { partsId: id }));
            }}
            downloadFn={async (data: API.ManualItem) =>
              downloadManual(
                {
                  req: data.partsManualId!,
                },
                data.name?.message,
              )
            }
            rbac={rbac.PART.MANUAL}
          />
        </Card>

        <Card
          style={{ margin: 20 }}
          title={intl.formatMessage({ id: 'webOperation_partDetail_guide_table_text' })}
          size="small"
        >
          <GuideList
            accept=".mp4"
            text={<FormattedMessage id="webCommon_page_add_button_text" />}
            sortable
            listFn={async () => await listGuide({ req: id })}
            deleteFn={async (data: API.ManualItem) =>
              deleteGuide({ req: data.partsOperationGuidanceId })
            }
            createFn={async (data: API.ManualItem) => {
              if (String(data.type) === '2') {
                const { key } = await getS3File(data.url as string);
                data.s3key = key;
              }
              await createGuide(assign({}, data, { partsId: id }));
            }}
            updateFn={async (data: API.ManualItem) => {
              if (String(data.type) === '2') {
                const { key } = await getS3File(data.url as string);
                data.s3key = key;
              }
              await updateGuide(assign({}, data, { partsId: id }));
            }}
            downloadFn={async (data: API.ManualItem) =>
              downloadGuide(
                {
                  req: data.partsOperationGuidanceId!,
                },
                data.name?.message,
              )
            }
            sortFn={sortGuide}
            namespace="parts"
            rbac={rbac.PART.GUIDE}
          />
        </Card>
        <Card
          style={{ margin: 20 }}
          title={intl.formatMessage({ id: 'webOperation_partDetail_faq_table_text' })}
          size="small"
        >
          <DefinitionContext.Provider value={DefinitionContextProps}>
            <FaqList
              listFn={listFaq}
              deleteFn={deleteFaq}
              createFn={createFaq}
              updateFn={updateFaq}
              sortFn={sortFaq}
              namespace="parts"
              rbac={rbac.PART.FAQ}
            />
          </DefinitionContext.Provider>
        </Card>
      </Spin>
    </div>
  );
};
