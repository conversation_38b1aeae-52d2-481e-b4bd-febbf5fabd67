.partIcon {
  width: 80px;
  height: auto;
  max-height: 80px;
  object-fit: contain;
}
.dragSort {
  :global(.@{ant-prefix}-table-cell > span) {
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
}
.content {
  margin-bottom: 10px;
  line-height: 20px;
  white-space: normal;
  text-overflow: ellipsis;
  word-break: keep-all;

  & > span:first-child {
    font-weight: bold;
  }
}

.tableContent {
  :global(.@{ant-prefix}-pro-card-body) {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  :global(.@{ant-prefix}-pro-table-search) {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
}
.iot_ant_detail_col {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 10px;
  overflow: hidden;
  line-height: 20px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.iot_ant_detail_value {
  overflow: hidden;
  color: #000;
  font-weight: 500;
  white-space: nowrap;
  text-overflow: ellipsis;
}
