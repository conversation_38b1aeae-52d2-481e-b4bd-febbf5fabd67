import FormContainer from '@/components/Form/FormContainer';
import { createPart, getPart, updatePart } from '@/services/part';
import { len500, len128 } from '@/utils/validate';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Button, message } from 'antd';
import React, { useImperativeHandle, useMemo, useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { ProFormDependency } from '@ant-design/pro-form';
import { LocaleID } from '@/components/Locale';
import ImageField from '@/components/Form/ImageField';
import { last, split, join, slice, get } from 'lodash-es';
import { getS3File } from '@/services/common';
const PartEdit = React.forwardRef<APP.RefType<API.PartItem>, APP.EditFormProps>((props, ref) => {
  const { refresh } = props;
  const intl = useIntl();
  const formRef = useRef<ProFormInstance<API.PartItem>>();
  const [visible, setVisible] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState<API.PartItem>({} as any);
  const access = useAccess();

  const { run: handleSubmit, loading } = useRequest(
    async (values: API.PartItem) => {
      if (Number(values.iconType) === 1) {
        const { key } = await getS3File(values.iconUrl as string);
        values.uploadIconName = key;
      }
      if (Number(values.iconType) === 0 && values.uploadIconName?.includes('http')) {
        const urlNoProtocol = split(last(split(values.uploadIconName, '://')), '/');
        values.uploadIconName = join(slice(urlNoProtocol, 1, urlNoProtocol.length), '/');
      }
      if (initialValues.partsId) {
        await updatePart(values);
      } else {
        await createPart(values);
      }
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setVisible(false);
      refresh?.();
    },
    { manual: true },
  );
  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    edit: (item: API.PartItem) => {
      if (!access.canUpdatePart()) {
        console.warn(intl.formatMessage({ id: 'webCommon_page_noAccess_toast_text' }));
        return;
      }
      const values = { ...item };
      if (Number(values.iconType) === 0) {
        values.uploadIconName = values.iconUrl;
        values.iconUrl = '';
      } else {
        values.uploadIconName = '';
      }
      setInitialValues(values);
      setVisible(true);
      getPart(item.partsId!).then((res) => {
        const langId = get(res, ['data', 'nameLangId']);
        setInitialValues({ ...values, nameLangId: langId });
        formRef.current?.setFieldValue('nameLangId', langId);
      });
    },
  }));

  const onCreate = () => {
    setInitialValues({ iconType: '0' } as any);
    setVisible(true);
  };

  const isEdit = useMemo(() => {
    return initialValues.partsId !== null && initialValues.partsId !== undefined;
  }, [initialValues.partsId]);

  return (
    <>
      <FormContainer
        title={
          initialValues.partsId
            ? intl.formatMessage({ id: 'webCommon_page_edit_common_text' })
            : intl.formatMessage({ id: 'webOperation_part_add_button_text' })
        }
        width="50%"
        onCancel={onClose}
        onConfirm={() => formRef.current?.submit()}
        open={visible}
        destroyOnClose={true}
        loading={loading}
      >
        <ProForm
          initialValues={initialValues}
          onFinish={handleSubmit}
          formRef={formRef}
          labelCol={{ flex: '150px' }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
        >
          {isEdit ? (
            <ProFormText
              label={intl.formatMessage({
                id: 'webOperation_part_id_input_text',
              })}
              name="partsId"
              readonly
            />
          ) : null}
          <ProFormText
            name="commodityModel"
            label={intl.formatMessage({
              id: 'webOperation_part_commodityModel_input_text',
            })}
            rules={[
              {
                required: true,
                message: (
                  <FormattedMessage id="webOperation_part_commodityModel_input_placeholder" />
                ),
              },
              len128,
            ]}
            placeholder={intl.formatMessage({
              id: 'webOperation_part_commodityModel_input_placeholder',
            })}
          />

          <ProFormText
            name="name"
            label={intl.formatMessage({
              id: 'webOperation_part_name_input_text',
            })}
            extra={
              <ProFormDependency name={['nameLangId']}>
                {({ nameLangId }) => <LocaleID id={nameLangId} />}
              </ProFormDependency>
            }
            rules={[
              {
                required: true,
                message: <FormattedMessage id="webOperation_part_name_input_placeholder" />,
              },
              len128,
            ]}
            placeholder={intl.formatMessage({
              id: 'webOperation_part_name_input_placeholder',
            })}
          />

          <ProFormSelect
            name="type1"
            label={intl.formatMessage({
              id: 'webOperation_part_type1_select_text',
            })}
            valueEnum={{
              battery_pack_charger: intl.formatMessage({
                id: 'webOperation_dictionary_batteryCharger_select_text',
              }),
              common_parts_type: intl.formatMessage({
                id: 'webOperation_dictionary_generalAccessories_select_text',
              }),
              special_parts_type: intl.formatMessage({
                id: 'webOperation_dictionary_specializedAccessories_select_text',
              }),
            }}
            rules={[
              {
                required: true,
                message: <FormattedMessage id="webOperation_part_type1_select_placeholder" />,
              },
            ]}
            placeholder={intl.formatMessage({
              id: 'webOperation_part_type1_select_placeholder',
            })}
          />

          <ProFormSelect
            name="type2"
            label={intl.formatMessage({
              id: 'webOperation_part_type2_select_text',
            })}
            valueEnum={{
              battery_pack_charger: intl.formatMessage({
                id: 'webOperation_dictionary_batteryCharger_select_text',
              }),
              common_parts_type: intl.formatMessage({
                id: 'webOperation_dictionary_generalAccessories_select_text',
              }),
              special_parts_type: intl.formatMessage({
                id: 'webOperation_dictionary_specializedAccessories_select_text',
              }),
            }}
          />

          <ImageField
            formRef={formRef}
            label={intl.formatMessage({
              id: 'webOperation_part_icon_select_text',
            })}
            fieldInputName="iconUrl"
            fieldUploadName="uploadIconName"
          />

          <ProFormTextArea
            name="remark"
            label={intl.formatMessage({
              id: 'webOperation_part_desc_input_text',
            })}
            rules={[len500]}
            placeholder={intl.formatMessage({
              id: 'webOperation_part_desc_input_placeholder',
            })}
          />
        </ProForm>
      </FormContainer>
      <Access accessible={access.canCreatePart()}>
        <Button type="primary" onClick={onCreate}>
          <PlusOutlined />
          <FormattedMessage id="webOperation_part_add_button_text" />
        </Button>
      </Access>
    </>
  );
});

export default PartEdit;
