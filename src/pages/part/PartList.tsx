import ResizableTable from '@/components/Table';
import PartEdit from '@/pages/part/PartEdit';
import {
  deletePart,
  downloadTemplate,
  exportPart,
  listPart,
  listRelationProduct,
} from '@/services/part';
import { useIntl } from '@@/plugin-locale/localeExports';
import { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { Col, message, Row, Space as AntSpace } from 'antd';
import React, { useMemo, useRef } from 'react';
import { FormattedMessage, useHistory } from 'umi';
import styles from './part.less';
import { assign, filter, has, isArray, join, map, reduce } from 'lodash-es';
import Space from '@/components/Space';
import { AccessButton, AccessLink } from '@/components/Button/AccessButton';
import { DownloadOutlined } from '@ant-design/icons';
import { createKAC } from '@/components/KeepAlive';
import { rbac } from '@/access';
import { ProTableProps } from '@ant-design/pro-table';
import dayjs from 'dayjs';

import ErrorModal from '@/components/ErrorModal';
import { dateFilter } from '@/hooks/column';
import { useCategories } from '@/hooks/selectHooks';
import useExport from '@/hooks/useExport';
import ImportTemplate from '@/components/Import/index';

const RelatedItems: React.FC<{ record: API.PartItem }> = ({ record }) => {
  const { valueEnum: categoryEnums } = useCategories();

  const columns: ProColumns<API.PartItem>[] = [
    {
      title: <FormattedMessage id="webOperation_product_productId_tableColumn_text" />,
      dataIndex: 'productId',
      fixed: 'left',
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_category_categoryName_tableColumn_text" />,
      dataIndex: 'categoryId',
      valueEnum: categoryEnums,
      width: 150,
      render: (_, r) => r?.categoryName || _,
    },
    {
      title: <FormattedMessage id="webOperation_product_model_tableColumn_text" />,
      dataIndex: 'model',
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_product_commodityModel_tableColumn_text" />,
      dataIndex: 'commodityModel',
      width: 150,
      fixed: 'right',
    },
  ];
  return (
    <>
      <Row>
        <Col span={24} className={styles.content}>
          <span>
            <FormattedMessage id="webOperation_product_commodityModel_tableColumn_text" />
          </span>
          : <span>{record.commodityModel}</span>
        </Col>
        <Col span={24} className={styles.content}>
          <span>
            <FormattedMessage id="webOperation_part_name_tableColumn_text" />
          </span>
          : <span>{record.name}</span>
        </Col>
      </Row>

      <ResizableTable<API.PartItem, API.PartPageParams>
        rowKey="id"
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        scroll={{ x: 150 * 4 }}
        request={(params) => listRelationProduct({ ...params, partsId: record.partsId })}
        toolBarRender={false}
        columns={columns}
      />
    </>
  );
};

interface PartListProps {
  selectable: boolean;
  selectedKeys: React.Key[];
  disabledKeys?: React.Key[];
  productId?: string;
  onSelect?: (keys: React.Key[]) => void;
}

export const PartList: React.FC<PartListProps> = ({
  selectable = false,
  onSelect,
  selectedKeys,
  disabledKeys,
  productId,
}) => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const editRef = useRef<APP.RefType<API.PartItem>>(null);
  const history = useHistory();
  const intl = useIntl();
  const errorRef = useRef<APP.ViewRefType>(null);

  const columns = filter<ProColumns<API.PartItem>>(
    [
      ...dateFilter,
      {
        title: <FormattedMessage id="webOperation_part_id_tableColumn_text" />,
        dataIndex: 'partsId',
        width: 170,
      },
      {
        title: <FormattedMessage id="webOperation_part_commodityModel_tableColumn_text" />,
        dataIndex: 'commodityModel',
        width: 150,
      },
      {
        title: <FormattedMessage id="webOperation_part_name_tableColumn_text" />,
        dataIndex: 'name',
        width: 150,
      },
      {
        title: <FormattedMessage id="webOperation_part_partsIcon_tableColumn_text" />,
        dataIndex: 'iconUrl',
        width: 150,
        hideInSearch: true,
        render: (_, record) => {
          return record.iconUrl ? (
            <img alt="" src={record.iconUrl as string} className={styles.partIcon} />
          ) : (
            _
          );
        },
      },
      {
        title: <FormattedMessage id="webOperation_part_type1_tableColumn_text" />,
        dataIndex: 'type1',
        width: 150,
        valueEnum: {
          battery_pack_charger: intl.formatMessage({
            id: 'webOperation_dictionary_batteryCharger_select_text',
          }),
          common_parts_type: intl.formatMessage({
            id: 'webOperation_dictionary_generalAccessories_select_text',
          }),
          special_parts_type: intl.formatMessage({
            id: 'webOperation_dictionary_specializedAccessories_select_text',
          }),
        },
      },
      {
        title: <FormattedMessage id="webOperation_part_type2_tableColumn_text" />,
        dataIndex: 'type2',
        width: 150,
        valueEnum: {
          battery_pack_charger: intl.formatMessage({
            id: 'webOperation_dictionary_batteryCharger_select_text',
          }),
          common_parts_type: intl.formatMessage({
            id: 'webOperation_dictionary_generalAccessories_select_text',
          }),
          special_parts_type: intl.formatMessage({
            id: 'webOperation_dictionary_specializedAccessories_select_text',
          }),
        },
      },
      {
        title: <FormattedMessage id="webOperation_part_relate_tableColumn_text" />,
        dataIndex: 'relate',
        width: 150,
        hideInSearch: true,
        render: (_, record) => (
          <AccessLink
            text="webCommon_page_view_common_text"
            code={rbac.PART.RELATIVE}
            modal={{
              title: 'webOperation_part_relate_tableButton_text',
              content: <RelatedItems record={record} />,
              width: '60%',
              footer: null,
            }}
          />
        ),
      },
      {
        title: <FormattedMessage id="webCommon_page_createBy_tableColumn_text" />,
        dataIndex: 'createBy',
        hideInSearch: true,
        width: 150,
      },
      {
        title: <FormattedMessage id="webCommon_page_createTime_tableColumn_text" />,
        dataIndex: 'createTime',
        valueType: 'dateRange',
        hideInSearch: true,
        render: (_, record) =>
          record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') : _,
        width: 150,
      },
      {
        title: <FormattedMessage id="webCommon_page_updateBy_tableColumn_text" />,
        dataIndex: 'updateBy',
        hideInSearch: true,
        width: 150,
      },
      {
        title: <FormattedMessage id="webCommon_page_updateTime_tableColumn_text" />,
        dataIndex: 'updateTime',
        valueType: 'dateRange',
        hideInSearch: true,
        render: (_, record) =>
          record.updateTime ? dayjs(record.updateTime).format('YYYY-MM-DD HH:mm:ss') : _,
        width: 150,
      },
      {
        title: <FormattedMessage id="webOperation_part_desc_tableColumn_text" />,
        dataIndex: 'remark',
        hideInSearch: true,
        width: 150,
        ellipsis: true,
      },
      {
        title: <FormattedMessage id="webOperation_part_option_tableColumn_text" />,
        dataIndex: 'option',
        valueType: 'option',
        width: 200,
        fixed: 'right',
        render: (_: any, record: API.PartItem) => (
          <Space>
            <AccessLink
              text="webCommon_page_detail_common_text"
              code={rbac.PART.VIEW}
              onClick={() => {
                if (window['__POWERED_BY_QIANKUN__']) {
                  window.router.push({
                    path: `/${ROOT_PATH.replace(/\//g, '')}/part/${record.partsId}`,
                  });
                } else {
                  history.push({
                    pathname: `/part/${record.partsId}`,
                  });
                }
              }}
            />
            <AccessLink
              text="webCommon_page_edit_common_text"
              code={rbac.PART.UPDATE}
              onClick={() => {
                editRef.current?.edit(record);
              }}
            />
            <AccessLink
              text="webCommon_page_delete_tableButton_linkText"
              code={rbac.PART.DELETE}
              modal={{
                content: <FormattedMessage id="webOperation_part_delete_modal_message" />,
              }}
              onClick={async () => {
                try {
                  await deletePart(record.partsId!);
                  message.success(
                    intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }),
                  );
                  actionRef.current?.reload();
                } catch (e: any) {
                  try {
                    const res = JSON.parse(e.message);
                    if (res.responseCode === '1090042008') {
                      errorRef.current?.open(res.errorMessage);
                    } else {
                      message.error(res.errorMessage);
                    }
                  } catch (err) {
                    message.error(
                      intl.formatMessage({ id: 'webCommon_page_internalError_toast_text' }),
                    );
                  }
                }
              }}
            />
          </Space>
        ),
      },
    ],
    (item) => {
      return (
        !selectable ||
        (selectable &&
          ['partsId', 'commodityModel', 'name', 'type1'].indexOf(
            isArray(item.dataIndex) ? join(item.dataIndex, '.') : (item.dataIndex as string),
          ) > -1)
      );
    },
  );

  const { run: handleExport, loading: exportLoading } = useExport(
    exportPart,
    formRef,
    `Product-${dayjs().format('YYYYMMDD')}`,
  );

  const config: Partial<ProTableProps<API.PartItem, API.PartPageParams>> = useMemo(() => {
    return selectable
      ? {
          rowSelection: {
            preserveSelectedRowKeys: true,
            onChange: (selectedRowKeys: React.Key[], selectedRows: API.PartItem[]) => {
              console.log(`selectedRowKeys: ${selectedRowKeys}`);
              onSelect?.(map(selectedRows, (item) => item.partsId as string));
            },
            getCheckboxProps: (record: API.PartItem) => {
              return {
                disabled: has(disabledKeys, record.partsId!),
              };
            },
          },
          toolBarRender: false,
        }
      : {};
  }, [onSelect, selectable, disabledKeys]);

  return (
    <>
      <ResizableTable<API.PartItem, API.PartPageParams>
        {...config}
        actionRef={actionRef}
        formRef={formRef}
        rowKey="partsId"
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        scroll={{
          x: reduce(columns, (total, current) => total + (Number(current.width) || 150), 0),
        }}
        headerTitle={
          <AntSpace>
            <PartEdit ref={editRef} key="create" refresh={() => actionRef.current?.reload()} />
            <ImportTemplate
              key="import"
              pageName="part"
              buttonName="webCommon_page_import_button_text"
              getTemplate={downloadTemplate}
              url="/operation-platform/parts/import"
              urlParam="file"
              refresh={() => {
                return actionRef.current?.reload();
              }}
            />
            <AccessButton
              text="webCommon_page_export_button_text"
              code={rbac.PART.EXPORT}
              buttonProps={{ loading: exportLoading }}
              icon={<DownloadOutlined />}
              onClick={handleExport}
            />
          </AntSpace>
        }
        request={(v) => listPart(assign(v, { productId }))}
        columns={columns}
      />
      <ErrorModal title="webCommon_page_deleteForbid_modal_title" ref={errorRef} />
    </>
  );
};

export default createKAC(PartList);
