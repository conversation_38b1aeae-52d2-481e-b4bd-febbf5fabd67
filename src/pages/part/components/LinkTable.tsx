import React, { useRef } from 'react';
import { ActionType, ProColumns, ProFormText } from '@ant-design/pro-components';
import { FormattedMessage, useIntl } from '@@/plugin-locale/localeExports';
import { AccessLink } from '@/components/Button/AccessButton';
import ResizableTable from '@/components/Table';
import { deleteLink, listLinks } from '@/services/part';
import LinkEdit from '@/pages/part/components/LinkEdit';
import { useParams } from 'umi';
import Space from '@/components/Space';
import styles from '../part.less';
import { rbac } from '@/access';

const LinkTable: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const actionRef = useRef<ActionType>();
  const editRef = useRef<APP.RefType<API.PartsPurchaseLink>>(null);
  const intl = useIntl();
  const columns: ProColumns<API.PartsPurchaseLink>[] = [
    {
      title: <FormattedMessage id="webOperation_part_linkId_tableColumn_text" />,
      dataIndex: ['link', 'instanceId'],
      fixed: 'left',
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_part_linkUrl_tableColumn_text" />,
      dataIndex: ['link', 'url'],
      width: 150,
    },
    {
      title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
      width: 150,
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      render: (_: any, record) => (
        <Space>
          <AccessLink
            text="webCommon_page_edit_common_text"
            code={rbac.PART.LINK.UPDATE}
            onClick={() => {
              editRef.current?.edit(record);
            }}
          />
          <AccessLink
            text="webCommon_page_delete_tableButton_linkText"
            code={rbac.PART.LINK.DELETE}
            onClick={async () => {
              await deleteLink(record.partsLinkId!);
              actionRef.current?.reload();
            }}
            modal={{
              content: (
                <>
                  <div style={{ marginBottom: '10px' }}>
                    <div>
                      <FormattedMessage id="webOperation_part_deleteLine1_modal_message" />
                    </div>
                    <div>
                      <FormattedMessage id="webOperation_part_deleteLine2_modal_message" />
                    </div>
                  </div>

                  <ProFormText
                    name="confirm"
                    label={false}
                    rules={[
                      {
                        required: true,
                        pattern: /^Yes$/,
                        message: <FormattedMessage id="webCommon_page_delete_validator_message" />,
                      },
                    ]}
                    placeholder={intl.formatMessage({
                      id: 'webCommon_page_delete_validator_message',
                    })}
                  />
                </>
              ),
            }}
          />
        </Space>
      ),
    },
  ];
  return (
    <ResizableTable
      rowKey="partsLinkId"
      actionRef={actionRef}
      defaultSize="small"
      search={false}
      scroll={{ x: 150 * 3 }}
      request={() => listLinks(id)}
      pagination={false}
      headerTitle={<LinkEdit ref={editRef} refresh={() => actionRef.current?.reload()} />}
      columns={columns}
      className={styles.tableContent}
    />
  );
};

export default LinkTable;
