import { rbac } from '@/access';
import FormContainer from '@/components/Form/FormContainer';
import { createLink, updateLink } from '@/services/part';
import { len500 } from '@/utils/validate';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { PlusOutlined } from '@ant-design/icons';
import { ProForm, ProFormInstance, ProFormText } from '@ant-design/pro-components';
import { Button, message } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl, useParams } from 'umi';

const LinkEdit = React.forwardRef<APP.RefType<API.PartsPurchaseLink>, APP.EditFormProps>(
  (props, ref) => {
    const { refresh } = props;
    const intl = useIntl();
    const formRef = useRef<ProFormInstance<API.PartsPurchaseLink>>();
    const [visible, setVisible] = useState<boolean>(false);
    const [initialValues, setInitialValues] = useState<API.PartsPurchaseLink>({} as any);
    const access = useAccess();
    const { id } = useParams<{ id: string }>();

    const { run: handleSubmit, loading } = useRequest(
      async (values) => {
        if (initialValues.partsLinkId) {
          await updateLink({
            link: { url: values.link.url },
            operateItemId: initialValues.partsLinkId,
            partsId: id,
          });
        } else {
          await createLink({ ...values, partsId: id });
        }
        message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
        setVisible(false);
        refresh?.();
      },
      { manual: true },
    );

    const onClose = () => {
      setVisible(false);
    };

    useImperativeHandle(ref, () => ({
      edit: (item: API.PartsPurchaseLink) => {
        if (!access.canUpdatePart()) {
          console.warn(intl.formatMessage({ id: 'webCommon_page_noAccess_toast_text' }));
          return;
        }
        setInitialValues(item);
        setVisible(true);
      },
    }));

    const onCreate = () => {
      setInitialValues({} as any);
      setVisible(true);
    };

    return (
      <>
        <FormContainer
          title={
            initialValues.partsLinkId
              ? intl.formatMessage({ id: 'webOperation_part_updateLink_drawer_title' })
              : intl.formatMessage({ id: 'webOperation_part_addLink_drawer_title' })
          }
          width="50%"
          onCancel={onClose}
          onConfirm={() => formRef.current?.submit()}
          open={visible}
          destroyOnClose={true}
          loading={loading}
        >
          <ProForm
            initialValues={initialValues}
            onFinish={handleSubmit}
            formRef={formRef}
            labelCol={{ flex: '150px' }}
            layout="horizontal"
            onReset={onClose}
            submitter={false}
          >
            {initialValues.partsLinkId ? (
              <ProFormText
                label={intl.formatMessage({ id: 'webOperation_part_linkId_input_text' })}
                name={['link', 'instanceId']}
                readonly
              />
            ) : null}
            <ProFormText
              name={['link', 'url']}
              label={intl.formatMessage({
                id: 'webOperation_part_linkUrl_input_text',
              })}
              rules={[
                {
                  required: true,
                  message: <FormattedMessage id="webOperation_part_linkUrl_input_placeholder" />,
                },
                {
                  pattern: /^http:\/\/|^https:\/\//,
                  message: intl.formatMessage({ id: 'webOperation_part_link_validator_message' }),
                },
                len500,
              ]}
              placeholder={intl.formatMessage({
                id: 'webOperation_part_linkUrl_input_placeholder',
              })}
            />
          </ProForm>
        </FormContainer>
        <Access accessible={access.checkAuth(rbac.PART.LINK.CREATE)}>
          <Button type="primary" onClick={onCreate}>
            <PlusOutlined />
            <FormattedMessage id="webCommon_page_add_button_text" />
          </Button>
        </Access>
      </>
    );
  },
);

export default LinkEdit;
