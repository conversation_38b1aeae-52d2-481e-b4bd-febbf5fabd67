import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProFormInstance } from '@ant-design/pro-components';
import { Spin, Button, Space } from 'antd';
import React, { useRef, useState } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Access } from '@@/plugin-access/access';

import Modal, { RefType } from '@/components/Modal/index';
import ResizableTable from '@/components/Table';
import { showConfirmModal, useAction } from '@/hooks/action';
import { useColumn } from '@/hooks/column';
import KeepAlive from '@/components/KeepAlive';
import Import from '@/components/Import/index';
import useExport from '@/hooks/useExport';
import {
  getGuideList,
  getGuideDetail,
  downloadGuide,
  exportGuides,
  getImportTemplate,
  deleteGuide,
} from '@/services/guide';
import { preColumns, beforeColumns } from './columns/ProductGuide';
import GuideDetail, { RefType as DetailRefType } from './GuideDetail';
import GuideProducts, { RefType as ProductRefType } from './GuideProducts';
import GuideEdit, { RefType as EditType } from './GuideEdit';

const GeneralGuides: React.FC = () => {
  const access = useAccess();
  const actionRef = useRef<ActionType>();
  const modalRef = useRef<RefType>(null);
  const formRef = useRef<ProFormInstance>();
  const modalFormRef = useRef<ProFormInstance>();
  const editRef = useRef<EditType>(null);
  const intl = useIntl();
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const { createColumns } = useColumn();

  const { createOptimazationActions } = useAction('commonOperationGuidanceId');
  const detailRef = useRef<DetailRefType>(null);
  const productRef = useRef<ProductRefType>(null);

  const { run: handleExport, loading: exportLoading } = useExport(
    exportGuides,
    formRef,
    intl.formatMessage({ id: 'webOperation_guide_export_filename_text' }),
  );

  // 点击详情按钮
  const showDetail = async (id: string) => {
    setActionLoading(true);
    try {
      const res = await getGuideDetail(id);
      setActionLoading(false);
      detailRef.current?.show(res);
    } catch (e) {
      setActionLoading(false);
    }
  };
  // 点击编辑按钮
  const editGuide = async (id: string) => {
    setActionLoading(true);
    try {
      const res = await getGuideDetail(id);
      setActionLoading(false);
      editRef.current?.edit(res, 'edit');
    } catch (e) {
      setActionLoading(false);
    }
  };

  // 点击查看按钮
  const downloadFile = async (id: string) => {
    setActionLoading(true);
    try {
      const res = await downloadGuide(id);
      setActionLoading(false);
      window.open(res.data.info);
    } catch (e) {
      setActionLoading(false);
    }
  };

  const relativeColumn: ProColumns = {
    title: <FormattedMessage id="webOperation_page_relativeProduct_drawer_title" />,
    dataIndex: 'relative',
    valueType: 'option',
    hideInSearch: true,
    width: 120,
    align: 'center',
    render: (_, record) => (
      <Access accessible={access.canViewRelativeProductGuide()}>
        <Button
          key="relative"
          type="primary"
          size="small"
          onClick={() => {
            productRef.current?.show(record);
          }}
        >
          <FormattedMessage id="webCommon_page_view_common_text" />
        </Button>
      </Access>
    ),
  };
  const actionColumn: ProColumns<API.GuideItem> = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 250,
    fixed: 'right',
    render: (_, record) => {
      const columns = [
        // 详情
        {
          name: 'canViewGuideApply',
          langCode: 'webCommon_page_detail_common_text',
          onClick: showDetail,
        },
        // 编辑
        {
          name: 'canUpdateGuideApply',
          langCode: 'webCommon_page_edit_common_text',
          onClick: editGuide,
        },
        // 查看
        {
          name: 'canDownloadGuideApply',
          langCode: 'webCommon_page_view_common_text',
          onClick: downloadFile,
        },
        // 删除
        {
          name: 'canDeleteGuideApply',
          langCode: 'webCommon_page_delete_tableButton_linkText',
          onClick: (id: string) => {
            showConfirmModal(modalRef, { api: deleteGuide, id }, 'guide_delete');
          },
        },
      ];
      return createOptimazationActions(columns, record, 'product');
    },
  };

  const initColumns = createColumns('guide', [
    ...preColumns,
    relativeColumn,
    ...beforeColumns,
    actionColumn,
  ]);

  return (
    <>
      <Spin spinning={actionLoading}>
        <ResizableTable<API.GuideItem, API.GuideItemPageParams>
          actionRef={actionRef}
          formRef={formRef}
          rowKey="commonOperationGuidanceId"
          defaultSize="small"
          search={{
            labelWidth: 'auto',
          }}
          headerTitle={
            <Space>
              <Access accessible={access.canExportGuide()}>
                <Button
                  key="export"
                  loading={exportLoading}
                  type="primary"
                  ghost
                  onClick={handleExport}
                >
                  <FormattedMessage id="webCommon_page_export_button_text" />
                </Button>
              </Access>
              <Access accessible={access.canImportGuide()}>
                <Import
                  key="import"
                  pageName="guide"
                  buttonName="webCommon_page_import_button_text"
                  getTemplate={getImportTemplate}
                  url="/operation-platform/common/operationGuidance/import"
                  urlParam="file"
                  refresh={() => {
                    return actionRef.current?.reload();
                  }}
                />
              </Access>
              <GuideEdit
                ref={editRef}
                key="create"
                refresh={() => {
                  return actionRef.current?.reload();
                }}
              />
            </Space>
          }
          scroll={{ x: 1300 }}
          request={getGuideList}
          columns={initColumns}
        />
      </Spin>
      <GuideDetail ref={detailRef} key="detail" />
      <GuideProducts ref={productRef} key="product" />
      <Modal ref={modalRef} parentRef={actionRef} formRef={modalFormRef} />
    </>
  );
};

export default () => {
  return (
    <KeepAlive>
      <GeneralGuides />
    </KeepAlive>
  );
};
