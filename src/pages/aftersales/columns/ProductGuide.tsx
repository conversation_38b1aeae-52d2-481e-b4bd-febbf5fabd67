import type { typeColumns } from '@/hooks/column';
import { ProFormText } from '@ant-design/pro-components';
import { extra, dateFilter, creatorFilter } from '@/hooks/column';
import { getIntl } from 'umi';

// 列表字段
export const preColumns: typeColumns[] = [
  ...dateFilter,
  {
    dataIndex: 'instanceId',
    hideInTable: true,
  },
  {
    dataIndex: 'name',
    hideInTable: true,
  },
  {
    dataIndex: ['operationGuidance', 'instanceId'],
    width: 170,
    langCode: 'webOperation_guide_instanceId_tableColumn_text',
    hideInSearch: true,
  },
  {
    dataIndex: ['operationGuidance', 'name'],
    width: 150,
    langCode: 'webOperation_guide_name_tableColumn_text',
    render: (_, record) => (
      <a href={record?.operationGuidance?.url} target="_blank">
        {record?.operationGuidance?.name}
      </a>
    ),
    hideInSearch: true,
  },
  {
    dataIndex: ['operationGuidance', 'nameLangId'],
    width: 170,
    hideInSearch: true,
    langCode: 'webOperation_guide_nameLangId_tableColumn_text',
  },
  {
    dataIndex: ['operationGuidance', 'description'],
    width: 150,
    hideInSearch: true,
    langCode: 'webOperation_guide_desc_tableColumn_text',
  },
  {
    dataIndex: ['operationGuidance', 'descriptionLangId'],
    width: 170,
    hideInSearch: true,
    langCode: 'webOperation_guide_descLangId_tableColumn_text',
  },
];
export const beforeColumns: typeColumns[] = [
  {
    dataIndex: ['operationGuidance', 'url'],
    width: 150,
    hideInSearch: true,
    langCode: 'webOperation_guide_address_tableColumn_text',
  },
  {
    dataIndex: ['operationGuidance', 'format'],
    width: 150,
    hideInSearch: true,
    langCode: 'webOperation_guide_format_tableColumn_text',
  },
  {
    dataIndex: ['operationGuidance', 'size'],
    width: 150,
    hideInSearch: true,
    langCode: 'webOperation_guide_size_tableColumn_text',
    render: (_, record) =>
      record.operationGuidance?.size
        ? Math.floor(record.operationGuidance?.size / 1024) + 'KB'
        : null,
  },
  ...creatorFilter,
  ...extra,
];
// 添加方式为文件上传时的相关表单字段
export const fileColumns = (method = 'create') => {
  return [
    {
      dataIndex: ['operationGuidance', 'name'],
      showInForm: true,
      required: true,
      langCode: 'webOperation_guide_name_tableColumn_text',
    },
    {
      dataIndex: ['operationGuidance', 'nameLangId'],
      showInForm: method !== 'create' ? true : false,
      readonly: true,
      langCode: 'webOperation_guide_nameLangId_tableColumn_text',
    },
    {
      dataIndex: ['operationGuidance', 'description'],
      showInForm: true,
      langCode: 'webOperation_guide_desc_tableColumn_text',
    },
    {
      dataIndex: ['operationGuidance', 'descriptionLangId'],
      showInForm: method !== 'create' ? true : false,
      readonly: true,
      langCode: 'webOperation_guide_descLangId_tableColumn_text',
    },
    {
      dataIndex: ['operationGuidance', 'format'],
      showInForm: true,
      readonly: true,
      langCode: 'webOperation_guide_format_tableColumn_text',
    },
    {
      dataIndex: ['operationGuidance', 'fileSize'],
      showInForm: true,
      readonly: true,
      langCode: 'webOperation_guide_size_tableColumn_text',
    },
    {
      dataIndex: ['operationGuidance', 'size'],
      showInForm: true,
      langCode: 'webOperation_guide_size_tableColumn_text',
      renderInEditForm: () => {
        return <ProFormText key="size" name={['operationGuidance', 'size']} hidden />;
      },
    },
    {
      dataIndex: ['operationGuidance', 'url'],
      showInForm: true,
      readonly: true,
      hiddenInForm: true,
      langCode: 'webOperation_guide_address_tableColumn_text',
    },
    {
      dataIndex: ['operationGuidance', 'uploadFileName'],
      showInForm: true,
      readonly: true,
      hiddenInForm: true,
      langCode: 'webOperation_guide_address_tableColumn_text',
    },
  ];
};
// 添加方式为添加链接时的相关表单字段
export const linkColumns = (method = 'create') => {
  return [
    {
      dataIndex: ['operationGuidance', 'name'],
      showInForm: true,
      required: true,
      langCode: 'webOperation_guide_name_tableColumn_text',
    },
    {
      dataIndex: ['operationGuidance', 'nameLangId'],
      showInForm: method !== 'create' ? true : false,
      readonly: true,
      langCode: 'webOperation_guide_nameLangId_tableColumn_text',
    },
    {
      dataIndex: ['operationGuidance', 'description'],
      showInForm: true,
      langCode: 'webOperation_guide_desc_tableColumn_text',
    },
    {
      dataIndex: ['operationGuidance', 'descriptionLangId'],
      showInForm: method !== 'create' ? true : false,
      readonly: true,
      langCode: 'webOperation_guide_descLangId_tableColumn_text',
    },
  ];
};
// 详情页显示字段
export const detailColumns = (typeCode = '1'): typeColumns[] => {
  return [
    {
      dataIndex: ['operationGuidance', 'instanceId'],
      showInForm: true,
      readonly: true,
      langCode: 'webOperation_guide_instanceId_tableColumn_text',
    },
    {
      dataIndex: ['operationGuidance', 'typeCode'],
      showInForm: true,
      readonly: true,
      langCode: 'webOperation_guide_typeCode_tableColumn_text',
      valueType: 'radio',
      valueEnum: {
        1: getIntl().formatMessage({
          id: 'webOperation_dictionary_localFileUpload_select_text',
        }),
        2: getIntl().formatMessage({
          id: 'webOperation_dictionary_S3FileAddress_select_text',
        }),
      },
    },
    // 文件上传方式
    {
      dataIndex: ['operationGuidance', 'url'],
      showInForm: typeCode === '1' ? true : false,
      readonly: true,
      langCode: 'webOperation_guide_file_tableColumn_text',
    },
    // 添加链接方式
    {
      dataIndex: ['operationGuidance', 'url'],
      showInForm: typeCode === '2' ? true : false,
      readonly: true,
      langCode: 'webOperation_guide_url_tableColumn_text',
    },
    {
      dataIndex: ['operationGuidance', 'name'],
      showInForm: true,
      readonly: true,
      langCode: 'webOperation_guide_name_tableColumn_text',
    },
    {
      dataIndex: ['operationGuidance', 'nameLangId'],
      showInForm: true,
      readonly: true,
      langCode: 'webOperation_guide_nameLangId_tableColumn_text',
    },
    {
      dataIndex: ['operationGuidance', 'description'],
      showInForm: true,
      readonly: true,
      langCode: 'webOperation_guide_desc_tableColumn_text',
    },
    {
      dataIndex: ['operationGuidance', 'descriptionLangId'],
      showInForm: true,
      readonly: true,
      langCode: 'webOperation_guide_descLangId_tableColumn_text',
    },
    {
      dataIndex: ['operationGuidance', 'format'],
      showInForm: typeCode === '1' ? true : false,
      readonly: true,
      langCode: 'webOperation_guide_format_tableColumn_text',
    },
    {
      dataIndex: ['operationGuidance', 'fileSize'],
      showInForm: typeCode === '1' ? true : false,
      readonly: true,
      langCode: 'webOperation_guide_size_tableColumn_text',
    },
  ];
};

export const productColumns = (categoryValueEnum: Record<string, string>): typeColumns[] => [
  {
    dataIndex: ['operationGuidance', 'name'],
    showInForm: true,
    readonly: true,
    hideInTable: true,
    hideInSearch: true,
    langCode: 'webOperation_guide_name_tableColumn_text',
  },
  {
    dataIndex: ['operationGuidance', 'description'],
    showInForm: true,
    readonly: true,
    hideInTable: true,
    hideInSearch: true,
    langCode: 'webOperation_guide_desc_tableColumn_text',
  },
  {
    dataIndex: 'productId',
    width: 170,
    render: (_, record) => record.id ?? record.productId,
  },
  {
    dataIndex: 'categoryId',
    width: 120,
    valueType: 'select',
    valueEnum: categoryValueEnum,
  },
  {
    dataIndex: 'model',
    width: 100,
  },
  {
    dataIndex: 'commodityModel',
    width: 140,
  },
  {
    dataIndex: 'productName',
    width: 150,
    hideInTable: true,
  },
  {
    dataIndex: 'productNameLanguage',
    langCode: 'webOperation_product_productName_tableColumn_text',
    width: 140,
    hideInSearch: true,
    render: (_, record) => record.productNameLanguage ?? record.productName,
  },
];
