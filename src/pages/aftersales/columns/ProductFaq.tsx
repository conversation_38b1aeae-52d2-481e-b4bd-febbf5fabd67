import type { typeColumns } from '@/hooks/column';
import { extra, dateFilter, creatorFilter } from '@/hooks/column';
import { len5000 } from '@/utils/validate';
import { getIntl } from 'umi';
// 列表字段
export const preColumns: typeColumns[] = [
  ...dateFilter,
  {
    dataIndex: ['faq', 'instanceId'],
    width: 170,
    langCode: 'webOperation_faq_instanceId_tableColumn_text',
  },
  {
    dataIndex: ['faq', 'typeCode'],
    width: 100,
    showInForm: true,
    required: true,
    langCode: 'webOperation_faq_typeCode_tableColumn_text',
    valueType: 'select',
    valueEnum: {
      1: getIntl().formatMessage({ id: 'webOperation_dictionary_question1_select_text' }),
      2: getIntl().formatMessage({ id: 'webOperation_dictionary_question2_select_text' }),
      3: getIntl().formatMessage({ id: 'webOperation_dictionary_question3_select_text' }),
      common: getIntl().formatMessage({ id: 'webOperation_dictionary_common_select_text' }),
      notCommon: getIntl().formatMessage({ id: 'webOperation_dictionary_notCommon_select_text' }),
    },
  },
  {
    dataIndex: ['faq', 'title'],
    width: 150,
    showInForm: true,
    required: true,
    langCode: 'webOperation_faq_title_tableColumn_text',
  },
  {
    dataIndex: ['faq', 'titleLangId'],
    width: 170,
    hideInSearch: true,
    langCode: 'webOperation_faq_titleLangId_tableColumn_text',
  },
  {
    dataIndex: ['faq', 'answer'],
    width: 170,
    hideInSearch: true,
    showInForm: true,
    required: true,
    langCode: 'webOperation_faq_answer_tableColumn_text',
    valueType: 'textarea',
    rule: len5000,
  },
  {
    dataIndex: ['faq', 'answerLangId'],
    width: 170,
    hideInSearch: true,
    langCode: 'webOperation_faq_answerLangId_tableColumn_text',
  },
];
export const beforeColumns: typeColumns[] = [...creatorFilter, ...extra];

// 表单字段
export const formColumns = (mode = 'edit'): typeColumns[] => [
  {
    dataIndex: ['faq', 'instanceId'],
    showInForm: true,
    readonly: true,
    langCode: 'webOperation_faq_instanceId_tableColumn_text',
  },
  {
    dataIndex: ['faq', 'typeCode'],
    showInForm: true,
    required: mode === 'edit' ? true : false,
    readonly: mode === 'edit' ? false : true,
    langCode: 'webOperation_faq_typeCode_tableColumn_text',
    valueType: 'select',
    valueEnum: {
      1: getIntl().formatMessage({ id: 'webOperation_dictionary_question1_select_text' }),
      2: getIntl().formatMessage({ id: 'webOperation_dictionary_question2_select_text' }),
      3: getIntl().formatMessage({ id: 'webOperation_dictionary_question3_select_text' }),
      common: getIntl().formatMessage({ id: 'webOperation_dictionary_common_select_text' }),
      notCommon: getIntl().formatMessage({ id: 'webOperation_dictionary_notCommon_select_text' }),
    },
  },
  {
    dataIndex: ['faq', 'title'],
    showInForm: true,
    required: mode === 'edit' ? true : false,
    readonly: mode === 'edit' ? false : true,
    langCode: 'webOperation_faq_title_tableColumn_text',
  },
  {
    dataIndex: ['faq', 'titleLangId'],
    showInForm: true,
    readonly: true,
    langCode: 'webOperation_faq_titleLangId_tableColumn_text',
  },
  {
    dataIndex: ['faq', 'answer'],
    showInForm: true,
    required: mode === 'edit' ? true : false,
    readonly: mode === 'edit' ? false : true,
    langCode: 'webOperation_faq_answer_tableColumn_text',
    valueType: 'textarea',
    rule: len5000,
  },
  {
    dataIndex: ['faq', 'answerLangId'],
    showInForm: true,
    readonly: true,
    langCode: 'webOperation_faq_answerLangId_tableColumn_text',
  },
];

export const productColumns = (categoryValueEnum: Record<string, string>): typeColumns[] => [
  {
    dataIndex: ['faq', 'typeCode'],
    showInForm: true,
    readonly: true,
    hideInTable: true,
    hideInSearch: true,
    langCode: 'webOperation_faq_typeCode_tableColumn_text',
    valueType: 'select',
    valueEnum: {
      1: getIntl().formatMessage({ id: 'webOperation_dictionary_question1_select_text' }),
      2: getIntl().formatMessage({ id: 'webOperation_dictionary_question2_select_text' }),
      3: getIntl().formatMessage({ id: 'webOperation_dictionary_question3_select_text' }),
      common: getIntl().formatMessage({ id: 'webOperation_dictionary_common_select_text' }),
      notCommon: getIntl().formatMessage({ id: 'webOperation_dictionary_notCommon_select_text' }),
    },
  },
  {
    dataIndex: ['faq', 'title'],
    showInForm: true,
    readonly: true,
    hideInTable: true,
    hideInSearch: true,
    langCode: 'webOperation_faq_title_tableColumn_text',
  },
  {
    dataIndex: 'productId',
    width: 170,
    render: (_, record) => record.id ?? record.productId,
  },
  {
    dataIndex: 'categoryId',
    width: 120,
    valueType: 'select',
    valueEnum: categoryValueEnum,
  },
  {
    dataIndex: 'model',
    width: 100,
  },
  {
    dataIndex: 'commodityModel',
    width: 140,
  },
  {
    dataIndex: 'productName',
    width: 140,
  },
];
