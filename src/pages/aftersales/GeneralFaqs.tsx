import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProFormInstance } from '@ant-design/pro-components';
import { Spin, Button, Space } from 'antd';
import React, { useRef, useState } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { omit } from 'lodash-es';
import { Access } from '@@/plugin-access/access';
import Modal, { RefType } from '@/components/Modal/index';
import ResizableTable, { beforeSearchSubmit } from '@/components/Table';
import { OptionColumn, showConfirmModal, useAction } from '@/hooks/action';
import { useColumn } from '@/hooks/column';
import KeepAlive from '@/components/KeepAlive';
import Import from '@/components/Import/index';
import useExport from '@/hooks/useExport';
import { getFaqList, getFaqDetail, exportFaqs, getImportTemplate, deleteFaq } from '@/services/faq';
import { preColumns, beforeColumns } from './columns/ProductFaq';
import FaqProducts, { RefType as ProductRefType } from './FaqProducts';
import FaqEdit, { RefType as EditType } from './FaqEdit';

const GeneralFaqs: React.FC = () => {
  const access = useAccess();
  const actionRef = useRef<ActionType>();
  const modalRef = useRef<RefType>(null);
  const formRef = useRef<ProFormInstance>();
  const modalFormRef = useRef<ProFormInstance>();
  const editRef = useRef<EditType>(null);
  const intl = useIntl();
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const { createColumns } = useColumn();

  const { createOptimazationActions } = useAction('commonFaqId');

  const productRef = useRef<ProductRefType>(null);

  const { run: handleExport, loading: exportLoading } = useExport(
    exportFaqs,
    formRef,
    intl.formatMessage({ id: 'webOperation_faq_export_filename_text' }),
  );
  // 点击详情按钮
  const showDetail = async (id: string) => {
    setActionLoading(true);
    try {
      const res = await getFaqDetail(id);
      setActionLoading(false);
      editRef.current?.edit(res, 'detail');
    } catch (e) {
      setActionLoading(false);
    }
  };
  // 点击编辑按钮
  const editGuide = async (id: string) => {
    setActionLoading(true);
    try {
      const res = await getFaqDetail(id);
      setActionLoading(false);
      editRef.current?.edit(res, 'edit');
    } catch (e) {
      setActionLoading(false);
    }
  };

  const relativeColumn: ProColumns = {
    title: <FormattedMessage id="webOperation_page_relativeProduct_drawer_title" />,
    dataIndex: 'relative',
    valueType: 'option',
    hideInSearch: true,
    width: 120,
    align: 'center',
    render: (_, record) => (
      <Access accessible={access.canViewRelativeProductFaq()}>
        <Button
          key="relative"
          type="primary"
          size="small"
          onClick={() => {
            productRef.current?.show(record);
          }}
        >
          <FormattedMessage id="webCommon_page_view_common_text" />
        </Button>
      </Access>
    ),
  };
  const actionColumn: ProColumns<API.GuideItem> = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 250,
    fixed: 'right',
    render: (_, record) => {
      const columns: OptionColumn[] = [
        // 详情
        {
          name: 'canViewFaqApply',
          langCode: 'webCommon_page_detail_common_text',
          onClick: showDetail,
        },
        // 编辑
        {
          name: 'canUpdateFaqApply',
          langCode: 'webCommon_page_edit_common_text',
          onClick: editGuide,
        },
        // 删除
        {
          name: 'canDeleteFaqApply',
          langCode: 'webCommon_page_delete_tableButton_linkText',
          onClick: (id: string) => {
            showConfirmModal(modalRef, { api: deleteFaq, id }, 'faq_delete');
          },
        },
      ];
      return createOptimazationActions(columns, record, 'product');
    },
  };
  const initColumns = createColumns('faq', [
    ...preColumns,
    relativeColumn,
    ...beforeColumns,
    actionColumn,
  ]);

  return (
    <>
      <Spin spinning={actionLoading}>
        <ResizableTable<API.FaqItem, API.FaqItemPageParams>
          actionRef={actionRef}
          formRef={formRef}
          rowKey="commonFaqId"
          defaultSize="small"
          search={{
            labelWidth: 'auto',
          }}
          headerTitle={
            <Space>
              <Access accessible={access.canExportFaq()}>
                <Button
                  key="export"
                  loading={exportLoading}
                  type="primary"
                  ghost
                  onClick={handleExport}
                >
                  <FormattedMessage id="webCommon_page_export_button_text" />
                </Button>
              </Access>
              <Access accessible={access.canImportFaq()}>
                <Import
                  key="import"
                  pageName="faq"
                  buttonName="webCommon_page_import_button_text"
                  getTemplate={getImportTemplate}
                  url="/operation-platform/common/faq/import"
                  urlParam="file"
                  refresh={() => {
                    return actionRef.current?.reload();
                  }}
                />
              </Access>
              <FaqEdit
                ref={editRef}
                key="create"
                refresh={() => {
                  return actionRef.current?.reload();
                }}
              />
            </Space>
          }
          scroll={{ x: 1300 }}
          request={getFaqList}
          columns={initColumns}
          beforeSearchSubmit={(params) => {
            return {
              ...omit(beforeSearchSubmit(params), ['faq']),
              ...params.faq,
            };
          }}
        />
      </Spin>
      <FaqProducts ref={productRef} key="product" />
      <Modal ref={modalRef} parentRef={actionRef} formRef={modalFormRef} />
    </>
  );
};

export default () => {
  return (
    <KeepAlive>
      <GeneralFaqs />
    </KeepAlive>
  );
};
