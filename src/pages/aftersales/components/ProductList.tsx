import React, { useState } from 'react';
import { useIntl, useAccess, FormattedMessage } from 'umi';
import { Access } from '@@/plugin-access/access';
import { Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { omit } from 'lodash-es';

import ResizableTable from '@/components/Table';
import FormContainer from '@/components/Form/FormContainer';
import { useColumn } from '@/hooks/column';

import { getProductList } from '@/services/product';
import { productColumns } from '../columns/ProductGuide';

export interface ProductProps {
  setData: (list: API.ProductItem[]) => void;
  selectedKeys?: string[];
  moduleName?: string;
  categoryValueEnum: Record<string, any>;
}

const ProductList = React.forwardRef<APP.RefType, ProductProps>((props, ref) => {
  const [selectedValues, setSelectedValues] = useState<API.ProductItem[]>([]);
  const { setData, selectedKeys, moduleName = 'Common', categoryValueEnum } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const intl = useIntl();
  const access = useAccess();
  const { createColumns } = useColumn();
  const initColumns = createColumns('product', productColumns(categoryValueEnum));

  const onSelectChange = (newSelectedRowKeys: React.Key[], selectedRows: API.ProductItem[]) => {
    setSelectedValues(selectedRows);
  };

  const rowSelection = {
    preserveSelectedRowKeys: true,
    onChange: onSelectChange,
    getCheckboxProps: (record: API.ProductItem) => ({
      disabled: selectedKeys?.includes(record.id!),
    }),
  };

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: 'webOperation_product_relative_button_text' })}
        width="50%"
        onCancel={() => {
          setVisible(false);
        }}
        onConfirm={() => {
          setData(selectedValues);
          setVisible(false);
        }}
        open={visible}
        destroyOnClose={true}
      >
        <ResizableTable<API.ProductItem, API.ProductPageParams>
          rowKey="id"
          defaultSize="small"
          search={{
            defaultCollapsed: false,
            collapseRender: false,
            labelWidth: 'auto',
          }}
          scroll={{ x: 1300 }}
          rowSelection={rowSelection}
          request={getProductList}
          columns={initColumns}
          beforeSearchSubmit={(params) => {
            return {
              ...omit(params, 'productId'),
              id: params.productId,
            };
          }}
        />
      </FormContainer>
      <Access accessible={access['canAddRelativeProduct' + moduleName]?.()}>
        <Button
          key="product"
          type="primary"
          onClick={() => {
            setSelectedValues([]);
            setVisible(true);
          }}
        >
          <PlusOutlined />
          <FormattedMessage id="webOperation_product_relative_button_text" />
        </Button>
      </Access>
    </>
  );
});

export default ProductList;
