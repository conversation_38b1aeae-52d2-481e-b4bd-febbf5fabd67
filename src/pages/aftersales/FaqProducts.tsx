import { ProForm } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import React, { useImperativeHandle, useState, useRef } from 'react';
import { useIntl, FormattedMessage, useAccess } from 'umi';
import { Space, Divider, message, Spin } from 'antd';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { map, get } from 'lodash-es';

import ResizableTable from '@/components/Table';
import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';
import { useColumn } from '@/hooks/column';
import Modal, { RefType as modalRefType } from '@/components/Modal/index';
import { showDeleteModal } from '@/hooks/action';
import { useCategories } from '@/hooks/selectHooks';
import ProductList from './components/ProductList';
import {
  getFaqProducts,
  addFaqProducts,
  deleteFaqProduct,
  getAllFaqProducts,
} from '@/services/faq';
import { productColumns } from './columns/ProductFaq';

export interface RefType {
  show: (record: API.FaqItem, mode?: string) => void;
}

const FaqProducts = React.forwardRef<RefType>((props, ref) => {
  const actionRef = useRef<ActionType>();
  const intl = useIntl();
  const access = useAccess();
  const modalRef = useRef<modalRefType>(null);
  const productRef = useRef<APP.RefType>(null);
  const [visible, setVisible] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState<API.FaqItem>({});
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<string[]>([]);
  const [method, setMethod] = useState<string>('edit');
  const { createColumns } = useColumn();
  const { valueEnum: categoryValueEnum } = useCategories();
  const { run: handleSubmit } = useRequest(
    async (values) => {
      if (values && values.length > 0) {
        setActionLoading(true);
        // 转换成接口参数要求的格式
        const products = map(values, (item) => item.id);
        await addFaqProducts({
          commonId: initialValues.commonFaqId!,
          relatedProduct: products,
        });
        setActionLoading(false);
        message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
        actionRef.current?.reload();
      }
    },
    { manual: true },
  );

  const onClose = () => {
    setVisible(false);
  };

  const getData = async (params = {}) => {
    const res = await getFaqProducts(params);
    const all = await getAllFaqProducts({ req: get(params, 'commonId') });
    setSelectedData(all?.data);
    return res;
  };

  useImperativeHandle(ref, () => ({
    show: (record: API.FaqItem, mode?: string) => {
      if (mode) {
        setMethod(mode);
      }
      setInitialValues(record);
      setVisible(true);
    },
  }));

  const actionColumn: ProColumns = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 90,
    render: (_, record) => (
      <Space key="option" split={<Divider type="vertical" />}>
        <Access accessible={access.canDeleteRelativeProductFaq()}>
          <a
            key="delete"
            onClick={() =>
              showDeleteModal(
                modalRef,
                {
                  api: deleteFaqProduct,
                  id: { commonId: initialValues.commonFaqId!, relatedProduct: [record.productId] },
                },
                'relativeProduct_delete',
              )
            }
          >
            <FormattedMessage id="webCommon_page_delete_tableButton_linkText" />
          </a>
        </Access>
      </Space>
    ),
  };
  const initColumns = createColumns(
    'product',
    method === 'edit'
      ? [...productColumns(categoryValueEnum), actionColumn]
      : productColumns(categoryValueEnum),
  );
  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: 'webOperation_page_relativeProduct_drawer_title' })}
        width="60%"
        onCancel={onClose}
        hiddenConfirm={true}
        open={visible}
        destroyOnClose={true}
      >
        <ProForm
          initialValues={initialValues}
          labelCol={{ span: 3 }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
        >
          <FormFields columns={productColumns(categoryValueEnum)} pageName="faq" />
        </ProForm>
        <Spin spinning={actionLoading}>
          <ResizableTable<API.ProductItem, API.ProductPageParams>
            actionRef={actionRef}
            rowKey="productId"
            defaultSize="small"
            search={{
              labelWidth: 'auto',
            }}
            columns={initColumns}
            headerTitle={
              method === 'edit' ? (
                <ProductList
                  ref={productRef}
                  key="addProduct"
                  moduleName="Faq"
                  selectedKeys={selectedData}
                  setData={handleSubmit}
                  categoryValueEnum={categoryValueEnum}
                />
              ) : (
                false
              )
            }
            scroll={{ x: 1300 }}
            request={getData}
            params={{ commonId: initialValues.commonFaqId }}
          />
        </Spin>
      </FormContainer>
      <Modal ref={modalRef} parentRef={actionRef} />
    </>
  );
});

export default FaqProducts;
