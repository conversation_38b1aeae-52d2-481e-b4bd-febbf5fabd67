import { ProForm } from '@ant-design/pro-components';
import React, { useImperativeHandle, useState, useRef } from 'react';
import { useIntl, useAccess, FormattedMessage } from 'umi';
import { Access } from '@@/plugin-access/access';
import { Button } from 'antd';
import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';
import type { typeColumns } from '@/hooks/column';
import { detailColumns } from './columns/ProductGuide';
import GuideProducts, { RefType as ProductRefType } from './GuideProducts';
export interface RefType {
  show: (record: API.GuideItem) => void;
}

const GuideDetail = React.forwardRef<RefType>((props, ref) => {
  const intl = useIntl();
  const access = useAccess();
  const [visible, setVisible] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState<API.GuideItem>({});
  const [columns, setColumns] = useState<typeColumns[]>([]);
  const productRef = useRef<ProductRefType>(null);
  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    show: (record: API.GuideItem) => {
      record.testGroupList = record.testGroup?.join('，');
      if (record.operationGuidance?.typeCode === '1') {
        record.operationGuidance.fileSize =
          Math.floor((record.operationGuidance?.size || 0) / 1024) + 'KB';
      }
      setColumns(detailColumns(record.operationGuidance?.typeCode || '1'));
      setInitialValues(record);
      setVisible(true);
    },
  }));

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: 'webCommon_page_detail_common_text' })}
        width="50%"
        onCancel={onClose}
        hiddenConfirm={true}
        open={visible}
        destroyOnClose={true}
      >
        <ProForm
          initialValues={initialValues}
          labelCol={{ span: 7 }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
        >
          <FormFields columns={columns} pageName="guide" />
          <Access accessible={access.canViewRelativeProductGuide()}>
            <ProForm.Item
              name="relativeProduct"
              label={intl.formatMessage({
                id: 'webOperation_page_relativeProduct_drawer_title',
              })}
            >
              <Button
                key="relative"
                type="primary"
                size="small"
                onClick={() => {
                  productRef.current?.show(initialValues, 'view');
                }}
              >
                <FormattedMessage id="webCommon_page_view_common_text" />
              </Button>
            </ProForm.Item>
          </Access>
        </ProForm>
        <GuideProducts ref={productRef} key="product" />
      </FormContainer>
    </>
  );
});

export default GuideDetail;
