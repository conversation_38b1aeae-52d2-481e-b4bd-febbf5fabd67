// 框架依赖引入
import React, { useImperativeHandle, useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { Button, message } from 'antd';
import {
  ProForm,
  ProFormInstance,
  ProFormRadio,
  ProFormDependency,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { PlusOutlined } from '@ant-design/icons';
import { assign, get, omit, set } from 'lodash-es';
// 公共自定义依赖引入
import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';
import type { typeColumns } from '@/hooks/column';
import { FileUpload } from '@/components/Form/FileUpload';
import { getS3File, uploadImage } from '@/services/common';
import { urlChecked, len500 } from '@/utils/validate';
// 页面自定义依赖引入
import { createGuide, editGuide } from '@/services/guide';
import { fileColumns, linkColumns } from './columns/ProductGuide';

export interface RefType {
  edit: (record: API.GuideItem, method: string) => void;
}

const GuideEdit = React.forwardRef<RefType, APP.EditFormProps>((props, ref) => {
  const { refresh } = props;
  const intl = useIntl();
  const formRef = useRef<ProFormInstance<API.GuideItem>>();
  const [visible, setVisible] = useState<boolean>(false);
  const [method, setMethod] = useState<string>('create');
  const [columns, setColumns] = useState<typeColumns[]>([]);
  const [initialValues, setInitialValues] = useState<API.GuideItem>({} as any);
  const access = useAccess();

  const { run: handleSubmit, loading } = useRequest(
    async (values) => {
      if (String(values?.operationGuidance?.typeCode) === '2') {
        const { key } = await getS3File(values.operationGuidance.url as string);
        values.operationGuidance.uploadFileName = key;
      }
      switch (method) {
        case 'create':
          values.operationGuidance = omit(values.operationGuidance, 'fileSize');
          await createGuide(values);
          break;
        case 'edit':
          values.commonOperationGuidanceId = initialValues.commonOperationGuidanceId;
          values.operationGuidance.operationGuidanceId = values.operationGuidance.instanceId;
          values.operationGuidance = omit(values.operationGuidance, 'instanceId', 'fileSize');
          await editGuide(values);
          break;
        default:
          break;
      }
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setVisible(false);
      refresh?.();
    },
    { manual: true },
  );

  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    edit: (record: API.GuideItem, mode: string) => {
      // 设置页面表单功能模式
      setMethod(mode);
      // 设置显示字段
      if (record.operationGuidance?.typeCode === '1') {
        record.operationGuidance.fileSize =
          Math.floor((record.operationGuidance?.size || 0) / 1024) + 'KB';
        setColumns(fileColumns(mode));
      } else if (record.operationGuidance?.typeCode === '2') {
        setColumns(linkColumns(mode));
      }
      // 设置表单字段初始值
      const typeCode = get(record, ['operationGuidance', 'typeCode']);
      if (typeCode === '2') {
        set(record, ['operationGuidance', 'format'], undefined);
        set(record, ['operationGuidance', 'size'], undefined);
      }

      setInitialValues(record || {});
      setVisible(true);
    },
  }));

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: `webOperation_guide_${method}_drawer_title` })}
        width="50%"
        onCancel={onClose}
        onConfirm={() => formRef.current?.submit()}
        open={visible}
        destroyOnClose={true}
        loading={loading}
      >
        <ProForm
          initialValues={initialValues}
          onFinish={handleSubmit}
          formRef={formRef}
          layout="horizontal"
          labelCol={{ span: 5 }}
          onReset={onClose}
          submitter={false}
        >
          {method !== 'create' ? (
            <ProFormText
              name={['operationGuidance', 'instanceId']}
              label={intl.formatMessage({ id: 'webOperation_guide_instanceId_tableColumn_text' })}
              readonly
            />
          ) : null}
          <ProFormRadio.Group
            name={['operationGuidance', 'typeCode']}
            initialValue="1"
            rules={[
              {
                required: true,
                message: <FormattedMessage id="webOperation_guide_typeCode_input_placeholder" />,
              },
            ]}
            fieldProps={{
              onChange: (e) => {
                if (e.target.value === '1') {
                  // 设置文件相关属性字段
                  setColumns(fileColumns(method));
                } else if (e.target.value === '2') {
                  // 设置链接相关属性字段
                  setColumns(linkColumns(method));
                }
              },
            }}
            label={intl.formatMessage({
              id: 'webOperation_guide_typeCode_tableColumn_text',
            })}
            valueEnum={{
              1: intl.formatMessage({ id: 'webOperation_dictionary_localFileUpload_select_text' }),
              2: intl.formatMessage({ id: 'webOperation_dictionary_S3FileAddress_select_text' }),
            }}
          />
          <ProFormDependency name={['operationGuidance', 'typeCode']}>
            {({ operationGuidance = {} }) => {
              const { typeCode } = operationGuidance;
              /**
               * 文件上传方式
               */
              if (typeCode === '1') {
                // 文件上传组件
                return (
                  <ProForm.Item
                    name="file"
                    label={intl.formatMessage({
                      id: 'webOperation_guide_file_tableColumn_text',
                    })}
                    initialValue={initialValues.operationGuidance?.url}
                    rules={[
                      {
                        required: true,
                        message: <FormattedMessage id="webCommon_page_file_select_placeholder" />,
                      },
                    ]}
                    transform={(value: any) => ({})}
                  >
                    <FileUpload
                      maxCount={1}
                      presigned
                      fileName={initialValues.operationGuidance?.name}
                      value={initialValues.operationGuidance?.url}
                      accept=".mp4"
                      getSignedUrl={async ({ name, size, type }) => {
                        try {
                          const data = await uploadImage({
                            fileName: name || '',
                            fileType: 'picture',
                          });

                          formRef.current?.setFieldsValue({
                            operationGuidance: assign(
                              {},
                              operationGuidance,
                              { format: type },
                              { name },
                              {
                                fileSize:
                                  size === undefined ? '' : Math.floor((size || 0) / 1024) + 'KB',
                              },
                              { size },
                              { uploadFileName: data.key },
                              { url: data.key },
                            ),
                          });
                          return data;
                        } catch (e) {
                          message.error(
                            intl.formatMessage({ id: 'webCommon_page_uploadFailed_toast_message' }),
                          );
                        }
                      }}
                    />
                  </ProForm.Item>
                );
              }
              /**
               * 添加链接方式
               */
              // 链接文本组件
              return (
                <ProFormTextArea
                  name={['operationGuidance', 'url']}
                  label={intl.formatMessage({ id: 'webOperation_guide_url_tableColumn_text' })}
                  rules={[
                    {
                      required: true,
                      message:
                        intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                        intl.formatMessage({ id: 'webOperation_guide_url_tableColumn_text' }),
                    },
                    len500,
                    urlChecked,
                  ]}
                  placeholder={
                    intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                    intl.formatMessage({ id: 'webOperation_guide_url_tableColumn_text' })
                  }
                />
              );
            }}
          </ProFormDependency>
          <FormFields columns={columns} pageName="guide" />
        </ProForm>
      </FormContainer>
      <Access accessible={access.canCreateGuide()}>
        <Button
          type="primary"
          onClick={() => {
            // 页面表单功能为添加新协议
            setMethod('create');
            // 创建时的表单字段
            setColumns(fileColumns('create'));
            setInitialValues({} as any);
            setVisible(true);
          }}
        >
          <PlusOutlined />
          <FormattedMessage id="webOperation_guide_create_drawer_title" />
        </Button>
      </Access>
    </>
  );
});

export default GuideEdit;
