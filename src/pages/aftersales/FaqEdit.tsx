// 框架依赖引入
import React, { useImperativeHandle, useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { Button, message } from 'antd';
import { ProForm, ProFormInstance } from '@ant-design/pro-components';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { PlusOutlined } from '@ant-design/icons';
import { omit } from 'lodash-es';
// 公共自定义依赖引入
import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';
import type { typeColumns } from '@/hooks/column';
// 页面自定义依赖引入
import { createFaq, editFaq } from '@/services/faq';
import { formColumns, preColumns } from './columns/ProductFaq';

export interface RefType {
  edit: (record: API.FaqItem, method: string) => void;
}

const FaqEdit = React.forwardRef<RefType, APP.EditFormProps>((props, ref) => {
  const { refresh } = props;
  const intl = useIntl();
  const formRef = useRef<ProFormInstance<API.FaqItem>>();
  const [visible, setVisible] = useState<boolean>(false);
  const [method, setMethod] = useState<string>('create');
  const [columns, setColumns] = useState<typeColumns[]>([]);
  const [initialValues, setInitialValues] = useState<API.FaqItem>({} as any);
  const [showConfirm, setShowConfirm] = useState({} as any);
  const access = useAccess();

  const { run: handleSubmit, loading } = useRequest(
    async (values) => {
      switch (method) {
        case 'create':
          await createFaq(values);
          break;
        case 'edit':
          values.commonFaqId = initialValues.commonFaqId;
          values.faq.faqId = values.faq.instanceId;
          values.faq = omit(values.faq, 'instanceId');
          await editFaq(values);
          break;
        default:
          break;
      }
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setVisible(false);
      refresh?.();
    },
    { manual: true },
  );

  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    edit: (record: API.FaqItem, mode: string) => {
      // 设置页面表单功能模式
      setMethod(mode);
      if (mode === 'edit') {
        setShowConfirm({ onConfirm: () => formRef.current?.submit() });
      } else if (mode === 'detail') {
        setShowConfirm({ hiddenConfirm: true });
      }
      // // 设置显示字段
      setColumns(formColumns(mode));
      // 设置表单字段初始值
      record.testGroupList = record.testGroup?.join('，');
      setInitialValues(record || {});
      setVisible(true);
    },
  }));

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: `webOperation_faq_${method}_drawer_title` })}
        width="50%"
        onCancel={onClose}
        {...showConfirm}
        open={visible}
        destroyOnClose={true}
        loading={loading}
      >
        <ProForm
          initialValues={initialValues}
          onFinish={handleSubmit}
          formRef={formRef}
          layout="horizontal"
          labelCol={{ span: 5 }}
          onReset={onClose}
          submitter={false}
        >
          <FormFields columns={columns} pageName="faq" />
        </ProForm>
      </FormContainer>
      {/* 仅作详情页用时，不显示创建按钮 */}
      {refresh ? (
        <Access accessible={access.canCreateFaq()}>
          <Button
            type="primary"
            onClick={() => {
              // 页面表单功能为添加新协议
              setMethod('create');
              setShowConfirm({ onConfirm: () => formRef.current?.submit() });
              // 创建时的表单字段
              setColumns(preColumns);
              setInitialValues({} as any);
              setVisible(true);
            }}
          >
            <PlusOutlined />
            <FormattedMessage id="webOperation_faq_create_drawer_title" />
          </Button>
        </Access>
      ) : null}
    </>
  );
});

export default FaqEdit;
