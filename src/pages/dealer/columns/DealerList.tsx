import type { typeColumns } from '@/hooks/column';
import { extra, dateFilter } from '@/hooks/column';
import { ProFormDependency } from '@ant-design/pro-components';
import { FormattedMessage, getIntl } from '@@/plugin-locale/localeExports';
import { digitChecked, urlChecked } from '@/utils/validate';
export const listColumns = (mode = 'new'): typeColumns[] => [
  ...dateFilter,
  {
    dataIndex: 'dealerId',
    width: 160,
    showInForm: mode === 'new' ? false : true,
    readonly: true,
  },
  {
    dataIndex: 'name',
    width: 150,
    showInForm: true,
    required: true,
    addonRender:
      mode === 'edit' ? (
        <ProFormDependency name={['nameLangId']}>
          {({ nameLangId }) => {
            return (
              <span>
                <FormattedMessage id="webCommon_page_langId_common_text" />
                {nameLangId}
              </span>
            );
          }}
        </ProFormDependency>
      ) : undefined,
  },
  {
    dataIndex: 'country',
    width: 150,
    showInForm: true,
    hideInSearch: true,
    addonRender:
      mode === 'edit' ? (
        <ProFormDependency name={['countryLangId']}>
          {({ countryLangId }) => {
            return (
              <span>
                <FormattedMessage id="webCommon_page_langId_common_text" />
                {countryLangId}
              </span>
            );
          }}
        </ProFormDependency>
      ) : undefined,
  },
  {
    dataIndex: 'state',
    width: 150,
    showInForm: true,
    hideInSearch: true,
    addonRender:
      mode === 'edit' ? (
        <ProFormDependency name={['stateLangId']}>
          {({ stateLangId }) => {
            return (
              <span>
                <FormattedMessage id="webCommon_page_langId_common_text" />
                {stateLangId}
              </span>
            );
          }}
        </ProFormDependency>
      ) : undefined,
  },
  {
    dataIndex: 'city',
    width: 150,
    showInForm: true,
    hideInSearch: true,
    addonRender:
      mode === 'edit' ? (
        <ProFormDependency name={['cityLangId']}>
          {({ cityLangId }) => {
            return (
              <span>
                <FormattedMessage id="webCommon_page_langId_common_text" />
                {cityLangId}
              </span>
            );
          }}
        </ProFormDependency>
      ) : undefined,
  },
  {
    dataIndex: 'address',
    showInForm: true,
    required: true,
    hideInSearch: true,
    hideInTable: true,
    addonRender:
      mode === 'edit' ? (
        <ProFormDependency name={['addressLangId']}>
          {({ addressLangId }) => {
            return (
              <span>
                <FormattedMessage id="webCommon_page_langId_common_text" />
                {addressLangId}
              </span>
            );
          }}
        </ProFormDependency>
      ) : undefined,
  },
  {
    dataIndex: 'zipcode',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
  },
  {
    dataIndex: 'telephone',
    width: 160,
    showInForm: true,
  },
  {
    dataIndex: 'email',
    width: 150,
    showInForm: true,
  },
  {
    dataIndex: 'website',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
    rule: urlChecked,
  },
  {
    dataIndex: 'hours',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
    valueType: 'digit',
    fieldProps: { addonAfter: <FormattedMessage id="webOperation_dealer_hour_label_text" /> },
  },
  {
    dataIndex: 'lat',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
    required: true,
    rule: digitChecked,
  },
  {
    dataIndex: 'lng',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
    required: true,
    rule: digitChecked,
  },
  {
    dataIndex: 'category',
    showInForm: true,
    hideInTable: true,
    required: true,
    valueType: mode === 'list' ? 'select' : 'checkbox',
    fieldProps: { mode: 'multiple', showArrow: true },
    valueEnum: {
      'Ace Hardware': getIntl().formatMessage({
        id: 'webOperation_dictionary_aceHardware_select_text',
      }),
      'Service Location': getIntl().formatMessage({
        id: 'webOperation_dictionary_serviceLocation_select_text',
      }),
      "Lowe's": getIntl().formatMessage({ id: 'webOperation_dictionary_lowes_select_text' }),
      Dealer: getIntl().formatMessage({ id: 'webOperation_dictionary_dealer_select_text' }),
    },
  },
  {
    dataIndex: 'zoomLevel',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
    valueType: 'digit',
    fieldProps: { precision: 0 },
  },
  {
    dataIndex: 'color',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
  },
  {
    dataIndex: 'fontClass',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
  },
  {
    dataIndex: 'isActive',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
  },
  {
    dataIndex: 'image',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
  },
  {
    dataIndex: 'storeLocatorId',
    showInForm: true,
    hideInSearch: true,
    hideInTable: true,
  },
  ...extra,
];

export const viewColumns: typeColumns[] = [
  {
    dataIndex: 'dealerId',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'name',
    showInForm: true,
    readonly: true,
    extraRender: (
      <ProFormDependency name={['nameLangId']}>
        {({ nameLangId }) => {
          return (
            <span>
              <FormattedMessage id="webCommon_page_langId_common_text" />
              {nameLangId}
            </span>
          );
        }}
      </ProFormDependency>
    ),
  },
  {
    dataIndex: 'country',
    showInForm: true,
    readonly: true,
    extraRender: (
      <ProFormDependency name={['countryLangId']}>
        {({ countryLangId }) => {
          return (
            <span>
              <FormattedMessage id="webCommon_page_langId_common_text" />
              {countryLangId}
            </span>
          );
        }}
      </ProFormDependency>
    ),
  },
  {
    dataIndex: 'state',
    showInForm: true,
    readonly: true,
    extraRender: (
      <ProFormDependency name={['stateLangId']}>
        {({ stateLangId }) => {
          return (
            <span>
              <FormattedMessage id="webCommon_page_langId_common_text" />
              {stateLangId}
            </span>
          );
        }}
      </ProFormDependency>
    ),
  },
  {
    dataIndex: 'city',
    showInForm: true,
    readonly: true,
    extraRender: (
      <ProFormDependency name={['cityLangId']}>
        {({ cityLangId }) => {
          return (
            <span>
              <FormattedMessage id="webCommon_page_langId_common_text" />
              {cityLangId}
            </span>
          );
        }}
      </ProFormDependency>
    ),
  },
  {
    dataIndex: 'address',
    showInForm: true,
    readonly: true,
    extraRender: (
      <ProFormDependency name={['addressLangId']}>
        {({ addressLangId }) => {
          return (
            <span>
              <FormattedMessage id="webCommon_page_langId_common_text" />
              {addressLangId}
            </span>
          );
        }}
      </ProFormDependency>
    ),
  },
  {
    dataIndex: 'zipcode',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'telephone',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'email',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'website',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'hours',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'lat',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'lng',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'category',
    showInForm: true,
    readonly: true,
    valueType: 'select',
    valueEnum: {
      'Ace Hardware': getIntl().formatMessage({
        id: 'webOperation_dictionary_aceHardware_select_text',
      }),
      'Service Location': getIntl().formatMessage({
        id: 'webOperation_dictionary_serviceLocation_select_text',
      }),
      "Lowe's": getIntl().formatMessage({ id: 'webOperation_dictionary_lowes_select_text' }),
      Dealer: getIntl().formatMessage({ id: 'webOperation_dictionary_dealer_select_text' }),
    },
  },
  {
    dataIndex: 'zoomLevel',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'color',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'fontClass',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'isActive',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'image',
    showInForm: true,
    readonly: true,
  },
  {
    dataIndex: 'storeLocatorId',
    showInForm: true,
    readonly: true,
  },
];
