import React, { useRef, useState } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Divider, Modal, Space, message } from 'antd';
import { useRequest } from '@@/plugin-request/request';
import { Access } from '@@/plugin-access/access';
import { ProFormInstance } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import ResizableTable from '@/components/Table';
import Import from '@/components/Import/index';
import { createKAC } from '@/components/KeepAlive';

import DealerEdit, { RefType } from './DealerEdit';
import { deleteDealer, getList, getImportTemplate } from '@/services/dealer';
import { listColumns } from './columns/DealerList';
import { useColumn } from '@/hooks/column';

const DealerList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const editRef = useRef<RefType>(null);
  const { createColumns } = useColumn();
  const intl = useIntl();
  const access = useAccess();
  const [dealerId, setDealerId] = useState<string | undefined>();

  const { run: handleDelete, loading } = useRequest(
    async () => {
      if (!dealerId) return;
      await deleteDealer(dealerId);
      setDealerId(undefined);
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      actionRef.current?.reload();
    },
    { manual: true },
  );

  const actionColumn: ProColumns = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 200,
    fixed: 'right',
    render: (_, record) => [
      <Space key="edit" split={<Divider type="vertical" />}>
        <Access accessible={access.canViewDealer()}>
          <a
            key="view"
            onClick={() => {
              editRef.current?.edit(record, 'view');
            }}
          >
            <FormattedMessage id="webCommon_page_view_common_text" />
          </a>
        </Access>
        <Access accessible={access.canEditDealer()}>
          <a
            key="edit"
            onClick={() => {
              editRef.current?.edit(record, 'edit');
            }}
          >
            <FormattedMessage id="webCommon_page_edit_common_text" />
          </a>
        </Access>
        <Access accessible={access.canDeleteDealer()}>
          <a
            key="delete"
            onClick={() => {
              setDealerId(record.dealerId);
            }}
          >
            <FormattedMessage id="webCommon_page_delete_tableButton_linkText" />
          </a>
        </Access>
      </Space>,
    ],
  };

  const initColumns = [...listColumns('list'), actionColumn];
  const allColumns = createColumns('dealer', initColumns);

  return (
    <>
      <ResizableTable<API.DealerItem, API.DealerPageParams>
        actionRef={actionRef}
        formRef={formRef}
        rowKey="dealerId"
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        headerTitle={
          <Space>
            <Access accessible={access.canImportDealer()}>
              <Import
                key="import"
                pageName="dealer"
                getTemplate={getImportTemplate}
                url="/operation-platform/dealer/import"
                urlParam="file"
                refresh={() => {
                  return actionRef.current?.reload();
                }}
              />
            </Access>
            <DealerEdit
              ref={editRef}
              key="create"
              refresh={() => {
                return actionRef.current?.reload();
              }}
            />
          </Space>
        }
        scroll={{ x: 1300 }}
        request={getList}
        columns={allColumns}
      />
      <Modal
        title={intl.formatMessage({ id: 'webCommon_page_confirmToDelete_modal_title' })}
        open={!!dealerId}
        onOk={handleDelete}
        onCancel={() => setDealerId(undefined)}
        confirmLoading={loading}
      >
        <FormattedMessage id="webOperation_dealer_delete_modal_message" />
      </Modal>
    </>
  );
};
export default createKAC(DealerList);
