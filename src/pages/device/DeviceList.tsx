import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProFormInstance } from '@ant-design/pro-components';
import { Button, Divider, message, Modal, Space, Tooltip } from 'antd';
import React, { useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl, useHistory, getIntl } from 'umi';
import { toLower, omit, join } from 'lodash-es';

import ResizableTable from '@/components/Table';
import { typeColumns, useColumn } from '@/hooks/column';
import { useCategories, useBrands } from '@/hooks/selectHooks';
import { exportDevices, getDeviceList, stopDevice } from '@/services/device';
import { downloadByData } from '@/utils/download';
import KeepAlive from '@/components/KeepAlive';
const DeviceList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const intl = useIntl();
  const access = useAccess();
  const history = useHistory();
  const [visible, setVisible] = useState<boolean>(false);
  const { createColumns } = useColumn();
  const { valueEnum: categoryValueEnum } = useCategories();
  const { valueEnum: brandValueEnum } = useBrands();
  const [deviceItem, setDeviceItem] = useState<API.DeviceItem>();
  const { run: handleEditStatus, loading: stopLoading } = useRequest(stopDevice, { manual: true });

  const listColumns: typeColumns[] = [
    {
      dataIndex: 'firstBindTime',
      valueType: 'dateRange',
      hideInTable: true,
      showLabel: true,
      fieldProps: {
        placeholder: [
          getIntl().formatMessage({
            id: 'webCommon_page_select_common_placeholder',
          }),
          getIntl().formatMessage({
            id: 'webCommon_page_select_common_placeholder',
          }),
        ],
      },
    },
    {
      dataIndex: 'lastLoginTime',
      valueType: 'dateRange',
      hideInTable: true,
      showLabel: true,
      fieldProps: {
        placeholder: [
          getIntl().formatMessage({
            id: 'webCommon_page_select_common_placeholder',
          }),
          getIntl().formatMessage({
            id: 'webCommon_page_select_common_placeholder',
          }),
        ],
      },
    },
    {
      dataIndex: 'activationTime',
      valueType: 'dateRange',
      hideInTable: true,
      showLabel: true,
      fieldProps: {
        placeholder: [
          getIntl().formatMessage({
            id: 'webCommon_page_select_common_placeholder',
          }),
          getIntl().formatMessage({
            id: 'webCommon_page_select_common_placeholder',
          }),
        ],
      },
    },
    {
      dataIndex: 'deviceId',
      width: 150,
    },
    {
      dataIndex: 'sn',
      width: 150,
    },
    {
      dataIndex: 'pid',
      width: 170,
    },
    {
      dataIndex: 'categoryId',
      width: 150,
      valueEnum: categoryValueEnum,
    },
    {
      dataIndex: 'brandId',
      width: 150,
      valueEnum: brandValueEnum,
    },
    {
      dataIndex: 'productModel',
      width: 150,
    },
    {
      dataIndex: 'commodityModel',
      width: 150,
    },
    {
      dataIndex: 'productType',
      width: 150,
      valueEnum: {
        gatewaySubDevice: intl.formatMessage({
          id: 'webOperation_dictionary_gatewaySubDevice_select_text',
        }),
        oldIotDevice: intl.formatMessage({
          id: 'webOperation_dictionary_oldIotDevice_select_text',
        }),
        directConnectedDevice: intl.formatMessage({
          id: 'webOperation_dictionary_directConnectedDevice_select_text',
        }),
        gatewayDevice: intl.formatMessage({
          id: 'webOperation_dictionary_gatewayDevice_select_text',
        }),
        notIotDevice: intl.formatMessage({
          id: 'webOperation_dictionary_notIotDevice_select_text',
        }),
      },
    },
    {
      dataIndex: 'devicePhoneNum',
      width: 150,
    },
    {
      dataIndex: 'isOnline',
      width: 150,
      valueEnum: {
        ONLINE: {
          text: intl.formatMessage({
            id: 'webOperation_dictionary_online_select_text',
          }),
          status: 'Success',
        },
        OFFLINE: {
          text: intl.formatMessage({
            id: 'webOperation_dictionary_offline_select_text',
          }),
          status: 'Default',
        },
      },
    },
    {
      dataIndex: 'status',
      width: 150,
      valueEnum: {
        DISABLE: {
          text: intl.formatMessage({
            id: 'webOperation_dictionary_fault_select_text',
          }),
          status: 'Default',
        },
        NORMAL: {
          text: intl.formatMessage({
            id: 'webOperation_dictionary_normal_select_text',
          }),
          status: 'Success',
        },
      },
    },
    {
      dataIndex: 'currentBindBusinessType',
      width: 150,
      valueEnum: {
        1: intl.formatMessage({ id: 'webOperation_dictionary_EGOConnect_select_text' }),
        2: intl.formatMessage({ id: 'webOperation_dictionary_fleetService_select_text' }),
      },
      fieldProps: {
        mode: 'multiple',
        showArrow: true,
      },
    },
    {
      dataIndex: 'currentBindEgoUserId',
      width: 150,
      render: (_, record) => (
        <Tooltip placement="topLeft" title={join(record.currentBindEgoUserId, '，')}>
          {join(record.currentBindEgoUserId, '，')}
        </Tooltip>
      ),
    },
    {
      dataIndex: 'currentBindFleetCompanyId',
      width: 150,
    },
    {
      dataIndex: 'usageStatus',
      width: 150,
      valueEnum: {
        ACTIVE: {
          text: intl.formatMessage({
            id: 'webOperation_dictionary_active_select_text',
          }),
          status: 'Success',
        },
        INACTIVE: {
          text: intl.formatMessage({
            id: 'webOperation_dictionary_inactive_select_text',
          }),
          status: 'Default',
        },
      },
    },
    {
      dataIndex: 'firstBindBusinessType',
      width: 150,
      valueEnum: {
        1: intl.formatMessage({ id: 'webOperation_dictionary_EGOConnect_select_text' }),
        2: intl.formatMessage({ id: 'webOperation_dictionary_fleetService_select_text' }),
      },
      fieldProps: {
        mode: 'multiple',
        showArrow: true,
      },
    },
    {
      dataIndex: 'firstBindEgoUserId',
      width: 150,
    },
    {
      dataIndex: 'firstBindFleetCompanyId',
      width: 150,
    },
    {
      dataIndex: 'firstBindFleetUserId',
      width: 200,
    },
    {
      dataIndex: 'activationTime',
      width: 150,
      hideInSearch: true,
      valueType: 'dateTime',
    },
    {
      dataIndex: 'firstBindTime',
      width: 150,
      hideInSearch: true,
      valueType: 'dateTime',
    },
    {
      dataIndex: 'lastLoginTime',
      width: 150,
      hideInSearch: true,
      valueType: 'dateTime',
    },
    {
      dataIndex: 'nickName',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      dataIndex: 'productSnCode',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      dataIndex: 'communicateMode',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      dataIndex: 'configurationTable',
      hideInSearch: true,
      hideInTable: true,
    },
  ];
  const { run: handleExport, loading: exportLoading } = useRequest(
    async (values) => {
      const params = formRef.current?.getFieldsFormatValue?.();
      const file = await exportDevices({
        ...omit(params, ['activationTime', 'lastLoginTime']),
        activationStartTime: params.activationTime ? params.activationTime?.[0] : undefined,
        activationEndTime: params.activationTime ? params.activationTime?.[1] : undefined,
        lastLoginStartTime: params.lastLoginTime ? params.lastLoginTime?.[0] : undefined,
        lastLoginEndTime: params.lastLoginTime ? params.lastLoginTime?.[1] : undefined,
      });
      downloadByData({
        data: file,
        filename: intl.formatMessage({ id: 'webOperation_device_export_filename_text' }) + '.csv',
      });
    },
    { manual: true },
  );
  const editDeviceStatus = async () => {
    if (deviceItem) {
      // 设备状态值集合
      const deviceStatus = ['DISABLE', 'NORMAL'];
      // 取差集
      const status = deviceStatus.filter(
        (item: string) => ![deviceItem.status].some((current) => current === item),
      );
      // 修改设备状态(启用->停用；停用->启用)
      await handleEditStatus(deviceItem.deviceId, status.join(''));
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setVisible(false);
      actionRef.current?.reload();
    }
  };

  const showDetail = (id: string) => {
    if (window['__POWERED_BY_QIANKUN__']) {
      window.router.push({
        path: `/operation/device/${id}`,
      });
    } else {
      history.push({
        pathname: `/device/${id}`,
      });
    }
  };

  const showStopDialog = (record: API.DeviceItem) => {
    setVisible(true);
    setDeviceItem(record);
  };

  const actionColumn: ProColumns<API.DeviceItem> = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 120,
    fixed: 'right',
    render: (_, record) => [
      <Space key="option" split={<Divider type="vertical" />}>
        <Access accessible={access.canViewDeviceDetail()}>
          <a onClick={() => showDetail(record.deviceId)}>
            <FormattedMessage id="webCommon_page_detail_common_text" />
          </a>
        </Access>
        <Access accessible={access.canStopDevice()}>
          <a onClick={() => showStopDialog(record)}>
            <FormattedMessage
              id={
                record.status === 'NORMAL'
                  ? 'webCommon_page_disable_tableButton_linkText'
                  : 'webCommon_page_enable_tableButton_linkText'
              }
            />
          </a>
        </Access>
      </Space>,
    ],
  };
  const initColumns = [...listColumns, actionColumn];

  const allColumns = createColumns('device', initColumns);

  return (
    <>
      <ResizableTable<API.DeviceItem, API.DevicePageParams>
        actionRef={actionRef}
        formRef={formRef}
        rowKey={(_, index: number | undefined) => String(index)}
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        request={getDeviceList}
        scroll={{ x: 2700 }}
        columns={allColumns}
        headerTitle={
          <Access accessible={access.canExportDevice()}>
            <Button loading={exportLoading} type="primary" onClick={handleExport}>
              <FormattedMessage id="webCommon_page_export_button_text" />
            </Button>
          </Access>
        }
        beforeSearchSubmit={(params) => {
          return {
            ...omit(params, ['activationTime', 'lastLoginTime', 'firstBindTime']),
            activationStartTime: params.activationTime ? params.activationTime?.[0] : undefined,
            activationEndTime: params.activationTime ? params.activationTime?.[1] : undefined,
            lastLoginStartTime: params.lastLoginTime ? params.lastLoginTime?.[0] : undefined,
            lastLoginEndTime: params.lastLoginTime ? params.lastLoginTime?.[1] : undefined,
            firstBindStartTime: params.firstBindTime ? params.firstBindTime?.[0] : undefined,
            firstBindEndTime: params.firstBindTime ? params.firstBindTime?.[1] : undefined,
          };
        }}
      />

      <Modal
        title={intl.formatMessage({
          id: `webOperation_device_${toLower(deviceItem?.status || 'normal')}_modal_title`,
        })}
        open={visible}
        onOk={editDeviceStatus}
        onCancel={() => setVisible(false)}
        confirmLoading={stopLoading}
      >
        <FormattedMessage
          id={`webOperation_device_${toLower(deviceItem?.status || 'normal')}_modal_message`}
        />
      </Modal>
    </>
  );
};
export default () => {
  return (
    <KeepAlive>
      <DeviceList />
    </KeepAlive>
  );
};
