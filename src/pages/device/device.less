.deviceWrapper {
  position: relative;
  margin-bottom: 30px;
}

.deviceExpand {
  display: flex;
  justify-content: center;
  margin-bottom: -16px;
}

.deviceContent {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 10px;
  overflow: hidden;
  line-height: 20px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.deviceValue {
  overflow: hidden;
  color: #000;
  font-weight: 500;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.copyContent {
  display: inline-block;
  width: 70%;
  overflow: hidden;
  color: #000;
  font-weight: 500;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.deviceUp {
  transform: rotate(-90deg);
  cursor: pointer;
  transition: transform 0.5s;
}

.deviceDown {
  transform: rotate(90deg);
  cursor: pointer;
  transition: transform 0.5s;
}
.iotDeviceBaseInfo {
  display: flex;
}

.iotDeviceContent {
  margin-left: 12px;
}

.iotDeviceDetail {
  margin: 9px 0;
}

.configString {
  display: inline-block;
  width: 75%;
  overflow: hidden;
  color: #000;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.content {
  margin-bottom: 10px;
  line-height: 20px;
}

.expand {
  display: flex;
  justify-content: center;
  margin-bottom: -16px;
}

.up {
  transform: rotate(-90deg);
  cursor: pointer;
  transition: transform 0.5s;
}

.down {
  transform: rotate(90deg);
  cursor: pointer;
  transition: transform 0.5s;
}

.logViewer {
  min-height: 350px;
  max-height: 350px;
  padding-bottom: 20px;
  overflow-y: scroll;
  background-color: #282c34;
}
.logSwitch {
  margin-right: 30px;
}
.logSwitchLabel {
  margin-right: 5px;
  color: #77bc1f;
  font-weight: bold;
}
.logPageContainer {
  margin-top: 0 !important;
}

.noBorder {
  padding-top: 0 !important;
  border-bottom: none !important;
}

.flexCenter {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
