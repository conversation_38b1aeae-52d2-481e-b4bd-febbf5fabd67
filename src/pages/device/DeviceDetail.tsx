import { FormattedMessage } from '@@/plugin-locale/localeExports';
import { useRequest } from '@@/plugin-request/request';
import { DoubleRightOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, Col, Row, Tabs } from 'antd';
import { includes, map } from 'lodash-es';
import { useState, useEffect } from 'react';
import { useIntl, useParams } from 'umi';

import PartList from '@/pages/device/components/PartList';
import styles from '@/pages/device/device.less';
import { getDeviceDetail } from '@/services/device';

const { TabPane } = Tabs;

const BasicInfo = ({ detail }: { detail: API.DeviceItem }) => {
  const [show, toggleShow] = useState<boolean>(false);
  const intl = useIntl();

  useEffect(() => {
    const domList = document.querySelectorAll('.operation-ant-userdevice');
    if (domList && domList.length > 0) {
      map(domList, (dom) => {
        const classes = dom.className.split(' ');
        if (!includes(classes, 'operation-ant-tooltip-hidden')) {
          classes.push('operation-ant-tooltip-hidden');
          dom.className = classes.join(' ');
        }
      });
    }
  }, []);

  const networkModesEnums = {
    notNetworked: intl.formatMessage({
      id: 'webOperation_dictionary_notNetworked_select_text',
    }),
    wifi: intl.formatMessage({
      id: 'webOperation_dictionary_wifi_select_text',
    }),
    '4G': intl.formatMessage({
      id: 'webOperation_dictionary_4G_select_text',
    }),
    '4GAndBle': intl.formatMessage({
      id: 'webOperation_dictionary_4GAndBle_select_text',
    }),
    lan: intl.formatMessage({
      id: 'webOperation_dictionary_lan_select_text',
    }),
    ble: intl.formatMessage({
      id: 'webOperation_dictionary_ble_select_text',
    }),
    wifiAndBle: intl.formatMessage({
      id: 'webOperation_dictionary_wifiAndBle_select_text',
    }),
    DOrT: intl.formatMessage({
      id: 'webOperation_dictionary_dOrT_select_text',
    }),
  };
  const deviceTypeEnums = {
    gatewaySubDevice: intl.formatMessage({
      id: 'webOperation_dictionary_gatewaySubDevice_select_text',
    }),
    directConnectedDevice: intl.formatMessage({
      id: 'webOperation_dictionary_directConnectedDevice_select_text',
    }),
    gatewayDevice: intl.formatMessage({
      id: 'webOperation_dictionary_gatewayDevice_select_text',
    }),
  };

  const data = [
    {
      label: <FormattedMessage id="webOperation_device_deviceId_tableColumn_text" />,
      value: detail?.deviceId,
    },
    {
      label: <FormattedMessage id="webOperation_device_sn_tableColumn_text" />,
      value: detail?.sn,
    },
    {
      label: <FormattedMessage id="webOperation_device_pid_tableColumn_text" />,
      value: detail?.pid,
    },
    {
      label: <FormattedMessage id="webOperation_device_categoryName_tableColumn_text" />,
      value: detail?.categoryName,
    },
    {
      label: <FormattedMessage id="webOperation_device_brandName_tableColumn_text" />,
      value: detail?.brandName,
    },
    {
      label: <FormattedMessage id="webOperation_device_nickName_tableColumn_text" />,
      value: detail?.nickName,
    },
    {
      label: <FormattedMessage id="webOperation_device_productModel_tableColumn_text" />,
      value: detail?.productModel,
    },
    {
      label: <FormattedMessage id="webOperation_device_commodityModel_tableColumn_text" />,
      value: detail?.commodityModel,
    },
    {
      label: <FormattedMessage id="webOperation_device_productType_tableColumn_text" />,
      value: deviceTypeEnums[detail?.productType],
    },
    {
      label: <FormattedMessage id="webOperation_device_productSnCode_tableColumn_text" />,
      value: detail?.productSnCode,
    },
    {
      label: <FormattedMessage id="webOperation_device_communicateMode_tableColumn_text" />,
      value: networkModesEnums[detail?.communicateMode],
    },
  ];

  return (
    <div className={styles.deviceWrapper}>
      <Card>
        <Row gutter={16}>
          {map(data, (item, index) => {
            return index < 6 || show ? (
              <Col span={8} key={index} className={styles.deviceContent}>
                <span>{item.label}</span>：<span className={styles.deviceValue}>{item.value}</span>
              </Col>
            ) : null;
          })}
        </Row>
        <div className={styles.deviceExpand}>
          {show ? (
            <a onClick={() => toggleShow(false)}>
              <DoubleRightOutlined className={styles.deviceUp} />
            </a>
          ) : (
            <a onClick={() => toggleShow(true)}>
              <DoubleRightOutlined className={styles.deviceDown} />
            </a>
          )}
        </div>
      </Card>
    </div>
  );
};

const DeviceDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [detail, setDetail] = useState<API.DeviceItem>({} as any);
  const { run: getDevice } = useRequest(getDeviceDetail, { manual: true });

  useEffect(() => {
    setDetail({} as any);
    getDevice(id).then((res) => {
      setDetail(res);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  return (
    <PageContainer title={false} breadcrumb={undefined} style={{ background: '#fff', margin: 0 }}>
      <BasicInfo detail={detail} key="basic" />

      <Tabs type="card" size="small">
        <TabPane tab={<FormattedMessage id="webOperation_device_detail_tabs_text" />} key="detail">
          <PartList detail={detail} />
        </TabPane>
      </Tabs>
    </PageContainer>
  );
};

export default DeviceDetail;
