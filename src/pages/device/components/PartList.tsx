import ResizableTable from '@/components/Table';
import { Access } from '@@/plugin-access/access';
import { DoubleRightOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { Card, Col, Row, Tooltip } from 'antd';
import { map } from 'lodash-es';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { FormattedMessage, useAccess, useParams, useIntl } from 'umi';

import { useColumn } from '@/hooks/column';
import UpgradeList, { RefType } from '@/pages/device/components/UpgradeList';
import styles from '@/pages/device/device.less';
import { getDeviceComponents } from '@/services/device';
import { listColumns } from '../columns/PartList';

const BasicInfo = ({ detail }: { detail: API.DeviceItem }) => {
  const [show, toggleShow] = useState<boolean>(false);
  const intl = useIntl();

  const deviceStatusEnums = {
    gatewaySubDevice: intl.formatMessage({
      id: 'webOperation_dictionary_gatewaySubDevice_select_text',
    }),
    directConnectedDevice: intl.formatMessage({
      id: 'webOperation_dictionary_directConnectedDevice_select_text',
    }),
    gatewayDevice: intl.formatMessage({
      id: 'webOperation_dictionary_gatewayDevice_select_text',
    }),
  };
  const deviceActiveStatusEnums = {
    ACTIVE: intl.formatMessage({
      id: 'webOperation_dictionary_active_select_text',
    }),
    INACTIVE: intl.formatMessage({
      id: 'webOperation_dictionary_inactive_select_text',
    }),
  };
  const businessTypeEnums = {
    1: intl.formatMessage({
      id: 'webOperation_dictionary_EGOConnect_select_text',
    }),
    2: intl.formatMessage({
      id: 'webOperation_dictionary_fleetService_select_text',
    }),
  };

  const data = [
    {
      label: <FormattedMessage id="webOperation_device_usageStatus_tableColumn_text" />,
      value: deviceActiveStatusEnums[detail?.usageStatus],
    },
    {
      label: <FormattedMessage id="webOperation_device_status_tableColumn_text" />,
      value: deviceStatusEnums[detail?.status],
    },
    {
      label: <FormattedMessage id="webOperation_device_customVersion_tableColumn_text" />,
      value: detail?.customVersion ? detail.customVersion : '--',
    },
    {
      label: <FormattedMessage id="webOperation_device_activationTime_tableColumn_text" />,
      value: detail?.activationTime
        ? dayjs(detail?.activationTime).format('YYYY-MM-DD HH:mm:ss')
        : '',
    },
    {
      label: <FormattedMessage id="webOperation_device_lastLoginTime_tableColumn_text" />,
      value: detail?.lastLoginTime
        ? dayjs(detail?.lastLoginTime).format('YYYY-MM-DD HH:mm:ss')
        : '',
    },
    {
      label: <FormattedMessage id="webOperation_device_currentBindBusinessType_tableColumn_text" />,
      value: businessTypeEnums[detail?.currentBindBusinessType],
    },
    {
      label: <FormattedMessage id="webOperation_device_currentBindEgoUserId_tableColumn_text" />,
      value: (
        <Tooltip placement="topLeft" title={detail?.currentBindEgoUserId?.join('，')}>
          {detail?.currentBindEgoUserId?.join('，')}
        </Tooltip>
      ),
    },
    {
      label: (
        <FormattedMessage id="webOperation_device_currentBindFleetCompanyId_tableColumn_text" />
      ),
      value: detail?.currentBindFleetCompanyId,
    },
    {
      label: <FormattedMessage id="webOperation_device_firstBindBusinessType_tableColumn_text" />,
      value: businessTypeEnums[detail?.firstBindBusinessType],
    },
    {
      label: <FormattedMessage id="webOperation_device_firstBindEgoUserId_tableColumn_text" />,
      value: detail?.firstBindEgoUserId,
    },
    {
      label: <FormattedMessage id="webOperation_device_firstBindTime_tableColumn_text" />,
      value: detail?.firstBindTime
        ? dayjs(detail?.firstBindTime).format('YYYY-MM-DD HH:mm:ss')
        : '',
    },
    {
      label: <FormattedMessage id="webOperation_device_firstBindFleetCompanyId_tableColumn_text" />,
      value: detail?.firstBindFleetCompanyId,
    },
    {
      label: <FormattedMessage id="webOperation_device_firstBindFleetUserId_tableColumn_text" />,
      value: detail?.firstBindFleetUserId,
    },
  ];

  return (
    <div className={styles.iotDeviceDetail}>
      <Card>
        <Row gutter={16}>
          {map(data, (item, index) => {
            return index < 6 || show ? (
              <Col span={8} key={index} className={styles.deviceContent}>
                <span>{item.label}：</span>
                <span className={styles.deviceValue}>{item.value}</span>
              </Col>
            ) : null;
          })}
        </Row>
        <div className={styles.deviceExpand}>
          {show ? (
            <a onClick={() => toggleShow(false)}>
              <DoubleRightOutlined className={styles.deviceUp} />
            </a>
          ) : (
            <a onClick={() => toggleShow(true)}>
              <DoubleRightOutlined className={styles.deviceDown} />
            </a>
          )}
        </div>
      </Card>
    </div>
  );
};

const DevicePartList = ({ detail }: { detail: API.DeviceItem }) => {
  const { id } = useParams<{ id: string }>();
  const upgradeRef = useRef<RefType>(null);
  const access = useAccess();
  const { createColumns } = useColumn();

  const partAction: ProColumns<API.PartItem> = {
    title: <FormattedMessage id="webOperation_device_otaUpgradeHistory_common_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 100,
    fixed: 'right',
    render: (_, record) => (
      <Access accessible={access.canViewOtaUpgrade()}>
        <a
          key="view"
          onClick={() => {
            upgradeRef.current?.view(record.componentNo);
          }}
        >
          <FormattedMessage id="webCommon_page_view_common_text" />
        </a>
      </Access>
    ),
  };

  const partColumns = createColumns('part', [...listColumns, partAction]);

  return (
    <>
      <BasicInfo detail={detail} key="info" />
      <PageContainer
        title={<FormattedMessage id="webOperation_device_part_table_text" />}
        breadcrumb={undefined}
        style={{ background: '#fff', margin: 0 }}
      >
        <ResizableTable<API.PartItem, API.DevicePartPageParams>
          rowKey={(_, index: number | undefined) => String(index)}
          defaultSize="small"
          search={false}
          params={{ deviceId: id }}
          request={getDeviceComponents}
          columns={partColumns}
        />
      </PageContainer>
      <UpgradeList ref={upgradeRef} key="upgrade" deviceId={id} />
    </>
  );
};
export default DevicePartList;
