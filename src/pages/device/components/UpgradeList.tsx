import FormContainer from '@/components/Form/FormContainer';
import ResizableTable from '@/components/Table';
import React, { useImperativeHandle, useState } from 'react';
import { useIntl } from 'umi';
import { omit } from 'lodash-es';

import { useColumn } from '@/hooks/column';
import { getUpgradeHistory } from '@/services/device';
import { listColumns } from '../columns/UpgradeList';

export interface RefType<T = any> {
  view: (component: string) => void;
}

export interface UpgradeProps {
  deviceId: string;
}

const UpgradeList = React.forwardRef<RefType, UpgradeProps>((props, ref) => {
  const { deviceId } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const intl = useIntl();
  const [componentNo, setComponentNo] = useState<string | undefined>();
  const { createColumns } = useColumn();

  const initColumns = createColumns('upgrade', listColumns);

  useImperativeHandle(ref, () => ({
    view: (component: string) => {
      setComponentNo(component);
      setVisible(true);
    },
  }));

  const onClose = () => {
    setVisible(false);
  };

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: 'webOperation_device_otaUpgradeHistory_common_text' })}
        width="70%"
        onCancel={onClose}
        open={visible}
        hiddenConfirm={true}
        destroyOnClose={true}
      >
        <ResizableTable<API.FirmwareUpgradeItem, API.UpgradePageParams>
          rowKey={(record) => record.id}
          defaultSize="small"
          params={{ componentNo, deviceId }}
          search={{
            labelWidth: 'auto',
          }}
          request={getUpgradeHistory}
          columns={initColumns}
          scroll={{ x: '100%' }}
          beforeSearchSubmit={(params) => {
            return {
              ...omit(params, [
                'upgradeTime', // 升级完成时间
              ]),
              startTime: params.upgradeTime ? params.upgradeTime?.[0] : undefined,
              endTime: params.upgradeTime ? params.upgradeTime?.[1] : undefined,
            };
          }}
        />
      </FormContainer>
    </>
  );
});

export default UpgradeList;
