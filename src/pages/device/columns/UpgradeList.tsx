import type { typeColumns } from '@/hooks/column';
import { fieldProps } from '@/hooks/column';
import { getIntl } from 'umi';

export const listColumns: typeColumns[] = [
  {
    dataIndex: 'upgradeTime',
    valueType: 'dateRange',
    hideInTable: true,
    showLabel: true,
    fieldProps,
  },
  {
    dataIndex: 'oldVersion',
    width: 150,
  },
  {
    dataIndex: 'newVersion',
    width: 150,
  },
  {
    dataIndex: 'status',
    width: 150,
    valueEnum: {
      0: getIntl().formatMessage({
        id: 'webOperation_dictionary_waitingForUpgrade_select_text',
      }),
      1: getIntl().formatMessage({
        id: 'webOperation_dictionary_downloading_select_text',
      }),
      2: getIntl().formatMessage({
        id: 'webOperation_dictionary_upgrading_select_text',
      }),
      3: getIntl().formatMessage({
        id: 'webOperation_dictionary_upgradeSuccessful_select_text',
      }),
      4: getIntl().formatMessage({
        id: 'webOperation_dictionary_upgradeFailure_select_text',
      }),
      6: getIntl().formatMessage({
        id: 'webOperation_dictionary_preparingForRestart_select_text',
      }),
    },
  },
  {
    dataIndex: 'userId',
    width: 170,
  },
  {
    dataIndex: 'upgradeTime',
    width: 150,
    valueType: 'dateTime',
    hideInSearch: true,
  },
];
