import type { typeColumns } from '@/hooks/column';
import { fieldProps } from '@/hooks/column';
import { getIntl } from 'umi';
export const listColumns: typeColumns[] = [
  {
    dataIndex: 'componentNo',
    width: 250,
    hideInSearch: true,
  },
  {
    dataIndex: 'componentName',
    width: 250,
    hideInSearch: true,
  },
  {
    dataIndex: 'componentType',
    width: 250,
    hideInSearch: true,
    valueEnum: {
      completeMachine: getIntl().formatMessage({
        id: 'webOperation_dictionary_completeMachine_select_text',
      }),
      bleModule: getIntl().formatMessage({
        id: 'webOperation_dictionary_bleModule_select_text',
      }),
      '4gModule': getIntl().formatMessage({
        id: 'webOperation_dictionary_4gModule_select_text',
      }),
      其他测试专用: getIntl().formatMessage({
        id: 'webOperation_dictionary_testOnly_select_text',
      }),
      wifiModule: getIntl().formatMessage({
        id: 'webOperation_dictionary_wifiModule_select_text',
      }),
      subDevice: getIntl().formatMessage({
        id: 'webOperation_dictionary_subDevice_select_text',
      }),
      mcu: getIntl().formatMessage({ id: 'webOperation_dictionary_mcu_select_text' }),
    },
  },
  {
    dataIndex: 'componentVersion',
    width: 200,
    hideInSearch: true,
    render: (_, record) => record.version,
  },
];

export const deviceColumns: typeColumns[] = [
  {
    dataIndex: 'upgradeTime',
    valueType: 'dateRange',
    hideInTable: true,
    showLabel: true,
    fieldProps,
  },
  {
    dataIndex: 'jobId',
    width: 170,
    hideInSearch: true,
    hideInTable: true,
  },
  {
    dataIndex: 'oldVersion',
    width: 170,
  },
  {
    dataIndex: 'newVersion',
    width: 170,
  },
  {
    dataIndex: 'status',
    width: 170,
    valueEnum: {
      IN_PROGRESS: getIntl().formatMessage({
        id: 'webOperation_dictionary_inProgress_select_text',
      }),
      FAILED: getIntl().formatMessage({
        id: 'webOperation_dictionary_failed_select_text',
      }),
      QUEUED: getIntl().formatMessage({
        id: 'webOperation_dictionary_queued_select_text',
      }),
      TIMED_OUT: getIntl().formatMessage({
        id: 'webOperation_dictionary_timeout_select_text',
      }),
      SUCCEEDED: getIntl().formatMessage({
        id: 'webOperation_dictionary_successed_select_text',
      }),
      CANCELED: getIntl().formatMessage({
        id: 'webOperation_dictionary_canceled_select_text',
      }),
      REJECTED: getIntl().formatMessage({
        id: 'webOperation_dictionary_rejected_select_text',
      }),
      REMOVED: getIntl().formatMessage({
        id: 'webOperation_dictionary_removed_select_text',
      }),
    },
  },
  {
    dataIndex: 'userId',
    width: 170,
  },
  {
    dataIndex: 'upgradeTime',
    width: 170,
    hideInSearch: true,
    valueType: 'dateTime',
  },
];
