import type { typeColumns } from '@/hooks/column';
import { extra, dateFilter } from '@/hooks/column';
import { Image } from 'antd';
import { getIntl } from 'umi';

interface ProductListProps {
  categoryEnum: typeColumns;
  brandEnum: typeColumns;
}
export const listColumns = ({ categoryEnum, brandEnum }: ProductListProps): typeColumns[] => [
  ...dateFilter,
  {
    dataIndex: 'id',
    width: 160,
  },
  {
    dataIndex: 'categoryId',
    width: 150,
    showInForm: true,
    valueType: 'select',
    required: true,
    valueEnum: categoryEnum,
  },
  {
    dataIndex: 'model',
    width: 150,
    showInForm: true,
    required: true,
  },
  {
    dataIndex: 'commodityModel',
    width: 150,
  },
  {
    dataIndex: 'productType',
    width: 150,
    showInForm: true,
    required: true,
    hideInSearch: true,
    valueType: 'select',
    hideInTable: true,
    valueEnum: {
      oldIotDevice: getIntl().formatMessage({
        id: 'webOperation_dictionary_oldIotDevice_select_text',
      }),
      notIotDevice: getIntl().formatMessage({
        id: 'webOperation_dictionary_notIotDevice_select_text',
      }),
    },
  },
  {
    dataIndex: 'productType',
    width: 150,
    valueEnum: {
      gatewaySubDevice: getIntl().formatMessage({
        id: 'webOperation_dictionary_gatewaySubDevice_select_text',
      }),
      oldIotDevice: getIntl().formatMessage({
        id: 'webOperation_dictionary_oldIotDevice_select_text',
      }),
      directConnectedDevice: getIntl().formatMessage({
        id: 'webOperation_dictionary_directConnectedDevice_select_text',
      }),
      gatewayDevice: getIntl().formatMessage({
        id: 'webOperation_dictionary_gatewayDevice_select_text',
      }),
      notIotDevice: getIntl().formatMessage({
        id: 'webOperation_dictionary_notIotDevice_select_text',
      }),
    },
  },
  {
    dataIndex: 'releaseStatus',
    width: 150,
    valueEnum: {
      PToBeRelease: getIntl().formatMessage({
        id: 'webOperation_dictionary_PToBeRelease_select_text',
      }),
      PReleased: getIntl().formatMessage({
        id: 'webOperation_dictionary_PReleased_select_text',
      }),
      POffReleased: getIntl().formatMessage({
        id: 'webOperation_dictionary_POffReleased_select_text',
      }),
    },
  },
  {
    dataIndex: 'productIcon',
    width: 150,
    hideInSearch: true,
    render: (_, record) => <Image width={80} height={80} src={record.productIcon} />,
  },
  {
    dataIndex: 'brandId',
    width: 150,
    hideInTable: true,
    valueEnum: brandEnum,
  },
  {
    dataIndex: 'brandName',
    width: 150,
    hideInSearch: true,
    langCode: 'webOperation_product_brandId_tableColumn_text',
  },
  {
    dataIndex: 'productName',
    width: 150,
    hideInTable: true,
  },
  {
    dataIndex: 'productNameLanguage',
    langCode: 'webOperation_product_productName_tableColumn_text',
    width: 150,
    hideInSearch: true,
  },
  {
    dataIndex: 'productSnCode',
    width: 150,
  },
  ...extra,
  {
    dataIndex: 'operationRemark',
    width: 150,
    hideInSearch: true,
  },
  {
    dataIndex: 'networkModes',
    width: 150,
    hideInSearch: true,
    hideInTable: true,
    showInForm: true,
    required: true,
    valueType: 'select',
    readonly: true,
    valueEnum: {
      notNetworked: getIntl().formatMessage({
        id: 'webOperation_dictionary_notNetworked_select_text',
      }),
      wifi: getIntl().formatMessage({
        id: 'webOperation_dictionary_wifi_select_text',
      }),
      '4G': getIntl().formatMessage({
        id: 'webOperation_dictionary_4G_select_text',
      }),
      '4GAndBle': getIntl().formatMessage({
        id: 'webOperation_dictionary_4GAndBle_select_text',
      }),
      lan: getIntl().formatMessage({
        id: 'webOperation_dictionary_lan_select_text',
      }),
      ble: getIntl().formatMessage({
        id: 'webOperation_dictionary_ble_select_text',
      }),
      wifiAndBle: getIntl().formatMessage({
        id: 'webOperation_dictionary_wifiAndBle_select_text',
      }),
      DOrT: getIntl().formatMessage({
        id: 'webOperation_dictionary_dOrT_select_text',
      }),
    },
  },
];
