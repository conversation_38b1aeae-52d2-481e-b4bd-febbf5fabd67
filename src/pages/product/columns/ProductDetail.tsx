import type { typeColumns } from '@/hooks/column';
import { getIntl } from 'umi';

export const detailColumns = (brandEnum: typeColumns): typeColumns[] => [
  {
    dataIndex: 'brandId',
    showInForm: true,
    required: true,
    valueType: 'select',
    valueEnum: brandEnum,
  },
  {
    dataIndex: 'productNameLanguage',
    langCode: 'webOperation_product_productName_tableColumn_text',
    showInForm: true,
    required: true,
  },
  {
    dataIndex: 'commodityModel',
    showInForm: true,
    required: true,
  },
  {
    dataIndex: 'productSnCode',
    showInForm: true,
    required: true,
  },
  {
    dataIndex: 'questionTemplate',
    showInForm: true,
    required: true,
    valueType: 'checkbox',
    valueEnum: {
      extendedWarrantyTemplate: getIntl().formatMessage({
        id: 'webOperation_dictionary_extendedWarrantyTemplate_select_text',
      }),
      commonTemplate: getIntl().formatMessage({
        id: 'webOperation_dictionary_commonTemplate_select_text',
      }),
    },
  },
  {
    dataIndex: 'productIcon',
  },
  {
    dataIndex: 'operationRemark',
  },
];
