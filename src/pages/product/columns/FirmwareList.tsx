import type { typeColumns } from '@/hooks/column';
import { fieldProps } from '@/hooks/column';
import { map } from 'lodash-es';
import { Tooltip } from 'antd';

export const resultColumns: typeColumns[] = [
  {
    dataIndex: 'resultTime',
    valueType: 'dateRange',
    hideInTable: true,
    showLabel: true,
    fieldProps,
  },
  {
    dataIndex: 'deviceId',
    width: 170,
    langCode: 'webCommon_page_deviceId_tableColumn_text',
  },
  {
    dataIndex: 'deviceName',
    width: 150,
    langCode: 'webCommon_page_deviceName_tableColumn_text',
  },
  {
    dataIndex: 'groupName',
    width: 150,
    langCode: 'webOperation_group_groupName_tableColumn_text',
  },
  {
    dataIndex: 'componentNo',
    width: 150,
    ellipsis: {
      showTitle: false,
    },
    render: (_, record) => {
      const values = map(
        record.componentResults,
        (item: API.UpgradeInfo, index: number) => item.componentNo,
      );
      return (
        <Tooltip placement="topLeft" title={values.join('，')}>
          {values.join('，')}
        </Tooltip>
      );
    },
  },
  {
    dataIndex: 'componentName',
    width: 150,
    ellipsis: {
      showTitle: false,
    },
    render: (_, record) => {
      const values = map(
        record.componentResults,
        (item: API.UpgradeInfo, index: number) => item.componentName,
      );
      return (
        <Tooltip placement="topLeft" title={values.join('，')}>
          {values.join('，')}
        </Tooltip>
      );
    },
  },
  {
    dataIndex: 'oldVersion',
    width: 150,
    ellipsis: {
      showTitle: false,
    },
    render: (_, record) => {
      const values = map(
        record.componentResults,
        (item: API.UpgradeInfo, index: number) => item.oldVersion,
      );
      return (
        <Tooltip placement="topLeft" title={values.join('，')}>
          {values.join('，')}
        </Tooltip>
      );
    },
  },
  {
    dataIndex: 'newVersion',
    width: 150,
    ellipsis: {
      showTitle: false,
    },
    render: (_, record) => {
      const values = map(
        record.componentResults,
        (item: API.UpgradeInfo, index: number) => item.newVersion,
      );
      return (
        <Tooltip placement="topLeft" title={values.join('，')}>
          {values.join('，')}
        </Tooltip>
      );
    },
  },
];
