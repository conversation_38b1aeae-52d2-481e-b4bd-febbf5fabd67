import React, { useState } from 'react';
import { useIntl, useAccess, FormattedMessage } from 'umi';
import ResizableTable from '@/components/Table';
import FormContainer from '@/components/Form/FormContainer';
import { Access } from '@@/plugin-access/access';
import { Button, ButtonProps, Tooltip } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { omit, pick, map } from 'lodash-es';
import { getGroupList } from '@/services/group';
import { typeColumns, useColumn } from '@/hooks/column';

export interface GroupListProps {
  setData: (list: API.GroupItem[]) => void;
  selectedKeys?: API.GroupItem[];
  buttonProps?: Partial<ButtonProps>;
  groupType?: string;
  moduleName?: string;
}

const GroupList: React.FC<GroupListProps> = (props) => {
  const [selectedValues, setSelectedValues] = useState<API.GroupItem[]>([]);
  const {
    setData,
    selectedKeys,
    buttonProps = {},
    groupType = 'USER_GROUP',
    moduleName = 'Common',
  } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const intl = useIntl();
  const access = useAccess();
  const { createColumns } = useColumn();

  const listColumns: typeColumns[] = [
    {
      dataIndex: 'groupName',
      width: 150,
    },
    {
      dataIndex: 'groupType',
      width: 150,
      hideInSearch: true,
      valueEnum: {
        USER_GROUP: intl.formatMessage({
          id: 'webOperation_dictionary_userGroup_select_text',
        }),
        DEVICE_GROUP: intl.formatMessage({
          id: 'webOperation_dictionary_deviceGroup_select_text',
        }),
      },
    },
    {
      dataIndex: 'conditionContents',
      width: 150,
      isRender: true,
      valueEnum: {
        RN_VERSION: intl.formatMessage({
          id: 'webOperation_dictionary_rnVersion_select_text',
        }),
        USER_ID: intl.formatMessage({
          id: 'webOperation_dictionary_userId_select_text',
        }),
        PHONE_OS_VERSION: intl.formatMessage({
          id: 'webOperation_dictionary_phoneOsVersion_select_text',
        }),
        PHONE_MODEL: intl.formatMessage({
          id: 'webOperation_dictionary_phoneModel_select_text',
        }),
        APP_VERSION: intl.formatMessage({
          id: 'webOperation_dictionary_appVersion_select_text',
        }),
        BUSINESS_TYPE: intl.formatMessage({
          id: 'webOperation_dictionary_businessType_select_text',
        }),
        FIRMWARE_VERSION: intl.formatMessage({
          id: 'webOperation_dictionary_firmwareVersion_select_text',
        }),
        PRODUCT_MODEL: intl.formatMessage({
          id: 'webOperation_dictionary_productModel_select_text',
        }),
        DEVICE_ID: intl.formatMessage({
          id: 'webOperation_dictionary_deviceId_select_text',
        }),
      },
    },
    {
      dataIndex: 'conditionRules',
      width: 150,
      isRender: true,
      hideInSearch: true,
      valueEnum: {
        GRATER_THAN: intl.formatMessage({
          id: 'webOperation_dictionary_graterThan_select_text',
        }),
        EQUALS: intl.formatMessage({ id: 'webOperation_dictionary_equals_select_text' }),
        EXCLUDE: intl.formatMessage({ id: 'webOperation_dictionary_exclude_select_text' }),
        LESS_THAN_EQUAL: intl.formatMessage({
          id: 'webOperation_dictionary_lessThanEqual_select_text',
        }),
        GRATER_THAN_EQUAL: intl.formatMessage({
          id: 'webOperation_dictionary_graterThanEqual_select_text',
        }),
        INCLUDE: intl.formatMessage({ id: 'webOperation_dictionary_include_select_text' }),
        LESS_THAN: intl.formatMessage({ id: 'webOperation_dictionary_lessThan_select_text' }),
      },
    },
    {
      dataIndex: 'conditionRule',
      width: 150,
      hideInTable: true,
      valueEnum: {
        GRATER_THAN: intl.formatMessage({
          id: 'webOperation_dictionary_graterThan_select_text',
        }),
        EQUALS: intl.formatMessage({ id: 'webOperation_dictionary_equals_select_text' }),
        EXCLUDE: intl.formatMessage({ id: 'webOperation_dictionary_exclude_select_text' }),
        LESS_THAN_EQUAL: intl.formatMessage({
          id: 'webOperation_dictionary_lessThanEqual_select_text',
        }),
        GRATER_THAN_EQUAL: intl.formatMessage({
          id: 'webOperation_dictionary_graterThanEqual_select_text',
        }),
        INCLUDE: intl.formatMessage({ id: 'webOperation_dictionary_include_select_text' }),
        LESS_THAN: intl.formatMessage({ id: 'webOperation_dictionary_lessThan_select_text' }),
      },
    },
    {
      dataIndex: 'conditionValues',
      width: 150,
      hideInSearch: true,
      ellipsis: {
        showTitle: false,
      },
      render: (_, record) => (
        <Tooltip placement="topLeft" title={record.conditionValues.join('，')}>
          {record.conditionValues.join('，')}
        </Tooltip>
      ),
    },
  ];

  const initColumns = createColumns('group', listColumns);

  const onSelectChange = (newSelectedRowKeys: React.Key[], selectedRows: API.GroupItem[]) => {
    setSelectedValues(selectedRows.map((item) => pick(item, 'groupName')));
  };

  const rowSelection = {
    preserveSelectedRowKeys: true,
    onChange: onSelectChange,
    getCheckboxProps: (record: API.GroupItem) => ({
      disabled: map(selectedKeys, (item) => item.groupName).includes(record.groupName),
    }),
  };

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: 'webOperation_group_select_drawer_title' })}
        width="50%"
        onCancel={() => {
          setVisible(false);
        }}
        onConfirm={() => {
          setData(selectedValues);
          setVisible(false);
        }}
        open={visible}
        destroyOnClose={true}
      >
        <ResizableTable<API.GroupItem, API.GroupPageParams>
          rowKey={(record) => record.groupName}
          defaultSize="small"
          search={{
            defaultCollapsed: false,
            collapseRender: false,
            labelWidth: 'auto',
          }}
          rowSelection={rowSelection}
          // pagination={false}
          scroll={{ x: 1300 }}
          request={getGroupList}
          columns={initColumns}
          params={{ groupType }}
          beforeSearchSubmit={(params) => {
            return {
              ...omit(params, 'conditionContents'),
              content: params.conditionContents,
            };
          }}
        />
      </FormContainer>
      <Access accessible={access['canAddGroup' + moduleName]?.()}>
        <Button
          key="groupList"
          type="primary"
          onClick={() => {
            setSelectedValues([]);
            setVisible(true);
          }}
          {...buttonProps}
        >
          <PlusOutlined />
          <FormattedMessage id="webOperation_group_create_button_text" />
        </Button>
      </Access>
    </>
  );
};

export default GroupList;
