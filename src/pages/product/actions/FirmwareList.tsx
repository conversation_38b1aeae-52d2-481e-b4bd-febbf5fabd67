import {
  applyFirmwareRelease,
  applyFirmwareStopRelease,
  cancelFirmwareRelease,
  cancelFirmwareStopRelease,
  viewRefuseFirmwareReason,
  viewRefuseFirmwareTestReason,
  viewRefuseFirmwareStopReason,
  applyFirmwareNullifyRelease,
  cancelFirmwareNullifyRelease,
  viewRefuseFirmwareNullifyReason,
} from '@/services/product';
import { message } from 'antd';
import { OptionColumn, showRefuseReason, showConfirmModal } from '@/hooks/action';
import { RefType } from '@/components/Modal/index';
import { RefObject } from 'react';

export const buttonList = (modalRef: RefObject<RefType>): OptionColumn[] => [
  // 任务详情
  {
    name: 'canViewJobDetail',
    module: 'Otaconfig',
    langCode: 'webOperation_task_detail_common_text',
  },
  // 固件发布配置
  {
    name: 'canConfigRelease',
    module: 'Otaconfig',
    langCode: 'webOperation_product_firmwareConfig_tabs_text',
  },
  // 固件发布详情
  {
    name: 'canViewReleaseDetail',
    module: 'Otaconfig',
    langCode: 'webOperation_firmware_releaseDetail_common_text',
  },
  // 申请发布
  {
    name: 'canApplyRelease',
    module: 'Otaconfig',
    onClick: (id: string) => {
      showConfirmModal(modalRef, { api: applyFirmwareRelease, id }, 'firmware_release');
    },
  },
  // 撤回发布申请
  {
    name: 'canCancelApplyRelease',
    module: 'Otaconfig',
    onClick: async (id, msg) => {
      await cancelFirmwareRelease(id);
      message.success(msg);
    },
  },
  // 申请停止发布
  {
    name: 'canApplyStopRelease',
    module: 'Otaconfig',
    onClick: async (id, msg) => {
      await applyFirmwareStopRelease(id);
      message.success(msg);
    },
  },
  // 撤回停止发布申请
  {
    name: 'canCancelStopRelease',
    module: 'Otaconfig',
    langCode: 'webOperation_product_canCancelApplyStopRelease_tableButton_text',
    onClick: async (id, msg) => {
      await cancelFirmwareStopRelease(id);
      message.success(msg);
    },
  },
  // 申请作废
  {
    name: 'canNullifyApply',
    module: 'Otaconfig',
    onClick: async (id, msg) => {
      await applyFirmwareNullifyRelease(id);
      message.success(msg);
    },
  },
  // 撤回作废申请
  {
    name: 'canNullifyApplyCancel',
    module: 'Otaconfig',
    onClick: async (id, msg) => {
      await cancelFirmwareNullifyRelease(id);
      message.success(msg);
    },
  },
  // 发布被驳回原因
  {
    name: 'canViewApplyRefuseReason',
    module: 'Otaconfig',
    langCode: 'webOperation_product_canViewRefuseReleaseReason_tableButton_text',
    onClick: async (id: string) => {
      showRefuseReason(viewRefuseFirmwareReason, modalRef, id);
    },
  },
  // 测试被驳回原因
  {
    name: 'canViewTestRefuseReason',
    module: 'Otaconfig',
    langCode: 'webOperation_product_canViewRefuseTestReason_tableButton_text',
    onClick: async (id: string) => {
      showRefuseReason(viewRefuseFirmwareTestReason, modalRef, id);
    },
  },
  // 停止发布被驳回原因
  {
    name: 'canViewStopRefuseReason',
    module: 'Otaconfig',
    langCode: 'webOperation_product_canViewRefuseStopReleaseReason_tableButton_text',
    onClick: async (id: string) => {
      showRefuseReason(viewRefuseFirmwareStopReason, modalRef, id);
    },
  },
  // 作废被驳回原因
  {
    name: 'canViewNullifyRefusedReason',
    module: 'Otaconfig',
    onClick: async (id: string) => {
      showRefuseReason(viewRefuseFirmwareNullifyReason, modalRef, id);
    },
  },
  // 升级结果
  {
    name: 'canViewOtaResult',
    module: 'Otaconfig',
    langCode: 'webOperation_upgrade_result_common_text',
  },
];
