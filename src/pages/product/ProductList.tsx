// 框架依赖引入
import React, { useCallback, useRef, useState } from 'react';
import { FormattedMessage, useIntl, useAccess, useRequest } from 'umi';
import { Button, message, Space, Spin } from 'antd';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProFormInstance } from '@ant-design/pro-components';
import { Access } from '@@/plugin-access/access';
// 公共自定义依赖引入
import ResizableTable from '@/components/Table';
import Modal, { RefType as modalRefType } from '@/components/Modal/index';
import { useColumn } from '@/hooks/column';
import { showDeleteModal, useAction } from '@/hooks/action';
import { useCategories, useBrands } from '@/hooks/selectHooks';
import KeepAlive from '@/components/KeepAlive';
import useExport from '@/hooks/useExport';
// 页面自定义依赖引入
import {
  getProductList,
  exportProducts,
  getImportTemplate,
  getProductById,
  deleteProduct,
  productWithdraw,
  productRelease,
} from '@/services/product';
import ProductEdit, { RefType } from './ProductEdit';
import ProductImport from '@/components/Import/index';
import ProductDetail, { RefType as DetailRefType } from '../product/ProductDetail';
import { listColumns } from './columns/ProductList';
import ProductInfoEdit from './manage/ProductInfoEdit';

enum ReleaseStatus {
  PToBeRelease = 'PToBeRelease', // 待发布
  PReleased = 'PReleased', // 已发布
  POffReleased = 'POffReleased', // 已下架
}
enum CreateType {
  IOT = 0, // 技术平台创建产品
  OPERATION = 1, // 运营平台创建产品
}
const ProductList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const { createOptimazationActions } = useAction('id');
  const formRef = useRef<ProFormInstance>();
  const editRef = useRef<RefType>(null);
  const modalRef = useRef<modalRefType>(null);
  const detailRef = useRef<DetailRefType>(null);
  const access = useAccess();
  const intl = useIntl();
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const { valueEnum: categoryEnum } = useCategories();
  const { valueEnum: brandEnum } = useBrands();

  const { run: handleExport, loading: exportLoading } = useExport(
    exportProducts,
    formRef,
    intl.formatMessage({ id: 'webOperation_product_export_filename_text' }),
  );

  const { createColumns } = useColumn();

  // 点击详情按钮
  const showDetail = async (id: string) => {
    setActionLoading(true);
    try {
      const res = await getProductById(id);
      setActionLoading(false);
      detailRef.current?.open(res?.data);
    } catch (e) {
      setActionLoading(false);
    }
  };
  const trigger = useCallback(
    <T extends string | Record<string, string>>(
        fn: (key: T) => Promise<T>,
        callback?: (data: T) => void,
      ) =>
      async (res: T) => {
        const data = await fn(res);
        callback?.(data);
        message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
        actionRef.current?.reload();
      },
    [intl],
  );

  // 发布
  const { run: handleRelease } = useRequest(trigger(productRelease), { manual: true });
  // 下架
  const { run: handleWithdraw } = useRequest(trigger(productWithdraw), { manual: true });

  const actionColumn: ProColumns<API.ProductItem> = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 250,
    fixed: 'right',
    render: (_, record) => {
      const columns = [
        {
          name: 'canViewProduct',
          langCode: 'webCommon_page_detail_common_text',
          onClick: showDetail,
        },
        {
          name: 'canUpdateProduct',
          langCode: 'webCommon_page_edit_common_text',
          onClick: () => {
            editRef.current?.edit(record);
          },
        },
        {
          name: 'canReleaseProduct',
          langCode: 'webCommon_page_release_tableButton_linkText',
          hidden: record.releaseStatus === ReleaseStatus.PReleased,
          onClick: handleRelease,
        },
        {
          name: 'canWithdrawProduct',
          langCode: 'webCommon_page_withdraw_tableButton_linkText',
          hidden: record.releaseStatus !== ReleaseStatus.PReleased,
          onClick: handleWithdraw,
        },
        {
          name: 'canDeleteProduct',
          langCode: 'webCommon_page_delete_tableButton_linkText',
          hidden: record.createType === CreateType.IOT,
          onClick: (id: string) => {
            showDeleteModal(modalRef, { api: deleteProduct, id }, 'product_delete');
          },
        },
      ];
      return createOptimazationActions(columns, record, 'product');
    },
  };

  const initColumns = [...listColumns({ categoryEnum, brandEnum }), actionColumn];
  const allColumns = createColumns('product', initColumns);

  return (
    <>
      <Spin spinning={actionLoading}>
        <ResizableTable<API.ProductItem, API.ProductPageParams>
          actionRef={actionRef}
          formRef={formRef}
          rowKey="id"
          defaultSize="small"
          search={{
            labelWidth: 'auto',
          }}
          headerTitle={
            <Space>
              <Access accessible={access.canExportProduct()}>
                <Button
                  key="export"
                  loading={exportLoading}
                  type="primary"
                  ghost
                  onClick={handleExport}
                >
                  <FormattedMessage id="webCommon_page_export_button_text" />
                </Button>
              </Access>
              <Access accessible={access.canImportProduct()}>
                <ProductImport
                  key="import"
                  pageName="product"
                  getTemplate={getImportTemplate}
                  url="/operation-platform/product/import"
                  urlParam="file"
                  refresh={() => {
                    return actionRef.current?.reload();
                  }}
                />
              </Access>
              <ProductEdit
                key="create"
                columns={allColumns}
                refresh={() => {
                  return actionRef.current?.reload();
                }}
              />
            </Space>
          }
          scroll={{ x: 1300 }}
          request={getProductList}
          columns={allColumns}
        />
      </Spin>
      <ProductDetail ref={detailRef} key="detail" refresh={() => actionRef.current?.reload()} />
      <ProductInfoEdit
        ref={editRef}
        key="edit"
        refresh={() => actionRef.current?.reload()}
        brandEnum={brandEnum}
      />
      <Modal ref={modalRef} parentRef={actionRef} />
    </>
  );
};

export default () => {
  return (
    <KeepAlive>
      <ProductList />
    </KeepAlive>
  );
};
