import { useRequest } from '@@/plugin-request/request';
import {
  createIntroduction,
  deleteIntroduction,
  listIntroduction,
  updateIntroduction,
} from '@/services/introduction';
import { Card, Empty, message, Modal, Row, Space, Spin } from 'antd';
import { FormattedMessage, useIntl } from '@@/plugin-locale/localeExports';
import React, {
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
  useContext,
} from 'react';
import { ProFormInstance } from '@ant-design/pro-components';
import { ProForm, ProFormDependency, ProFormText } from '@ant-design/pro-form';
import { LocaleID } from '@/components/Locale';
import { filter, get, isArray, map, omit } from 'lodash-es';
import OptionalItems, { transformForAPI, transformForForm } from '@/components/Form/OptionalItems';
import { AccessButton } from '@/components/Button/AccessButton';
import { len3000 } from '@/utils/validate';
import { rbac } from '@/access';
import { DefinitionContext } from '@/utils/context';
import { getS3File } from '@/services/common';

interface DescriptionFormProps {
  type: string;
  prefix: string;
  refresh: () => void;
}

type RefType = APP.RefType<API.IntroductionItem> & { create: () => void };

const DescriptionEdit = React.forwardRef<RefType, DescriptionFormProps>((props, ref) => {
  const { prefix, refresh, type } = props;
  const formRef = useRef<ProFormInstance<API.IntroductionItem>>(null);
  const [initialValues, setInitialValues] = useState<API.IntroductionItem>({} as any);
  const intl = useIntl();
  const { id } = useContext(DefinitionContext);
  const [visible, setVisible] = useState(false);

  const { run: handleSubmit, loading } = useRequest(
    async (values: any) => {
      if (Number(values.iconType) === 1) {
        const { key } = await getS3File(values.icon);
        values.icon = key;
      }
      if (values.introductionId) {
        await updateIntroduction(
          omit(
            {
              ...values,
              productId: id,
              content: get(values, ['content', 'message']),
            },
            ['type'],
          ),
        );
      } else {
        await createIntroduction({
          content: get(values, ['content', 'message']),
          icon: values.icon,
          iconType: values.iconType,
          productId: id,
          type,
        });
      }
      refresh();
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setVisible(false);
    },
    { manual: true },
  );

  useImperativeHandle(ref, () => ({
    edit: (v) => {
      const current = transformForForm(v!, 'iconType', 'icon');
      String(current.optional.type) === '0'
        ? (current.optional.url = '')
        : (current.optional.img = undefined);
      setInitialValues(current);
      setVisible(true);
    },
    create: () => {
      setInitialValues({});
      setVisible(true);
    },
  }));

  const title = useMemo(() => {
    return (
      intl.formatMessage({ id: `webOperation_introduction_${type}_label_text` }) +
      intl.formatMessage({ id: 'webOperation_introduction_step_label_text' })
    );
  }, [type, intl]);

  return (
    <Modal
      title={initialValues.cardTitle || title}
      open={visible}
      onOk={() => formRef.current?.submit()}
      onCancel={() => setVisible(false)}
      confirmLoading={loading}
      destroyOnClose
    >
      <ProForm
        onFinish={async (values) => {
          await handleSubmit(transformForAPI(values, 'iconType', 'icon'));
        }}
        labelCol={{ flex: '150px' }}
        formRef={formRef}
        layout="horizontal"
        submitter={false}
        initialValues={initialValues}
      >
        <ProFormText name="introductionId" hidden />

        <OptionalItems
          tip="webCommon_page_imageSize_tip_message"
          label={prefix + intl.formatMessage({ id: 'webOperation_introduction_img_select_text' })}
        />

        <ProFormText
          name={['content', 'message']}
          label={prefix + intl.formatMessage({ id: 'webOperation_introduction_text_input_text' })}
          rules={[
            {
              required: true,
              message: <FormattedMessage id="webOperation_introduction_text_input_placeholder" />,
            },
            len3000,
          ]}
          extra={
            <ProFormDependency name={['content', 'langId']}>
              {({ content = {} }) => <LocaleID id={content?.langId} />}
            </ProFormDependency>
          }
        />

        <ProFormText
          label={intl.formatMessage({ id: 'webOperation_introduction_langId_input_text' })}
          name={['content', 'langId']}
          hidden
        />
      </ProForm>
    </Modal>
  );
});

interface DescriptionProps {
  type: string;
  value: API.IntroductionItem[];
  refresh: () => void;
  limit?: number;
  loading?: boolean;
  readonly?: boolean;
}

const Description: React.FC<DescriptionProps> = (props) => {
  const { type, value, limit, loading, refresh, readonly } = props;
  const editRef = useRef<RefType>(null);
  const { id } = useContext(DefinitionContext);
  const intl = useIntl();

  const prefix = useMemo(() => {
    return (
      intl.formatMessage({ id: `webOperation_introduction_${type}_label_text` }) +
      intl.formatMessage({ id: 'webOperation_introduction_guide_label_text' })
    );
  }, [type, intl]);

  const isEmpty = useMemo(() => {
    return isArray(value) ? value.length <= 0 : true;
  }, [value]);

  const title = useMemo(() => {
    return (
      intl.formatMessage({ id: `webOperation_introduction_${type}_label_text` }) +
      (type === '0'
        ? intl.formatMessage({ id: 'webOperation_introduction_guide_label_text' })
        : intl.formatMessage({ id: 'webOperation_introduction_step_label_text' }))
    );
  }, [intl, type]);

  return (
    <>
      {map(value, (item, key) => {
        const cardTitle = title + (limit === undefined ? key + 1 : '');
        return (
          <Card
            key={key}
            title={cardTitle}
            style={{ margin: '20px 0' }}
            size="small"
            extra={
              !readonly && (
                <Space>
                  <AccessButton
                    text="webCommon_page_edit_common_text"
                    onClick={() => {
                      editRef.current?.edit({ ...item, cardTitle });
                    }}
                    code={rbac.INTRODUCTION[`UPDATE${type}`]}
                  />

                  <AccessButton
                    text="webCommon_page_delete_tableButton_linkText"
                    code={rbac.INTRODUCTION[`DELETE${type}`]}
                    onClick={async () => {
                      await deleteIntroduction({
                        productId: id,
                        introductionId: item.introductionId!,
                      });
                      refresh();
                      message.success(
                        intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }),
                      );
                    }}
                    modal={{
                      content: (
                        <>
                          <div style={{ marginBottom: '10px' }}>
                            <div>
                              <FormattedMessage id="webOperation_introduction_deleteLine1_modal_message" />
                            </div>
                            <div>
                              <FormattedMessage id="webOperation_introduction_deleteLine2_modal_message" />
                            </div>
                          </div>

                          <ProFormText
                            name="confirm"
                            label={false}
                            rules={[
                              {
                                required: true,
                                pattern: /^Yes$/,
                                message: (
                                  <FormattedMessage id="webOperation_introduction_delete_input_placeholder" />
                                ),
                              },
                            ]}
                            placeholder={intl.formatMessage({
                              id: 'webOperation_introduction_delete_input_placeholder',
                            })}
                          />
                        </>
                      ),
                    }}
                  />
                </Space>
              )
            }
          >
            <DescriptionContent value={item} prefix={prefix} />
          </Card>
        );
      })}
      {isEmpty && !loading ? (
        <Card title={title} style={{ margin: '20px 0' }} size="small">
          <Empty
            description={
              <FormattedMessage id={`webOperation_introduction_none${type}_label_text`} />
            }
          />
        </Card>
      ) : null}
      {(limit === undefined || (isArray(value) && value.length < limit)) && !loading ? (
        <Row justify="end">
          {!readonly && (
            <AccessButton
              buttonProps={{ ghost: true, type: 'primary' }}
              text={`webOperation_introduction_step${type}_button_text`}
              code={rbac.INTRODUCTION[`CREATE${type}`]}
              onClick={() => {
                editRef.current?.create();
              }}
            />
          )}
        </Row>
      ) : null}
      <DescriptionEdit ref={editRef} type={type} prefix={prefix} refresh={refresh} />
    </>
  );
};

const IntroductionDetail = ({ detail }: { detail: API.ProductItem }) => {
  const { data, loading, refresh } = useRequest(() => listIntroduction(detail.id!));

  const list = useMemo(() => {
    return [
      {
        data: filter(data, (item) => String(item.type) === '0'),
        type: '0',
        limit: 1,
      },
      {
        data: filter(data, (item) => String(item.type) === '1'),
        type: '1',
      },
      {
        data: filter(data, (item) => String(item.type) === '2'),
        type: '2',
      },
    ];
  }, [data]);

  return (
    <div style={{ paddingBottom: 50 }}>
      <Spin spinning={loading}>
        {list.map((item) => (
          <Description
            key={item.type}
            type={item.type}
            value={item.data}
            refresh={refresh}
            limit={item.limit}
            loading={loading}
            readonly={detail?.readonly}
          />
        ))}
      </Spin>
    </div>
  );
};

export default IntroductionDetail;

const DescriptionContent: React.FC<{ prefix: string; value?: API.IntroductionItem }> = ({
  prefix,
  value = {},
}) => {
  const formRef = useRef<ProFormInstance<API.IntroductionItem>>(null);
  const intl = useIntl();

  useEffect(() => {
    formRef.current?.setFieldsValue(value);
  }, [value]);

  return (
    <ProForm labelCol={{ flex: '150px' }} formRef={formRef} layout="horizontal" submitter={false}>
      <ProFormText
        label={prefix + intl.formatMessage({ id: 'webOperation_introduction_img_select_text' })}
      >
        {value?.icon ? (
          <img
            alt=""
            src={value?.icon}
            style={{ width: '200px', height: 'auto', maxHeight: '200px', objectFit: 'contain' }}
          />
        ) : (
          <FormattedMessage id="webCommon_page_noImageData_message_text" />
        )}
      </ProFormText>

      <ProFormText
        name={['content', 'message']}
        label={prefix + intl.formatMessage({ id: 'webOperation_introduction_text_input_text' })}
        readonly={true}
      />
      <ProFormText
        label={intl.formatMessage({ id: 'webOperation_introduction_langId_input_text' })}
        name={['content', 'langId']}
        readonly={true}
      />
    </ProForm>
  );
};
