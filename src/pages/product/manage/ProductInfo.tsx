import { Access } from '@@/plugin-access/access';
import { Card, Col, Row, Button, Image, Space, Tooltip } from 'antd';
import { map } from 'lodash-es';
import { useRef, useState, useEffect } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { useBrands } from '@/hooks/selectHooks';
import ProductInfoEdit, { RefType } from './ProductInfoEdit';
import { detailColumns } from '../columns/ProductDetail';
import { getProductById } from '@/services/product';
import styles from '@/pages/product/styles.less';
import { typeColumns } from '@/hooks/column';

interface Props {
  detail: API.ProductItem;
  refresh?: (data: API.ProductItem) => void;
}

const ProductInfo = ({ detail, refresh }: Props) => {
  const access = useAccess();
  const editRef = useRef<RefType>(null);
  const { list: brandList } = useBrands();
  const [product, setProduct] = useState<API.ProductItem>(detail);
  const intl = useIntl();
  const { valueEnum: brandEnum } = useBrands();

  const questionTemplateEnums = {
    extendedWarrantyTemplate: intl.formatMessage({
      id: 'webOperation_dictionary_extendedWarrantyTemplate_select_text',
    }),
    commonTemplate: intl.formatMessage({
      id: 'webOperation_dictionary_commonTemplate_select_text',
    }),
  };
  useEffect(() => {
    setProduct(detail);
  }, [detail]);

  const updateDetail = async () => {
    getProductById(detail.id!).then((res) => {
      setProduct(res.data);
      refresh?.(res.data);
    });
  };

  return (
    <>
      <div className={styles.iot_ant_detail_info}>
        <div className={styles.iot_ant_detail_edit}>
          <Access accessible={access.canUpdateProduct()}>
            <Button
              type="primary"
              onClick={() => {
                editRef.current?.edit(product);
              }}
            >
              <FormattedMessage id="webCommon_page_edit_common_text" />
            </Button>
          </Access>
        </div>
        <Card>
          <Row gutter={16}>
            {map(detailColumns(brandEnum), (item: typeColumns, index) => {
              let value: any = '';
              switch (item.dataIndex) {
                case 'productNameLanguage':
                  value = (
                    <Space>
                      <Tooltip placement="topLeft" title={product[item.dataIndex as string]}>
                        <span className={styles.iot_ant_lang_value}>
                          {product[item.dataIndex as string]}
                        </span>
                      </Tooltip>
                      <span className={styles.iot_ant_detail_langid}>
                        <FormattedMessage id="webCommon_page_langId_common_text" />:
                        <Tooltip placement="topLeft" title={product['productName']}>
                          {product['productName']}
                        </Tooltip>
                      </span>
                    </Space>
                  );
                  break;
                case 'productFullNameLanguage':
                  value = (
                    <Space>
                      <Tooltip placement="topLeft" title={product[item.dataIndex as string]}>
                        <span className={styles.iot_ant_lang_value}>
                          {product[item.dataIndex as string]}
                        </span>
                      </Tooltip>
                      <span className={styles.iot_ant_detail_langid}>
                        <FormattedMessage id="webCommon_page_langId_common_text" />:
                        <Tooltip placement="topLeft" title={product['productFullName']}>
                          {product['productFullName']}
                        </Tooltip>
                      </span>
                    </Space>
                  );
                  break;
                case 'questionTemplate':
                  value = map(product.questionTemplate, (key) => questionTemplateEnums[key]).join(
                    ', ',
                  );
                  break;
                case 'brandId':
                  value = map(brandList, (brand) => {
                    if (brand.value === String(product.brandId)) {
                      return brand.label;
                    } else {
                      return '';
                    }
                  }).join('');
                  break;
                case 'productIcon':
                  value = (
                    <Image
                      preview={false}
                      width={80}
                      height={80}
                      src={product[item.dataIndex as string]}
                    />
                  );
                  break;
                default:
                  value = (
                    <Tooltip placement="topLeft" title={product[item.dataIndex as string]}>
                      {product[item.dataIndex as string]}
                    </Tooltip>
                  );
                  // value = product[item.dataIndex as string];
                  break;
              }
              return (
                <Col span={8} key={index} className={styles.iot_ant_detail_col}>
                  <span>
                    <FormattedMessage
                      id={
                        item.langCode || `webOperation_product_${item.dataIndex}_tableColumn_text`
                      }
                    />
                    ：
                  </span>
                  <span className={styles.iot_ant_detail_value}>{value}</span>
                </Col>
              );
            })}
          </Row>
        </Card>
      </div>
      <ProductInfoEdit ref={editRef} key="edit" refresh={updateDetail} brandEnum={brandEnum} />
    </>
  );
};
export default ProductInfo;
