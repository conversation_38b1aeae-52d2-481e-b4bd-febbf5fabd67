import { FormattedMessage } from '@@/plugin-locale/localeExports';
import React, { useState, useImperativeHandle } from 'react';
import { useIntl } from 'umi';
import { Card, Col, Row, Tooltip } from 'antd';
import { map, omit, get, pick } from 'lodash-es';
import dayjs from 'dayjs';

import FormContainer from '@/components/Form/FormContainer';
import ResizableTable from '@/components/Table';
import { typeColumns, useColumn } from '@/hooks/column';

import styles from '@/pages/product/styles.less';
import { getFirmwareUpgradeResult } from '@/services/product';

import { resultColumns } from '../columns/FirmwareList';

const ResultInfo = ({ summary }: { summary: API.UpgradeResultSummary }) => {
  const Data = [
    {
      label: <FormattedMessage id="webOperation_firmware_total_tableColumn_text" />,
      value: summary.total,
    },
    {
      label: <FormattedMessage id="webOperation_firmware_succeed_tableColumn_text" />,
      value: summary.succeed,
    },
    {
      label: <FormattedMessage id="webOperation_firmware_failed_tableColumn_text" />,
      value: summary.failed,
    },
  ];

  return (
    <div className={styles.iot_ant_detail_wrapper}>
      <Card>
        <Row gutter={16}>
          {map(Data, (item, index) => {
            return (
              <Col span={8} key={index} className={styles.iot_ant_detail_col}>
                <span>{item.label}</span>：
                <span className={styles.iot_ant_detail_value}>{item.value}</span>
              </Col>
            );
          })}
        </Row>
      </Card>
    </div>
  );
};

export interface DetailProps {
  dict: { [key: string]: any };
}

export interface RefType {
  show: (id: string) => void;
}

const FirmwareUpgradeResult = React.forwardRef<RefType>((props, ref) => {
  const [summary, setSummary] = useState<API.UpgradeResultSummary>({} as any);
  const { createColumns } = useColumn();
  const [visible, setVisible] = useState<boolean>(false);
  const [jobId, setJobId] = useState<string>();
  const intl = useIntl();
  const getUpgradeResult = (params: API.UpgradeResultPageParams) => {
    return getFirmwareUpgradeResult(params).then((res) => {
      setSummary(omit(res.data, 'pageResult'));
      const result = get(res.data, ['pageResult']);
      return {
        data: get(result, ['list']),
        pageNo: get(result, ['pageNum']),
        ...pick(result, ['pageSize', 'total']),
      };
    });
  };

  const otaComponentStatusEnum = {
    SUCCEED: intl.formatMessage({ id: 'webOperation_dictionary_upgradeSucceed_select_text' }),
    FAILED: intl.formatMessage({ id: 'webOperation_dictionary_upgradeFailed_select_text' }),
    WAITING: intl.formatMessage({ id: 'webOperation_dictionary_waiting_select_text' }),
    DOWNLOADING: intl.formatMessage({
      id: 'webOperation_dictionary_downloading_select_text',
    }),
    UPGRADING: intl.formatMessage({ id: 'webOperation_dictionary_upgeading_select_text' }),
    unknown: intl.formatMessage({ id: 'webOperation_dictionary_unknown_select_text' }),
  };

  const extraColumn: typeColumns[] = [
    {
      dataIndex: 'status',
      width: 150,
      ellipsis: {
        showTitle: false,
      },
      valueEnum: otaComponentStatusEnum,
      render: (_, record) => {
        const values = map(record.componentResults, (item: API.UpgradeInfo, index: number) => {
          return otaComponentStatusEnum[item.status!];
        });
        return (
          <Tooltip placement="topLeft" title={values.join('，')}>
            {values.join('，')}
          </Tooltip>
        );
      },
    },
    {
      dataIndex: 'resultTime',
      width: 150,
      valueType: 'dateTime',
      hideInSearch: true,
      ellipsis: {
        showTitle: false,
      },
      render: (_, record) => {
        const values = map(record.componentResults, (item: API.UpgradeInfo, index: number) =>
          dayjs(item.resultTime).format('YYYY-MM-DD HH:mm:ss'),
        );
        return (
          <Tooltip placement="topLeft" title={values.join('，')}>
            {values.join('，')}
          </Tooltip>
        );
      },
    },
  ];

  const onClose = () => {
    setVisible(false);
  };
  useImperativeHandle(ref, () => ({
    show: (id: string) => {
      setSummary({});
      setJobId(id);
      setVisible(true);
    },
  }));

  const allColumns = createColumns('firmware', [...resultColumns, ...extraColumn]);

  return (
    <FormContainer
      title={intl.formatMessage({ id: 'webOperation_upgrade_result_common_text' })}
      width="85%"
      onCancel={onClose}
      hiddenConfirm={true}
      open={visible}
      destroyOnClose={true}
    >
      <ResultInfo summary={summary} />

      <ResizableTable<API.UpgradeResultItem, API.UpgradeResultPageParams>
        rowKey={(_, index: number | undefined) => String(index)}
        search={{
          labelWidth: 'auto',
        }}
        defaultSize="small"
        scroll={{ x: 1300 }}
        params={{ jobId: jobId }}
        request={getUpgradeResult}
        columns={allColumns}
        beforeSearchSubmit={(params) => {
          return {
            ...omit(params, [
              'resultTime', // 升级完成时间
            ]),
            resultStartTime: params.resultTime ? params.resultTime?.[0] : undefined,
            resultEndTime: params.resultTime ? params.resultTime?.[1] : undefined,
          };
        }}
      />
    </FormContainer>
  );
});

export default FirmwareUpgradeResult;
