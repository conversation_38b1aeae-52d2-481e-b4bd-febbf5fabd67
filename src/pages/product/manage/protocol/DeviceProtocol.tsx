import ResizableTable from '@/components/Table';
import { extra } from '@/hooks/column';
import { useIntl } from '@@/plugin-locale/localeExports';
import { ActionType, ProColumns, ProFormText } from '@ant-design/pro-components';
import { message } from 'antd';
import { useRef } from 'react';
import { FormattedMessage } from 'umi';
import { assign, get, omit } from 'lodash-es';
import Space from '@/components/Space';
import { AccessLink } from '@/components/Button/AccessButton';
import { deleteProtocol, listProtocol } from '@/services/device';
import DeviceProtocolEdit from '@/pages/product/manage/protocol/DeviceProtocolEdit';
import { rbac } from '@/access';

const DeviceProtocol = ({ detail }: { detail: API.ProductItem }) => {
  const actionRef = useRef<ActionType>();
  const editRef = useRef<APP.RefType<API.DeviceProtocolItem>>(null);
  const intl = useIntl();

  const columns: ProColumns<API.DeviceProtocolItem>[] = [
    {
      title: <FormattedMessage id="webOperation_protocol_id_tableColumn_text" />,
      dataIndex: 'agreementId',
      fixed: 'left',
      hideInSearch: true,
      width: 200,
    },
    {
      title: <FormattedMessage id="webOperation_protocol_name_tableColumn_text" />,
      dataIndex: ['name', 'message'],
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_protocol_langId_tableColumn_text" />,
      dataIndex: ['name', 'langId'],
      hideInSearch: true,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_protocol_version_tableColumn_text" />,
      dataIndex: 'version',
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_protocol_type_tableColumn_text" />,
      dataIndex: 'type',
      width: 150,
      valueEnum: {
        USER_PRIVACY_AGREEMENT: intl.formatMessage({
          id: 'webOperation_dictionary_userPrivacyAgreement_select_text',
        }),
        USAGE_AGREEMENT: intl.formatMessage({
          id: 'webOperation_dictionary_usageAgreement_select_text',
        }),
      },
    },
    {
      title: <FormattedMessage id="webOperation_protocol_contentId_tableColumn_text" />,
      dataIndex: ['content', 'langId'],
      hideInSearch: true,
      width: 150,
    },
    ...extra,
    {
      title: <FormattedMessage id="webOperation_protocol_option_tableColumn_text" />,
      dataIndex: 'option',
      valueType: 'option',
      width: detail.readonly ? 50 : 250,
      fixed: 'right',
      render: (_: any, record: API.DeviceProtocolItem) =>
        detail.readonly ? (
          <AccessLink
            text="webCommon_page_view_common_text"
            code={rbac.PROTOCOL.VIEW}
            onClick={() => {
              editRef.current?.edit(record, true);
            }}
          />
        ) : (
          <Space>
            <AccessLink
              text="webCommon_page_view_common_text"
              code={rbac.PROTOCOL.VIEW}
              onClick={() => {
                editRef.current?.edit(record, true);
              }}
            />
            <AccessLink
              text="webCommon_page_edit_common_text"
              code={rbac.PROTOCOL.UPDATE}
              onClick={() => {
                editRef.current?.edit(record);
              }}
            />
            <AccessLink
              text="webCommon_page_delete_tableButton_linkText"
              code={rbac.PROTOCOL.DELETE}
              modal={{
                content: (
                  <>
                    <div style={{ marginBottom: '10px' }}>
                      <div>
                        <FormattedMessage id="webOperation_protocol_deleteLine1_modal_message" />
                      </div>
                      <div>
                        <FormattedMessage id="webOperation_protoco2_deleteLine1_modal_message" />
                      </div>
                    </div>
                    <ProFormText
                      name="confirm"
                      label={false}
                      rules={[
                        {
                          required: true,
                          pattern: /^Yes$/,
                          message: (
                            <FormattedMessage id="webOperation_protocol_delete_input_placeholder" />
                          ),
                        },
                      ]}
                      placeholder={intl.formatMessage({
                        id: 'webOperation_protocol_delete_input_placeholder',
                      })}
                    />
                  </>
                ),
              }}
              onClick={async () => {
                await deleteProtocol({ id: record.id!, productId: detail.id! });
                message.success(
                  intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }),
                );
                actionRef.current?.reload();
              }}
            />
            <AccessLink
              text="webOperation_protocol_recreate_common_text"
              code={rbac.PROTOCOL.RECREATE}
              onClick={() => {
                editRef.current?.edit(record, false, true);
              }}
            />
          </Space>
        ),
    },
  ];

  return (
    <ResizableTable<API.DeviceProtocolItem, API.DeviceProtocolPageParams>
      actionRef={actionRef}
      rowKey="id"
      defaultSize="small"
      search={{
        labelWidth: 'auto',
      }}
      scroll={{ x: 150 * 11 + 150 }}
      headerTitle={
        <DeviceProtocolEdit
          ref={editRef}
          key="create"
          isViewer={detail.readonly}
          refresh={() => actionRef.current?.reload()}
        />
      }
      request={(v) => listProtocol(assign(v, { productId: detail.id! }))}
      columns={columns}
      beforeSearchSubmit={(queries) => {
        return {
          ...omit(queries, 'name'),
          name: get(queries, ['name', 'message']),
        };
      }}
    />
  );
};

export default DeviceProtocol;
