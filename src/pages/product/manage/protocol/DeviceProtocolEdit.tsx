import FormContainer from '@/components/Form/FormContainer';
import { createProtocol, recreateProtocol, updateProtocol, viewProtocol } from '@/services/device';
import { len36 } from '@/utils/validate';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { PlusOutlined } from '@ant-design/icons';
import { ProForm, ProFormInstance, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { Button, Col, message, Row, Spin } from 'antd';
import React, { useContext, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { ProFormDependency } from '@ant-design/pro-form';
import { LocaleID } from '@/components/Locale';
import { assign, get, join, map, omit } from 'lodash-es';
import { EditorItem, Validator } from '@/components/Form/EditorItem';
import GroupList from '../../components/GroupList';
import { DefinitionContext } from '@/utils/context';

interface TableItemProps {
  selectable: boolean;
  value?: any;
  readonly?: boolean;
  onChange?: (value: any) => void;
}

const TableItem: React.FC<TableItemProps> = ({ value = [], onChange, readonly = false }) => {
  const groups = useMemo(() => {
    return map(value, (item) => ({ groupName: item }));
  }, [value]);
  const access = useAccess();

  return (
    <div>
      {readonly
        ? join(value, ',')
        : map(value, (item, index: number) => {
            return (
              <Row gutter={10} style={{ marginBottom: 10 }} wrap={false} align="middle" key={index}>
                <Col flex="auto">
                  <div style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>{item}</div>
                </Col>
                <Col flex="100px">
                  <Access accessible={access.canDeleteGroupDeviceProtocol()}>
                    <Button
                      style={{ float: 'right' }}
                      onClick={() => {
                        const arr = [...value];
                        arr.splice(index, 1);
                        onChange?.(arr);
                      }}
                    >
                      <FormattedMessage id="webCommon_page_delete_tableButton_linkText" />
                    </Button>
                  </Access>
                </Col>
              </Row>
            );
          })}

      {!readonly ? (
        <GroupList
          selectedKeys={groups}
          moduleName="DeviceProtocol"
          setData={(k) => {
            onChange?.([...(value || []), ...map(k, (item) => item.groupName)]);
          }}
          buttonProps={{
            style: { float: 'right' },
          }}
        />
      ) : null}
    </div>
  );
};

interface EditProps {
  isViewer?: boolean;
  refresh?: () => Promise<void> | undefined;
}

const DeviceProtocolEdit = React.forwardRef<APP.RefType<API.DeviceProtocolItem>, EditProps>(
  (props, ref) => {
    const { refresh, isViewer = false } = props;
    const intl = useIntl();
    const formRef = useRef<ProFormInstance<API.DeviceProtocolItem>>();
    const [visible, setVisible] = useState<boolean>(false);
    const [readonly, setReadonly] = useState<boolean>(false);
    const [isRecreate, setIsRecreate] = useState<boolean>(false);
    const [initialValues, setInitialValues] = useState<API.DeviceProtocolItem>({} as any);
    const { id: productId } = useContext(DefinitionContext);

    const editorRef = useRef<{ validate: Validator }>(null);
    const access = useAccess();
    const { run: getProtocol, loading: initLoading } = useRequest(
      (params) => viewProtocol(params),
      { manual: true },
    );

    const { run: handleSubmit, loading } = useRequest(
      async (values) => {
        if (isRecreate) {
          await recreateProtocol({
            content: get(values, ['content', 'message']),
            groupNameList: values.groupNameList,
            id: values.id,
            version: values.version,
            productId,
          });
        } else if (values.id) {
          await updateProtocol({
            ...values,
            productId,
          });
        } else {
          await createProtocol({
            ...omit(values, ['name', 'content']),
            name: get(values, ['name', 'message']),
            content: get(values, ['content', 'message']),
            productId,
          });
        }
        message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
        setVisible(false);
        refresh?.();
      },
      { manual: true },
    );

    const onClose = () => {
      setVisible(false);
    };

    useImperativeHandle(ref, () => ({
      edit: (item: API.DeviceProtocolItem, r, recreate) => {
        if (!access.canUpdateDeviceProtocol()) {
          console.warn(intl.formatMessage({ id: 'webCommon_page_noAccess_toast_text' }));
          return;
        }
        setReadonly(!!r);
        setInitialValues(item);
        setVisible(true);
        setIsRecreate(!!recreate);
        getProtocol({ id: item.id!, productId }).then((res) => {
          const current = assign({}, item, res, { groupNameList: get(res, ['groupName']) });
          setInitialValues(current);
          formRef.current?.setFieldsValue(current);
        });
      },
    }));

    const onCreate = () => {
      setInitialValues({} as any);
      setVisible(true);
      setReadonly(false);
      setIsRecreate(false);
    };

    const title = useMemo(() => {
      if (isRecreate) return 'webOperation_protocol_recreate_common_text';
      else if (readonly) return 'webCommon_page_view_common_text';
      else if (initialValues.id) return 'webOperation_protocol_update_drawer_title';
      return 'webOperation_protocol_create_drawer_title';
    }, [readonly, initialValues, isRecreate]);

    return (
      <>
        <FormContainer
          title={<FormattedMessage id={title} />}
          width="50%"
          onCancel={onClose}
          onConfirm={() => formRef.current?.submit()}
          open={visible}
          destroyOnClose={true}
          loading={loading}
          hiddenConfirm={readonly}
        >
          <Spin spinning={initLoading}>
            <ProForm
              initialValues={initialValues}
              onFinish={handleSubmit}
              formRef={formRef}
              labelCol={{ flex: '150px' }}
              layout="horizontal"
              onReset={onClose}
              submitter={false}
              readonly={readonly}
            >
              {initialValues.id ? (
                <ProFormText
                  name="id"
                  readonly
                  label={intl.formatMessage({ id: 'webOperation_protocol_id_input_text' })}
                />
              ) : null}

              <ProFormSelect
                name="type"
                label={intl.formatMessage({
                  id: 'webOperation_protocol_type_select_text',
                })}
                rules={[
                  {
                    required: true,
                    message: (
                      <FormattedMessage id="webOperation_protocol_type_select_placeholder" />
                    ),
                  },
                ]}
                placeholder={intl.formatMessage({
                  id: 'webOperation_protocol_type_select_placeholder',
                })}
                readonly={(initialValues.canUpdate && initialValues.released) || isRecreate}
                valueEnum={{
                  USER_PRIVACY_AGREEMENT: intl.formatMessage({
                    id: 'webOperation_dictionary_userPrivacyAgreement_select_text',
                  }),
                  USAGE_AGREEMENT: intl.formatMessage({
                    id: 'webOperation_dictionary_usageAgreement_select_text',
                  }),
                }}
              />

              <ProFormText name={['name', 'langId']} hidden />

              {/*
               * readonly 控制
               * 1. 编辑模式(有id)且canUpdateAll为false
               * 2. isRecreate
               */}
              <ProFormText
                name={['name', 'message']}
                label={intl.formatMessage({
                  id: 'webOperation_protocol_name_input_text',
                })}
                extra={
                  !isRecreate ? (
                    <ProFormDependency name={['name', 'langId']}>
                      {({ name }) => <LocaleID id={name?.langId} />}
                    </ProFormDependency>
                  ) : null
                }
                rules={[
                  {
                    required: true,
                    message: <FormattedMessage id="webOperation_protocol_name_input_placeholder" />,
                  },
                  len36,
                ]}
                readonly={(initialValues.canUpdate && initialValues.released) || isRecreate}
                placeholder={intl.formatMessage({
                  id: 'webOperation_protocol_name_input_placeholder',
                })}
              />

              <ProFormText
                name="version"
                label={intl.formatMessage({
                  id: 'webOperation_protocol_version_input_text',
                })}
                rules={[
                  {
                    required: true,
                    message: (
                      <FormattedMessage id="webOperation_protocol_version_input_placeholder" />
                    ),
                  },
                  {
                    pattern: /^((0|[1-9][0-9]{0,2})\.){2}(0|[1-9][0-9]{0,2})$/,
                    message: (
                      <FormattedMessage id="webOperation_protocol_version_validator_message" />
                    ),
                  },
                  len36,
                ]}
                placeholder={intl.formatMessage({
                  id: 'webOperation_protocol_version_input_placeholder',
                })}
              />

              <ProForm.Item
                label={intl.formatMessage({ id: 'webOperation_protocol_condition_input_text' })}
                name="groupNameList"
              >
                <TableItem selectable readonly={readonly} />
              </ProForm.Item>

              <ProFormText name={['content', 'langId']} hidden />

              <ProForm.Item
                name={['content', 'message']}
                label={intl.formatMessage({
                  id: 'webOperation_protocol_content_input_text',
                })}
                required
                rules={[
                  {
                    validator: (rule, value, callback) =>
                      editorRef.current?.validate(rule, value, callback),
                    message: (
                      <FormattedMessage id="webOperation_protocol_content_input_placeholder" />
                    ),
                  },
                ]}
                validateTrigger={['onBlur']}
                extra={
                  !isRecreate ? (
                    <ProFormDependency name={['content', 'langId']}>
                      {({ content }) => <LocaleID style={{ bottom: -64 }} id={content?.langId} />}
                    </ProFormDependency>
                  ) : null
                }
              >
                <EditorItem readonly={readonly} ref={editorRef} />
              </ProForm.Item>
            </ProForm>
          </Spin>
        </FormContainer>
        {!isViewer && (
          <Access accessible={access.canCreateDeviceProtocol()}>
            <Button type="primary" onClick={onCreate}>
              <PlusOutlined />
              <FormattedMessage id="webOperation_protocol_create_button_text" />
            </Button>
          </Access>
        )}
      </>
    );
  },
);

export default DeviceProtocolEdit;
