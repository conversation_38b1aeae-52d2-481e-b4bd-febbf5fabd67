import React, { useImperativeHandle, useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { Space, message } from 'antd';
import {
  ProForm,
  ProFormInstance,
  ProFormFieldSet,
  ProFormTextArea,
  ProFormText,
  ProFormDependency,
  ProFormRadio,
} from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { omit, split, map, isArray } from 'lodash-es';
import dayjs from 'dayjs';

import ResizableTable from '@/components/Table';
import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';
import { len500 } from '@/utils/validate';

import GroupList from '../components/GroupList';
import { setFirmwareConfig } from '@/services/product';
import { typeColumns } from '@/hooks/column';
import { zone } from '@/utils/constant';

export interface RefType {
  edit: (id: string, record: API.FirmwareReleaseDetail) => void;
}

const FirmwareReleaseConfig = React.forwardRef<RefType, APP.EditFormProps>((props, ref) => {
  const { refresh } = props;
  const intl = useIntl();
  const formRef = useRef<ProFormInstance<API.FirmwareReleaseDetail>>();
  const [visible, setVisible] = useState<boolean>(false);
  const [jobId, setJobId] = useState<string>('');
  const [prdGroupList, setPrdGroupList] = useState<API.GroupItem[]>([]);
  const [testGroupList, setTestGroupList] = useState<API.GroupItem[]>([]);
  const [initialValues, setInitialValues] = useState<API.FirmwareReleaseDetail>({} as any);
  const access = useAccess();

  const { run: handleSubmit, loading } = useRequest(
    async (values) => {
      values.jobId = jobId;
      if (initialValues.langId) {
        values.langId = initialValues.langId;
      }
      if (values.startType === '2') {
        values.startTime = dayjs(values.startDate + ' ' + values.startTime).valueOf();
      }
      if (values.endType === '2') {
        values.endTime = dayjs(values.endDate + ' ' + values.endTime).valueOf();
      }
      values.startType = Number(values.startType);
      values.endType = Number(values.endType);
      const formData = omit(values, ['startDate', 'endDate']);
      await setFirmwareConfig(formData);
      setVisible(false);
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      refresh?.();
    },
    { manual: true },
  );

  const onClose = () => {
    setVisible(false);
  };

  const deleteProductGroup = (index: number) => {
    const oldValue = [...prdGroupList];
    oldValue.splice(index, 1);
    setPrdGroupList(oldValue);
    formRef.current?.setFieldsValue({ productGroupNames: oldValue.map((item) => item.groupName) });
  };

  const deleteTestGroup = (index: number) => {
    const oldValue = [...testGroupList];
    oldValue.splice(index, 1);
    setTestGroupList(oldValue);
    formRef.current?.setFieldsValue({ testGroupNames: oldValue.map((item) => item.groupName) });
  };

  const productColumns: ProColumns[] = [
    {
      title: <FormattedMessage id="webOperation_group_groupName_tableColumn_text" />,
      dataIndex: 'groupName',
      width: 350,
    },
    {
      title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
      dataIndex: 'option',
      valueType: 'option',
      width: 100,
      render: (_, record, index) => (
        <Access accessible={access.canDeleteProGroupOtaconfig()}>
          <a
            key="delete"
            onClick={() => {
              deleteProductGroup(index);
            }}
          >
            <FormattedMessage id="webCommon_page_delete_tableButton_linkText" />
          </a>
        </Access>
      ),
    },
  ];

  const testColumns: ProColumns[] = [
    {
      title: <FormattedMessage id="webOperation_group_groupName_tableColumn_text" />,
      dataIndex: 'groupName',
      width: 350,
    },
    {
      title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
      dataIndex: 'option',
      valueType: 'option',
      width: 100,
      render: (_, record, index) => (
        <Access accessible={access.canDeleteTestGroupOtaconfig()}>
          <a
            key="delete"
            onClick={() => {
              deleteTestGroup(index);
            }}
          >
            <FormattedMessage id="webCommon_page_delete_tableButton_linkText" />
          </a>
        </Access>
      ),
    },
  ];

  useImperativeHandle(ref, () => ({
    edit: (id: string, record: API.FirmwareReleaseDetail) => {
      setTestGroupList([]);
      setPrdGroupList([]);
      /* 获取到配置详情后，数据回显 */
      if (record.testGroupNames!.length > 0) {
        // 开始时间
        if (record.startType === 2) {
          const start = split(dayjs(record.startTime as number).format('YYYY-MM-DD HH:mm:ss'), ' ');
          record.startDate = start[0];
          record.startTime = start[1];
        } else {
          record.startZone = undefined;
          record.startDate = undefined;
          record.startTime = undefined;
        }
        // 结束时间
        if (record.endType === 2) {
          const end = split(dayjs(record.endTime as number).format('YYYY-MM-DD HH:mm:ss'), ' ');
          record.endDate = end[0];
          record.endTime = end[1];
        } else {
          record.endZone = undefined;
          record.endDate = undefined;
          record.endTime = undefined;
        }
        record.startType = String(record.startType);
        record.endType = String(record.endType);
        // 测试分组
        if (isArray(record.testGroupNames)) {
          formRef.current?.setFieldsValue({
            testGroupNames: record.testGroupNames,
          });
          setTestGroupList(
            map(record.testGroupNames, (item: string) => ({
              groupName: item,
            })),
          );
        }
        // 生产分组
        if (isArray(record.productGroupNames)) {
          formRef.current?.setFieldsValue({
            productGroupNames: record.productGroupNames,
          });
          setPrdGroupList(
            map(record.productGroupNames, (item: string) => ({
              groupName: item,
            })),
          );
        }
      }
      setInitialValues(record || {});
      setJobId(id);
      setVisible(true);
    },
  }));

  const updatePrdGroup = (list: API.GroupItem[]) => {
    const newValues = [...list, ...prdGroupList];
    setPrdGroupList(newValues);
    formRef.current?.setFieldsValue({ productGroupNames: newValues.map((item) => item.groupName) });
  };

  const updateTestGroup = (list: API.GroupItem[]) => {
    const newValues = [...list, ...testGroupList];
    setTestGroupList(newValues);
    formRef.current?.setFieldsValue({ testGroupNames: newValues.map((item) => item.groupName) });
  };

  const startColumns: typeColumns[] = [
    {
      dataIndex: 'startZone',
      langCode: 'webOperation_release_zone_tableColumn_text',
      width: 150,
      showInForm: true,
      required: true,
      valueType: 'select',
      valueEnum: zone,
    },
    {
      dataIndex: 'startDate',
      langCode: 'webOperation_release_date_tableColumn_text',
      width: 150,
      showInForm: true,
      required: true,
      valueType: 'date',
    },
    {
      dataIndex: 'startTime',
      langCode: 'webOperation_release_time_tableColumn_text',
      width: 150,
      showInForm: true,
      required: true,
      valueType: 'time',
    },
  ];

  const endColumns: typeColumns[] = [
    {
      dataIndex: 'endZone',
      langCode: 'webOperation_release_zone_tableColumn_text',
      width: 150,
      showInForm: true,
      required: true,
      valueType: 'select',
      valueEnum: zone,
    },
    {
      dataIndex: 'endDate',
      langCode: 'webOperation_release_date_tableColumn_text',
      width: 150,
      showInForm: true,
      required: true,
      valueType: 'date',
    },
    {
      dataIndex: 'endTime',
      langCode: 'webOperation_release_time_tableColumn_text',
      width: 150,
      showInForm: true,
      required: true,
      valueType: 'time',
    },
  ];
  const dict = {
    startType: [
      {
        label: <FormattedMessage id="webOperation_dictionary_immediately_select_text" />,
        value: '1',
      },
      {
        label: <FormattedMessage id="webOperation_dictionary_timeing_select_text" />,
        value: '2',
      },
    ],
    endType: [
      {
        label: <FormattedMessage id="webOperation_dictionary_neverExpires_select_text" />,
        value: '1',
      },
      {
        label: <FormattedMessage id="webOperation_dictionary_timeing_select_text" />,
        value: '2',
      },
    ],
  };

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: 'webOperation_product_firmwareconfig_drawer_title' })}
        width="50%"
        onCancel={onClose}
        onConfirm={() => formRef.current?.submit()}
        open={visible}
        destroyOnClose={true}
        loading={loading}
      >
        <ProForm
          initialValues={initialValues}
          onFinish={handleSubmit}
          formRef={formRef}
          layout="horizontal"
          labelCol={{ flex: '150px' }}
          onReset={onClose}
          submitter={false}
        >
          <ProFormTextArea
            name="releaseContent"
            label={intl.formatMessage({
              id: 'webOperation_firmware_releaseContent_tableColumn_text',
            })}
            rules={[
              {
                required: true,
                whitespace: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                  intl.formatMessage({
                    id: 'webOperation_firmware_releaseContent_tableColumn_text',
                  }),
              },
              len500,
            ]}
            placeholder={
              intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
              intl.formatMessage({ id: 'webOperation_firmware_releaseContent_tableColumn_text' })
            }
          />
          {initialValues.langId ? (
            <ProFormText
              name="langId"
              label={intl.formatMessage({ id: 'webCommon_page_langId_common_text' })}
              readonly={true}
            />
          ) : null}
          <ProForm.Item
            name="productGroupNames"
            label={intl.formatMessage({ id: 'webOperation_release_prdGroup_tableColumn_text' })}
          >
            <ResizableTable<API.GroupItem, API.PageParams>
              rowKey={(record) => record.groupName}
              defaultSize="small"
              search={false}
              options={false}
              columns={productColumns}
              pagination={false}
              headerTitle={
                <GroupList
                  key="prdGroup"
                  groupType="DEVICE_GROUP"
                  moduleName="ProOtaconfig"
                  selectedKeys={prdGroupList}
                  setData={updatePrdGroup}
                />
              }
              dataSource={prdGroupList}
            />
          </ProForm.Item>
          <ProForm.Item
            name="testGroupNames"
            label={intl.formatMessage({ id: 'webOperation_release_testGroup_tableColumn_text' })}
            rules={[
              {
                required: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_release_testGroup_tableColumn_text' }),
              },
            ]}
          >
            <ResizableTable<API.GroupItem, API.PageParams>
              rowKey={(record) => record.groupName}
              defaultSize="small"
              search={false}
              options={false}
              columns={testColumns}
              pagination={false}
              headerTitle={
                <GroupList
                  key="testGroup"
                  groupType="DEVICE_GROUP"
                  moduleName="TestOtaconfig"
                  selectedKeys={testGroupList}
                  setData={updateTestGroup}
                />
              }
              dataSource={testGroupList}
            />
          </ProForm.Item>
          <ProFormFieldSet
            name="start"
            label={intl.formatMessage({ id: 'webOperation_release_start_tableColumn_text' })}
            type="group"
            initialValue={initialValues.startType ? 0 : undefined}
            rules={[
              {
                required: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_release_start_tableColumn_text' }),
              },
            ]}
            transform={(value: any) => ({})}
          >
            <Space>
              <ProFormRadio.Group name="startType" label={false} options={dict['startType']} />
              <ProFormDependency name={['startType']}>
                {({ startType }) =>
                  startType === '2' ? (
                    <ProFormFieldSet name="startGroup" type="group" label={false}>
                      <FormFields columns={startColumns} pageName="release" hideLabel={true} />
                    </ProFormFieldSet>
                  ) : null
                }
              </ProFormDependency>
            </Space>
          </ProFormFieldSet>
          <ProFormFieldSet
            name="end"
            label={intl.formatMessage({ id: 'webOperation_release_end_tableColumn_text' })}
            type="group"
            initialValue={initialValues.endType ? 0 : undefined}
            rules={[
              {
                required: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_release_end_tableColumn_text' }),
              },
              {
                validator: (_, value) => {
                  if (formRef.current?.getFieldValue('endType') === '2') {
                    const endZone = formRef.current?.getFieldValue('endZone');
                    const endDate = dayjs(formRef.current?.getFieldValue('endDate')).format(
                      'YYYY-MM-DD',
                    );
                    let endTime = dayjs(formRef.current?.getFieldValue('endTime')).format(
                      'HH:mm:ss',
                    );
                    let timeWithDate = formRef.current?.getFieldFormatValue?.('endTime') as string;
                    if (isNaN(Date.parse(timeWithDate))) {
                      endTime = timeWithDate;
                    }
                    const end = dayjs(endDate + ' ' + endTime)
                      .add(endZone, 'hour')
                      .valueOf();
                    if (formRef.current?.getFieldValue('startType') === '2') {
                      const startZone = formRef.current?.getFieldValue('startZone');
                      const startDate = dayjs(formRef.current?.getFieldValue('startDate')).format(
                        'YYYY-MM-DD',
                      );
                      let startTime = dayjs(formRef.current?.getFieldValue('startTime')).format(
                        'HH:mm:ss',
                      );
                      timeWithDate = formRef.current?.getFieldFormatValue?.('startTime') as string;
                      if (isNaN(Date.parse(timeWithDate))) {
                        startTime = timeWithDate;
                      }
                      const start = dayjs(startDate + ' ' + startTime)
                        .add(startZone, 'hour')
                        .valueOf();
                      if (start > end)
                        return Promise.reject(
                          new Error(
                            intl.formatMessage({ id: 'webCommon_page_time_validator_message' }),
                          ),
                        );
                    }
                    if (formRef.current?.getFieldValue('startType') === '1') {
                      const start = dayjs().valueOf();
                      if (start > end)
                        return Promise.reject(
                          new Error(
                            intl.formatMessage({ id: 'webCommon_page_time_validator_message' }),
                          ),
                        );
                    }
                  }
                  return Promise.resolve();
                },
              },
            ]}
            transform={(value: any) => ({})}
          >
            <Space>
              <ProFormRadio.Group name="endType" label={false} options={dict['endType']} />
              <ProFormDependency name={['endType']}>
                {({ endType }) =>
                  endType === '2' ? (
                    <ProFormFieldSet name="endGroup" type="group" label={false}>
                      <FormFields columns={endColumns} pageName="release" hideLabel={true} />
                    </ProFormFieldSet>
                  ) : null
                }
              </ProFormDependency>
            </Space>
          </ProFormFieldSet>
        </ProForm>
      </FormContainer>
    </>
  );
});

export default FirmwareReleaseConfig;
