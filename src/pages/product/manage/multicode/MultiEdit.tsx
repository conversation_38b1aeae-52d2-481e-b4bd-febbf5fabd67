import { useRequest } from '@@/plugin-request/request';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormDatePicker,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { Button, message } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Access } from '@@/plugin-access/access';

import FormContainer from '@/components/Form/FormContainer';
import { createMultiCode } from '@/services/code';
import { len128 } from '@/utils/validate';

export interface MultiFormProps {
  refresh?: (resetPageIndex?: boolean | undefined) => Promise<void> | undefined;
  productSnCode: string;
}

export interface RefType {
  edit: (item: API.MultiCodeItem) => void;
}

const MultiForm = React.forwardRef<RefType, MultiFormProps>((props, ref) => {
  const { refresh, productSnCode } = props;
  const intl = useIntl();
  const [visible, setVisible] = useState<boolean>(false);
  const formRef = useRef<ProFormInstance<API.MultiCodeItem>>();
  const [initialValues, setInitialValues] = useState<Partial<API.MultiCodeItem>>({});
  const access = useAccess();

  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    edit: (item: API.MultiCodeItem) => {
      setInitialValues(item);
      setVisible(true);
    },
  }));

  const { run, loading } = useRequest(
    async (data) => {
      data.productSnCode = productSnCode;
      await createMultiCode(data);
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setVisible(false);
      refresh?.();
    },
    {
      manual: true,
    },
  );

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: 'webOperation_code_add_common_text' })}
        width="50%"
        loading={loading}
        onCancel={onClose}
        onConfirm={() => formRef.current?.submit()}
        open={visible}
        destroyOnClose={true}
      >
        <ProForm
          formRef={formRef}
          initialValues={initialValues}
          onFinish={async (values) => {
            await run(values);
            setVisible(false);
            refresh?.();
          }}
          labelCol={{ span: 5 }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
        >
          <ProFormText name="id" hidden={true} />
          <ProFormText
            name="deviceId"
            label={intl.formatMessage({
              id: 'webOperation_code_deviceId_tableColumn_text',
            })}
            rules={[
              {
                required: true,
                whitespace: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_code_deviceId_tableColumn_text' }),
              },
              len128,
            ]}
            placeholder={
              intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
              intl.formatMessage({ id: 'webOperation_code_deviceId_tableColumn_text' })
            }
          />
          <ProFormText
            name="sn"
            label={intl.formatMessage({
              id: 'webOperation_code_sn_tableColumn_text',
            })}
            rules={[
              {
                required: true,
                whitespace: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_code_sn_tableColumn_text' }),
              },
              len128,
            ]}
            placeholder={
              intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
              intl.formatMessage({ id: 'webOperation_code_sn_tableColumn_text' })
            }
          />
          <ProFormText
            name="moCode"
            label={intl.formatMessage({
              id: 'webOperation_code_moCode_tableColumn_text',
            })}
            rules={[
              {
                required: true,
                whitespace: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_code_moCode_tableColumn_text' }),
              },
              len128,
            ]}
            placeholder={
              intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
              intl.formatMessage({ id: 'webOperation_code_moCode_tableColumn_text' })
            }
          />
          <ProFormText
            name="mes"
            label={intl.formatMessage({
              id: 'webOperation_code_mes_tableColumn_text',
            })}
            rules={[
              {
                required: true,
                whitespace: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_code_mes_tableColumn_text' }),
              },
              len128,
            ]}
            placeholder={
              intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
              intl.formatMessage({ id: 'webOperation_code_mes_tableColumn_text' })
            }
          />
          <ProFormText
            name="itemCode"
            label={intl.formatMessage({
              id: 'webOperation_code_itemCode_tableColumn_text',
            })}
            rules={[
              {
                required: true,
                whitespace: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_code_itemCode_tableColumn_text' }),
              },
              len128,
            ]}
            placeholder={
              intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
              intl.formatMessage({ id: 'webOperation_code_itemCode_tableColumn_text' })
            }
          />
          <ProFormDatePicker
            name="productionDate"
            label={intl.formatMessage({
              id: 'webOperation_code_productionDate_tableColumn_text',
            })}
            rules={[
              {
                required: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_code_productionDate_tableColumn_text' }),
              },
            ]}
            width="sm"
            placeholder={
              intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
              intl.formatMessage({ id: 'webOperation_code_productionDate_tableColumn_text' })
            }
          />
        </ProForm>
      </FormContainer>
      <Access accessible={access.canCreateCode()}>
        <Button
          type="primary"
          onClick={() => {
            setInitialValues({} as any);
            setVisible(true);
          }}
        >
          <PlusOutlined />
          <FormattedMessage id="webOperation_code_add_common_text" />
        </Button>
      </Access>
    </>
  );
});

export default MultiForm;
