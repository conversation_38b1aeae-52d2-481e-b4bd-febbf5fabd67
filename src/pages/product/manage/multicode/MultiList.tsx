import { useRequest } from '@@/plugin-request/request';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProFormInstance } from '@ant-design/pro-components';
import { Access } from '@@/plugin-access/access';
import { Divider, message, Modal, Space, Button } from 'antd';
import { useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { extra, dateFilter, fieldProps, useColumn, typeColumns } from '@/hooks/column';
import ResizableTable, { beforeSearchSubmit } from '@/components/Table';
import { downloadByData } from '@/utils/download';
import MultiEdit, { RefType } from './MultiEdit';
import Import from '@/components/Import/index';

import { editStatus, getMultiCodeList, exportMultiCodes, getTemplate } from '@/services/code';

const MultiList = ({ detail }: { detail: API.ProductItem }) => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const editRef = useRef<RefType>(null);
  const intl = useIntl();
  const access = useAccess();
  const { createColumns } = useColumn();
  const [visible, setVisible] = useState<boolean>(false);
  const [code, setcode] = useState<Partial<API.MultiCodeItem>>({});
  const { run, loading } = useRequest(editStatus, { manual: true });

  const { run: handleExport, loading: exportLoading } = useRequest(
    async () => {
      const file = await exportMultiCodes({
        ...{ productSnCode: detail.productSnCode! },
        ...beforeSearchSubmit(formRef.current?.getFieldsFormatValue?.()),
      });
      downloadByData({
        data: file,
        filename: intl.formatMessage({ id: 'webOperation_code_export_fileName_text' }) + '.csv',
      });
    },
    { manual: true },
  );

  const editDeviceStatus = async () => {
    const status = 1 - (code as API.MultiCodeItem).status;
    await run((code as API.MultiCodeItem).deviceId, status);
    message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
    setVisible(false);
    actionRef.current?.reload();
  };

  const showStopDialog = (record: API.MultiCodeItem) => {
    setVisible(true);
    setcode(record);
  };

  const actionColumn: ProColumns<API.MultiCodeItem> = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 150,
    fixed: 'right',
    render: (_, record) => [
      <Space key="edit" split={<Divider type="vertical" />}>
        <Access accessible={access.canEditCodeStatus()}>
          <a
            onClick={() => {
              showStopDialog(record);
            }}
          >
            <FormattedMessage
              id={
                record.status === 1
                  ? 'webOperation_code_invalid_tableButton_text'
                  : 'webOperation_code_valid_tableButton_text'
              }
            />
          </a>
        </Access>
      </Space>,
    ],
  };

  const listColumns: typeColumns[] = [
    ...dateFilter,
    {
      dataIndex: 'deviceId',
      fixed: 'left',
      width: 150,
    },
    {
      dataIndex: 'sn',
      width: 150,
    },
    {
      dataIndex: 'moCode',
      width: 150,
    },
    {
      dataIndex: 'mes',
      width: 150,
    },
    {
      dataIndex: 'itemCode',
      width: 150,
    },
    {
      dataIndex: 'iccid',
      width: 150,
    },
    {
      dataIndex: 'productionDate',
      width: 150,
      valueType: 'date',
      hideInSearch: true,
    },
    {
      dataIndex: 'productDate',
      valueType: 'dateRange',
      hideInTable: true,
      showLabel: true,
      langCode: 'webOperation_code_productionDate_tableColumn_text',
      fieldProps,
    },
    {
      dataIndex: 'status',
      width: 150,
      valueEnum: {
        0: intl.formatMessage({
          id: 'webOperation_dictionary_disuse_select_text',
        }),
        1: intl.formatMessage({
          id: 'webOperation_dictionary_normal_select_text',
        }),
      },
    },
    ...extra,
  ];
  const initColumns = [...listColumns, actionColumn];
  const allColumns = createColumns('code', initColumns);

  const onCancel = () => {
    setVisible(false);
  };

  return (
    <>
      <ResizableTable<API.MultiCodeItem, API.MultiCodePageParams>
        actionRef={actionRef}
        formRef={formRef}
        rowKey="id"
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        scroll={{ x: 1300 }}
        headerTitle={
          <Space>
            <Access accessible={access.canExportCode()}>
              <Button loading={exportLoading} type="primary" ghost onClick={handleExport}>
                <FormattedMessage id="webCommon_page_export_button_text" />
              </Button>
            </Access>
            <Access accessible={access.canImportCode()}>
              <Import
                key="import"
                pageName="code"
                getTemplate={getTemplate}
                url="/operation-platform/device/code/upload"
                urlParam="deviceCodeFile"
                data={{ productId: detail.id }}
                refresh={() => {
                  return actionRef.current?.reload();
                }}
              />
            </Access>
            <MultiEdit
              ref={editRef}
              key="create"
              refresh={() => {
                return actionRef.current?.reload();
              }}
              productSnCode={detail.productSnCode!}
            />
          </Space>
        }
        params={{ productSnCode: detail.productSnCode! }}
        request={getMultiCodeList}
        columns={allColumns}
      />
      <Modal
        title={
          <FormattedMessage
            id={
              code.status === 1
                ? 'webOperation_code_invalid_modal_title'
                : 'webOperation_code_valid_modal_title'
            }
          />
        }
        width="50%"
        onCancel={onCancel}
        onOk={editDeviceStatus}
        open={visible}
        destroyOnClose={true}
        confirmLoading={loading}
      >
        <FormattedMessage
          id={
            code.status === 1
              ? 'webOperation_code_invalid_modal_message'
              : 'webOperation_code_valid_modal_message'
          }
        />
      </Modal>
    </>
  );
};
export default MultiList;
