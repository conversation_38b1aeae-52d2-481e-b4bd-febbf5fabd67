import { Card, Spin } from 'antd';
import { useRequest } from '@@/plugin-request/request';
import Description from '@/components/Description';
import { useCallback, useContext } from 'react';
import {
  createGuide,
  createManual,
  deleteGuide,
  deleteManual,
  downloadGuide,
  downloadManual,
  listGuide,
  listManual,
  updateDesc,
  updateGuide,
  updateManual,
  viewFaq,
} from '@/services/product';
import { DefinitionContext } from '@/utils/context';
import GuideList from '@/pages/product/manage/faq/GuideList';
import FaqList from '@/pages/product/manage/faq/FaqList';
import { useIntl } from 'umi';
import { FormattedMessage } from '@@/plugin-locale/localeExports';
import { rbac } from '@/access';
import { assign } from 'lodash-es';
import { getS3File } from '@/services/common';

const FaqDetail = ({ proDetail }: { proDetail: API.ProductItem }) => {
  const { id } = useContext(DefinitionContext);
  const intl = useIntl();
  const { data: { data: detail = {} } = {}, loading, refresh } = useRequest(() => viewFaq(id));
  const handleSubmit = useCallback(
    (type: string) => {
      return async (values: API.MultiLanguage) => {
        await updateDesc(type, { item: values.message!, productId: id });
        refresh();
      };
    },
    [id, refresh],
  );

  return (
    <div style={{ background: '#fff', overflow: 'hidden' }}>
      <Spin spinning={loading}>
        <Description
          label="webOperation_product_brief_input_text"
          value={detail.shortDescription}
          onSubmit={handleSubmit('shortDescription')}
          code={rbac.PRODUCT.EDITSHORT}
          copyCode={rbac.PRODUCT.COPYSHORT}
        />
        <Description
          label="webOperation_product_description_input_text"
          value={detail.longDescription}
          onSubmit={handleSubmit('longDescription')}
          code={rbac.PRODUCT.EDITLONG}
          copyCode={rbac.PRODUCT.COPYLONG}
        />
        <Description
          label="webOperation_product_spec_input_text"
          value={detail.technicalSpecification}
          onSubmit={handleSubmit('technicalSpecification')}
          code={rbac.PRODUCT.EDITTECH}
          copyCode={rbac.PRODUCT.COPYTECH}
        />
        <Card
          style={{ margin: 20 }}
          title={intl.formatMessage({ id: 'webOperation_product_manual_table_text' })}
          size="small"
        >
          <GuideList
            accept=".pdf"
            text={<FormattedMessage id="webOperation_file_add_button_title" />}
            listFn={listManual}
            deleteFn={async (data: API.ManualItem) =>
              deleteManual({ operateItemId: data.productManualId!, productId: id })
            }
            createFn={async (data: API.ManualItem) => {
              if (String(data.type) === '2') {
                const { key } = await getS3File(data.url as string);
                data.s3key = key;
              }
              await createManual(assign({}, data, { productId: id }));
            }}
            updateFn={async (data: API.ManualItem) => {
              if (String(data.type) === '2') {
                const { key } = await getS3File(data.url as string);
                data.s3key = key;
              }
              await updateManual(assign({}, data, { productId: id }));
            }}
            downloadFn={async (data: API.ManualItem) =>
              downloadManual(
                {
                  operateItemId: data.productManualId!,
                  productId: id,
                },
                data.name?.message,
              )
            }
            rbac={rbac.PRODUCT.MANUAL}
          />
        </Card>
        <Card
          style={{ margin: 20 }}
          title={intl.formatMessage({ id: 'webOperation_product_guide_table_text' })}
          size="small"
        >
          <GuideList
            accept=".mp4"
            text={<FormattedMessage id="webCommon_page_add_button_text" />}
            sortable
            listFn={listGuide}
            deleteFn={async (data: API.ManualItem) =>
              deleteGuide({ operateItemId: data.productOperationGuidanceId!, productId: id })
            }
            createFn={async (data: API.ManualItem) => {
              if (String(data.type) === '2') {
                const { key } = await getS3File(data.url as string);
                data.s3key = key;
              }
              await createGuide(assign({}, data, { productId: id }));
            }}
            updateFn={async (data: API.ManualItem) => {
              if (String(data.type) === '2') {
                const { key } = await getS3File(data.url as string);
                data.s3key = key;
              }
              await updateGuide(assign({}, data, { productId: id }));
            }}
            downloadFn={async (data: API.ManualItem) =>
              downloadGuide(
                {
                  operateItemId: data.productOperationGuidanceId!,
                  productId: id,
                },
                data.name?.message,
              )
            }
            rbac={rbac.PRODUCT.GUIDE}
          />
        </Card>
        <Card
          style={{ margin: 20 }}
          title={intl.formatMessage({ id: 'webOperation_product_faq_table_text' })}
          size="small"
        >
          <FaqList rbac={rbac.PRODUCT.FAQ} />
        </Card>
      </Spin>
    </div>
  );
};

export default FaqDetail;
