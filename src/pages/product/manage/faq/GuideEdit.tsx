import FormContainer from '@/components/Form/FormContainer';
import { len128, urlChecked, len500 } from '@/utils/validate';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormInstance,
  ProFormRadio,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Button, message } from 'antd';
import React, {
  ReactElement,
  useContext,
  ReactNode,
  Ref,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { assign, get, omit } from 'lodash-es';
import { FileUpload } from '@/components/Form/FileUpload';
import { uploadImage } from '@/services/common';
import { ProFormDependency } from '@ant-design/pro-form';
import { LocaleID } from '@/components/Locale';
import { DefinitionContext } from '@/utils/context';
type ForwardType<T = any> = (
  props: APP.EditFormProps & {
    createFn: Function;
    updateFn: Function;
    text: ReactNode;
    rbac: Record<string, string>;
    accept?: string;
  },
  ref: Ref<APP.RefType<T>>,
) => ReactElement | null;

const GuideEdit: ForwardType = (props, ref) => {
  const { refresh, createFn = () => {}, updateFn = () => {}, text, accept, rbac } = props;
  const intl = useIntl();
  const formRef = useRef<ProFormInstance<API.ManualItem>>();
  const [visible, setVisible] = useState<boolean>(false);
  const [readonly, setReadonly] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState<API.ManualItem>({} as any);
  const { readonly: releaseMode } = useContext(DefinitionContext);
  const access = useAccess();

  const { run: handleSubmit, loading } = useRequest(
    async (values) => {
      if (!readonly) {
        if (initialValues.instanceId) {
          await updateFn({ ...initialValues, ...values });
        } else {
          await createFn({ ...initialValues, ...values });
        }
        message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
        refresh?.();
      }
      setVisible(false);
    },
    { manual: true },
  );

  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    edit: (item: API.ManualItem, r) => {
      setReadonly(!!r);
      if (!access.checkAuth(rbac.UPDATE)) {
        console.warn(intl.formatMessage({ id: 'webCommon_page_noAccess_toast_text' }));
        return;
      }
      setInitialValues(item.typeCode !== '1' ? omit(item, ['fileSize', 'ext']) : item);
      setVisible(true);
    },
  }));

  const onCreate = () => {
    setReadonly(false);
    setInitialValues({} as any);
    setVisible(true);
  };

  return (
    <>
      <FormContainer
        title={
          readonly
            ? intl.formatMessage({ id: 'webOperation_file_view_drawer_title' })
            : initialValues.instanceId
            ? intl.formatMessage({ id: 'webOperation_file_edit_drawer_title' })
            : intl.formatMessage({ id: 'webOperation_file_create_drawer_title' })
        }
        width="50%"
        onCancel={onClose}
        onConfirm={() => formRef.current?.submit()}
        open={visible}
        destroyOnClose={true}
        loading={loading}
        hiddenConfirm={readonly}
      >
        <ProForm
          initialValues={initialValues}
          onFinish={handleSubmit}
          formRef={formRef}
          labelCol={{ flex: '150px' }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
          readonly={readonly}
        >
          {initialValues.instanceId ? (
            <ProFormText
              label={intl.formatMessage({ id: 'webOperation_file_id_input_text' })}
              name="instanceId"
              readonly
              required
            />
          ) : null}

          <ProFormRadio.Group
            name="type"
            initialValue="1"
            required
            label={intl.formatMessage({ id: 'webOperation_guide_typeCode_tableColumn_text' })}
            valueEnum={{
              1: intl.formatMessage({ id: 'webOperation_dictionary_localFileUpload_select_text' }),
              2: intl.formatMessage({ id: 'webOperation_dictionary_S3FileAddress_select_text' }),
            }}
            fieldProps={{
              onChange: () => {
                const data = formRef.current?.getFieldsValue();
                if (!get(data, ['s3key']))
                  formRef.current?.setFieldsValue({
                    file: undefined,
                  });
              },
            }}
          />

          <ProFormDependency name={['type']}>
            {({ type }) => {
              return String(type) === '1' ? (
                <>
                  <ProForm.Item name="s3key" hidden />
                  <ProForm.Item
                    name="file"
                    label={intl.formatMessage({
                      id: 'webOperation_guide_file_tableColumn_text',
                    })}
                    rules={[
                      {
                        required: true,
                        message: <FormattedMessage id="webCommon_page_file_select_placeholder" />,
                      },
                    ]}
                    tooltip={
                      intl.formatMessage({ id: 'webOperation_file_add_tooltip_title' }) + accept
                    }
                  >
                    <FileUpload
                      maxCount={1}
                      presigned
                      fileName={initialValues.fileName?.message}
                      accept={accept}
                      getSignedUrl={async ({ name, size, type: ext }) => {
                        try {
                          const data = await uploadImage({
                            fileName: name || '',
                            fileType: 'picture',
                          });

                          const fileName = formRef.current?.getFieldValue('name');
                          formRef.current?.setFieldsValue({
                            fileName: assign({}, fileName, { message: name }),
                            fileSize: size,
                            s3key: data.key,
                            ext,
                          });
                          return data;
                        } catch (e) {
                          message.error(
                            intl.formatMessage({ id: 'webCommon_page_uploadFailed_toast_message' }),
                          );
                        }
                      }}
                      disabled={readonly}
                    />
                  </ProForm.Item>
                  <ProFormText
                    name={['fileName', 'message']}
                    label={intl.formatMessage({
                      id: 'webOperation_file_name_input_text',
                    })}
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({
                          id: 'webOperation_file_name_input_placeholder',
                        }),
                      },
                      len128,
                    ]}
                    extra={
                      !readonly ? (
                        <ProFormDependency name={['fileName', 'langId']}>
                          {({ fileName = {} }) => <LocaleID id={fileName?.langId} />}
                        </ProFormDependency>
                      ) : null
                    }
                  />
                  <ProFormText
                    label={intl.formatMessage({ id: 'webOperation_file_nameLangId_input_text' })}
                    name={['fileName', 'langId']}
                    hidden={!readonly}
                    readonly
                  />
                  <ProFormText
                    name={['description', 'message']}
                    label={intl.formatMessage({
                      id: 'webOperation_file_desc_input_text',
                    })}
                    rules={[len128]}
                    extra={
                      !readonly ? (
                        <ProFormDependency name={['description', 'langId']}>
                          {({ description = {} }) => <LocaleID id={description?.langId} />}
                        </ProFormDependency>
                      ) : null
                    }
                  />
                  <ProFormText
                    label={intl.formatMessage({
                      id: 'webOperation_file_descLang_input_text',
                    })}
                    name={['description', 'langId']}
                    hidden={!readonly}
                    readonly
                  />
                  <ProFormText
                    name="ext"
                    label={intl.formatMessage({
                      id: 'webOperation_file_type_input_text',
                    })}
                    disabled={true}
                  />
                  <ProFormText
                    name="fileSize"
                    label={intl.formatMessage({
                      id: 'webOperation_file_size_input_text',
                    })}
                    hidden
                  />
                  <ProFormDependency name={['fileSize']}>
                    {({ fileSize }) => {
                      return (
                        <ProFormText
                          label={intl.formatMessage({
                            id: 'webOperation_file_size_input_text',
                          })}
                          fieldProps={{
                            value:
                              fileSize === undefined
                                ? ''
                                : Math.floor((fileSize || 0) / 1024) + 'KB',
                          }}
                          disabled
                          placeholder={intl.formatMessage({
                            id: 'webOperation_file_size_input_text',
                          })}
                        />
                      );
                    }}
                  </ProFormDependency>
                </>
              ) : (
                <>
                  <ProFormTextArea
                    name="url"
                    label={intl.formatMessage({
                      id: 'webOperation_file_link_input_text',
                    })}
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({
                          id: 'webOperation_file_link_input_placeholder',
                        }),
                      },
                      urlChecked,
                      len500,
                    ]}
                    placeholder={intl.formatMessage({
                      id: 'webOperation_file_link_input_placeholder',
                    })}
                  />
                  <ProFormText
                    name={['fileName', 'message']}
                    label={intl.formatMessage({
                      id: 'webOperation_file_name_input_text',
                    })}
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({
                          id: 'webOperation_file_name_input_placeholder',
                        }),
                      },
                      len128,
                    ]}
                    extra={
                      !readonly ? (
                        <ProFormDependency name={['fileName', 'langId']}>
                          {({ fileName = {} }) => <LocaleID id={fileName?.langId} />}
                        </ProFormDependency>
                      ) : null
                    }
                  />
                  <ProFormText
                    label={intl.formatMessage({ id: 'webOperation_file_nameLangId_input_text' })}
                    name={['fileName', 'langId']}
                    hidden={!readonly}
                    readonly
                  />
                  <ProFormText
                    name={['description', 'message']}
                    label={intl.formatMessage({
                      id: 'webOperation_file_desc_input_text',
                    })}
                    rules={[len128]}
                    extra={
                      !readonly ? (
                        <ProFormDependency name={['description', 'langId']}>
                          {({ description = {} }) => <LocaleID id={description?.langId} />}
                        </ProFormDependency>
                      ) : null
                    }
                  />
                  <ProFormText
                    label={intl.formatMessage({
                      id: 'webOperation_file_descLang_input_text',
                    })}
                    name={['description', 'langId']}
                    hidden={!readonly}
                    readonly
                  />
                </>
              );
            }}
          </ProFormDependency>
        </ProForm>
      </FormContainer>
      {releaseMode ? null : (
        <Access accessible={access.checkAuth(rbac.CREATE)}>
          <Button type="primary" onClick={onCreate}>
            <PlusOutlined />
            {text}
          </Button>
        </Access>
      )}
    </>
  );
};

export default React.forwardRef(GuideEdit);
