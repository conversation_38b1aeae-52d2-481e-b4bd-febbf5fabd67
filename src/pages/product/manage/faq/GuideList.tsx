import React, { ReactNode, useContext, useRef } from 'react';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { FormattedMessage } from '@@/plugin-locale/localeExports';
import { AccessLink } from '@/components/Button/AccessButton';
import ResizableTable from '@/components/Table';
import Space from '@/components/Space';
import styles from './index.less';
import { Space as AntSpace } from 'antd';
import { DefinitionContext } from '@/utils/context';
import GuideEdit from '@/pages/product/manage/faq/GuideEdit';
import GuideSorter from '@/pages/product/manage/faq/GuideSorter';
import { sortGuide } from '@/services/product';

interface GuideProps {
  listFn: Function;
  updateFn: Function;
  deleteFn: Function;
  createFn: Function;
  downloadFn: Function;
  sortFn?: Function;
  text: ReactNode;
  rbac: Record<string, string>;
  sortable?: boolean;
  accept?: string;
  namespace?: string;
}

const GuideList: React.FC<GuideProps> = ({
  listFn,
  updateFn,
  deleteFn,
  downloadFn,
  sortFn = sortGuide,
  createFn,
  sortable = false,
  text,
  accept,
  rbac,
  namespace = 'product',
}) => {
  const { id, readonly } = useContext(DefinitionContext);
  const actionRef = useRef<ActionType>();
  const editRef = useRef<APP.RefType<API.ManualItem>>(null);
  const columns: ProColumns<API.ManualItem>[] = [
    {
      title: <FormattedMessage id="webOperation_file_id_input_text" />,
      dataIndex: 'instanceId',
      fixed: 'left',
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_file_name_input_text" />,
      dataIndex: ['fileName', 'message'],
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_file_nameLangId_input_text" />,
      dataIndex: ['fileName', 'langId'],
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_file_desc_input_text" />,
      dataIndex: ['description', 'message'],
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_file_descLang_input_text" />,
      dataIndex: ['description', 'langId'],
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_file_url_tableColumn_text" />,
      dataIndex: 'url',
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_file_type_input_text" />,
      dataIndex: 'format',
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_file_size_input_text" />,
      dataIndex: 'size',
      render: (_, record) =>
        record.size === undefined || record.typeCode !== '1'
          ? ''
          : Math.floor((record.size || 0) / 1024) + 'KB',
      width: 150,
    },
    {
      title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
      width: 200,
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      render: (_: any, record) => (
        <Space>
          <AccessLink
            text="webCommon_page_view_common_text"
            code={rbac.VIEW}
            onClick={() => {
              editRef.current?.edit(record, true);
            }}
          />
          {record.ifCommon || readonly ? null : (
            <AccessLink
              text="webCommon_page_edit_common_text"
              code={rbac.UPDATE}
              onClick={() => {
                editRef.current?.edit(record);
              }}
            />
          )}
          {record.ifCommon || readonly ? null : (
            <AccessLink
              text="webCommon_page_delete_tableButton_linkText"
              code={rbac.DELETE}
              onClick={async () => {
                await deleteFn(record);
                actionRef.current?.reload();
              }}
              modal={{
                title: 'webCommon_page_confirmToDelete_modal_title',
                content: (
                  <div style={{ marginBottom: '10px' }}>
                    <div>
                      <FormattedMessage id="webOperation_product_deleteLine1_modal_message" />
                    </div>
                    <div>
                      <FormattedMessage id="webOperation_product_deleteLine2_modal_message" />
                    </div>
                  </div>
                ),
              }}
            />
          )}
          <AccessLink
            text="webCommon_page_browse_common_linkText"
            code={rbac.DOWNLOAD}
            onClick={async () => {
              await downloadFn(record);
            }}
          />
        </Space>
      ),
    },
  ];
  return (
    <ResizableTable
      rowKey="instanceId"
      actionRef={actionRef}
      defaultSize="small"
      search={false}
      scroll={{ x: 150 * 10 + 50 }}
      request={() => listFn({ req: id })}
      pagination={false}
      headerTitle={
        <AntSpace>
          <GuideEdit
            ref={editRef}
            refresh={() => actionRef.current?.reload()}
            createFn={createFn}
            updateFn={updateFn}
            text={text}
            accept={accept}
            rbac={rbac}
          />
          {sortable ? (
            <GuideSorter
              refresh={() => actionRef.current?.reload()}
              sortFn={sortFn}
              listFn={listFn}
              namespace={namespace}
              rbac={rbac}
            />
          ) : null}
        </AntSpace>
      }
      columns={columns}
      className={styles.tableContent}
    />
  );
};

export default GuideList;
