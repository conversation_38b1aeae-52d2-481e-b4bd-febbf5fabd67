import React, { useContext, useMemo, useRef } from 'react';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { FormattedMessage } from '@@/plugin-locale/localeExports';
import styles from './index.less';
import Space from '@/components/Space';
import { AccessLink } from '@/components/Button/AccessButton';
import { Space as AntSpace } from 'antd';
import ResizableTable from '@/components/Table';
import FaqEdit from '@/pages/product/manage/faq/FaqEdit';
import FaqSorter from '@/pages/product/manage/faq/FaqSorter';
import { deleteFaq, listFaq, createFaq, updateFaq, sortFaq } from '@/services/product';
import { DefinitionContext } from '@/utils/context';
import { useIntl } from 'umi';
import { get, omit } from 'lodash-es';

interface FaqProps<T = any, U = any> {
  listFn?: (params: T) => Promise<U>;
  updateFn?: Function;
  deleteFn?: Function;
  createFn?: Function;
  sortFn?: Function;
  namespace?: string;
  rbac: Record<string, string>;
}

const PRODUCT = 'product';

const FaqList: React.FC<FaqProps<Partial<API.AppQuestionPageParams>, API.SaleFaq>> = ({
  listFn = listFaq,
  updateFn = updateFaq,
  deleteFn = deleteFaq,
  createFn = createFaq,
  sortFn = sortFaq,
  namespace = PRODUCT,
  rbac,
}) => {
  const { id, readonly } = useContext(DefinitionContext);
  const actionRef = useRef<ActionType>();
  const editRef = useRef<APP.RefType<API.SaleFaq>>(null);
  const intl = useIntl();

  const isProduct = useMemo(() => {
    return namespace === PRODUCT;
  }, [namespace]);

  const columns: ProColumns<API.SaleFaq>[] = [
    {
      title: <FormattedMessage id="webOperation_product_faqId_tableColumn_text" />,
      dataIndex: 'instanceId',
      fixed: 'left',
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_product_faqType_tableColumn_text" />,
      dataIndex: 'typeCode',
      width: 150,
      valueEnum: {
        1: intl.formatMessage({ id: 'webOperation_dictionary_question1_select_text' }),
        2: intl.formatMessage({ id: 'webOperation_dictionary_question2_select_text' }),
        3: intl.formatMessage({ id: 'webOperation_dictionary_question3_select_text' }),
        common: intl.formatMessage({ id: 'webOperation_dictionary_common_select_text' }),
        notCommon: intl.formatMessage({
          id: 'webOperation_dictionary_notCommon_select_text',
        }),
      },
    },
    {
      title: <FormattedMessage id="webOperation_product_question_tableColumn_text" />,
      dataIndex: ['title', 'message'],
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_product_questionLangId_tableColumn_text" />,
      dataIndex: ['title', 'langId'],
      width: 150,
      formItemProps: {
        labelCol: { span: 200 },
      },
    },
    {
      title: <FormattedMessage id="webOperation_product_solution_tableColumn_text" />,
      dataIndex: ['answer', 'message'],
      hideInSearch: true,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_product_solutionLangId_tableColumn_text" />,
      dataIndex: ['answer', 'langId'],
      hideInSearch: true,
      width: 150,
    },
    {
      title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
      width: 200,
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      render: (_: any, record) => (
        <Space>
          <AccessLink
            text="webCommon_page_view_common_text"
            code={rbac.VIEW}
            onClick={() => {
              editRef.current?.edit(record, true);
            }}
          />
          {record.ifCommon || readonly ? null : (
            <AccessLink
              text="webCommon_page_edit_common_text"
              code={rbac.UPDATE}
              onClick={() => {
                editRef.current?.edit(record);
              }}
            />
          )}
          {record.ifCommon || readonly ? null : (
            <AccessLink
              text="webCommon_page_delete_tableButton_linkText"
              code={rbac.DELETE}
              onClick={async () => {
                await deleteFn({ operateItemId: record[`${namespace}FaqId`]!, productId: id });
                actionRef.current?.reload();
              }}
              modal={{
                title: 'webCommon_page_confirmToDelete_modal_title',
                content: (
                  <div style={{ marginBottom: '10px' }}>
                    <div>
                      <FormattedMessage id="webOperation_product_deleteLine1_modal_message" />
                    </div>
                    <div>
                      <FormattedMessage id="webOperation_product_deleteLine2_modal_message" />
                    </div>
                  </div>
                ),
              }}
            />
          )}
        </Space>
      ),
    },
  ];
  return (
    <ResizableTable<API.SaleFaq, API.AppQuestionPageParams>
      rowKey="instanceId"
      actionRef={actionRef}
      defaultSize="small"
      scroll={{ x: 150 * 7 + 50 }}
      request={listFn}
      pagination={false}
      headerTitle={
        <AntSpace>
          <FaqEdit
            ref={editRef}
            refresh={() => actionRef.current?.reload()}
            updateFn={updateFn}
            createFn={createFn}
            namespace={namespace}
            rbac={rbac}
          />
          <FaqSorter
            refresh={() => actionRef.current?.reload()}
            sortFn={sortFn}
            listFn={listFn}
            namespace={namespace}
            rbac={rbac}
          />
        </AntSpace>
      }
      columns={columns}
      className={styles.tableContent}
      beforeSearchSubmit={(params) => {
        return {
          ...omit(params, ['title']),
          ...(isProduct
            ? {
                productId: id,
              }
            : {
                partsId: id,
              }),
          title: get(params, ['title', 'message']),
          titleLangId: get(params, ['title', 'langId']),
        };
      }}
    />
  );
};

export default FaqList;
