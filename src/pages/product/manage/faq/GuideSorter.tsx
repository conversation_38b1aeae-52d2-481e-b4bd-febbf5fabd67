import React, { useContext, useRef, useState } from 'react';
import { ActionType, DragSortTable, ProColumns } from '@ant-design/pro-components';
import { FormattedMessage } from '@@/plugin-locale/localeExports';
import { Button, Tooltip } from 'antd';
import { DefinitionContext } from '@/utils/context';
import { useAccess } from 'umi';
import FormContainer from '@/components/Form/FormContainer';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { map } from 'lodash-es';
import styles from './index.less';
export interface SortTableProps {
  refresh?: () => void;
  listFn: Function;
  sortFn: Function;
  namespace?: string;
  rbac: Record<string, string>;
}

const GuideSorter: React.FC<SortTableProps> = ({ refresh, listFn, sortFn, namespace, rbac }) => {
  const { id, readonly } = useContext(DefinitionContext);
  const access = useAccess();
  const [visible, setVisible] = useState(false);
  const actionRef = useRef<ActionType>();
  const [list, setList] = useState<API.ManualItem[]>([]);

  const { run: loadData, loading } = useRequest(
    async () => {
      const { data } = await listFn({ req: id });
      setList(data);
    },
    { manual: true },
  );
  const columns: ProColumns<API.ManualItem>[] = [
    {
      title: <FormattedMessage id="webOperation_product_faqSort_tableColumn_text" />,
      dataIndex: 'sort',
      width: 60,
      hideInSearch: true,
      fixed: 'left',
    },
    {
      title: <FormattedMessage id="webOperation_file_id_input_text" />,
      dataIndex: 'instanceId',
      width: 170,
    },
    {
      title: <FormattedMessage id="webOperation_file_name_input_text" />,
      dataIndex: ['fileName', 'message'],
      width: 150,
      render: (_, record) => {
        return (
          <Tooltip placement="bottomLeft" title={record?.['fileName']?.['message']}>
            {record?.['fileName']?.['message']}
          </Tooltip>
        );
      },
    },
    {
      title: <FormattedMessage id="webOperation_file_nameLangId_input_text" />,
      dataIndex: ['fileName', 'langId'],
      width: 170,
    },
    {
      title: <FormattedMessage id="webOperation_file_desc_input_text" />,
      dataIndex: ['description', 'message'],
      width: 150,
      render: (_, record) => {
        return (
          <Tooltip placement="bottomLeft" title={record?.['description']?.['message']}>
            {record?.['description']?.['message']}
          </Tooltip>
        );
      },
    },
    {
      title: <FormattedMessage id="webOperation_file_descLang_input_text" />,
      dataIndex: ['description', 'langId'],
      width: 170,
    },
    {
      title: <FormattedMessage id="webOperation_file_url_tableColumn_text" />,
      dataIndex: 'url',
      ellipsis: true,
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_file_type_input_text" />,
      dataIndex: 'format',
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_file_size_input_text" />,
      dataIndex: 'size',
      width: 150,
    },
  ];

  const clear = () => {
    setList([]);
    setVisible(false);
  };

  const { run: handleConfirm, loading: confirmLoading } = useRequest(
    async () => {
      await sortFn({
        productId: id,
        [`${namespace}OperationGuidanceIds`]: map(
          list,
          (item) => item[`${namespace}OperationGuidanceId`]!,
        ),
      });
      clear();
      refresh?.();
    },
    { manual: true },
  );

  const handleDragSortEnd = (newDataSource: API.ManualItem[]) => {
    setList(newDataSource);
  };

  return (
    <>
      <FormContainer
        title={<FormattedMessage id="webOperation_guide_sort_button_title" />}
        width={700}
        onCancel={clear}
        onConfirm={handleConfirm}
        open={visible}
        destroyOnClose={true}
        loading={confirmLoading}
      >
        <DragSortTable<API.ManualItem>
          rowKey="instanceId"
          defaultSize="small"
          dragSortKey="sort"
          actionRef={actionRef}
          search={false}
          scroll={{ x: 150 * 8 + 60 }}
          dataSource={list}
          columns={columns}
          onDragSortEnd={handleDragSortEnd}
          onChange={loadData}
          loading={loading}
          pagination={false}
          toolBarRender={false}
          className={styles.dragSort}
        />
      </FormContainer>
      {readonly ? null : (
        <Access accessible={access.checkAuth(rbac.SORT)}>
          <Button
            onClick={() => {
              setVisible(true);
              loadData();
            }}
          >
            <FormattedMessage id="webOperation_guide_sort_button_title" />
          </Button>
        </Access>
      )}
    </>
  );
};

export default GuideSorter;
