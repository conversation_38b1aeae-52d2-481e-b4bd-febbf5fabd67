import React, { useContext, useMemo, useRef, useState } from 'react';
import { ActionType, DragSortTable, ProColumns } from '@ant-design/pro-components';
import { FormattedMessage } from '@@/plugin-locale/localeExports';
import { But<PERSON>, Tooltip } from 'antd';
import { DefinitionContext } from '@/utils/context';
import { useAccess, useIntl } from 'umi';
import FormContainer from '@/components/Form/FormContainer';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { map } from 'lodash-es';
import styles from './index.less';
export interface SortTableProps {
  refresh?: () => void;
  listFn: Function;
  sortFn: Function;
  namespace?: string;
  rbac: Record<string, string>;
}
const PRODUCT = 'product';
const FaqSorter: React.FC<SortTableProps> = ({
  refresh,
  listFn,
  sortFn,
  namespace = 'product',
  rbac,
}) => {
  const { id, readonly } = useContext(DefinitionContext);
  const access = useAccess();
  const [visible, setVisible] = useState(false);
  const actionRef = useRef<ActionType>();
  const [list, setList] = useState<API.SaleFaq[]>([]);
  const intl = useIntl();
  const isProduct = useMemo(() => {
    return namespace === PRODUCT;
  }, [namespace]);

  const { run: loadData, loading } = useRequest(
    async () => {
      const { data } = await listFn(isProduct ? { productId: id } : { partsId: id });
      setList(data);
    },
    { manual: true },
  );
  const columns: ProColumns<API.SaleFaq>[] = [
    {
      title: <FormattedMessage id="webOperation_product_faqSort_tableColumn_text" />,
      dataIndex: 'sort',
      width: 60,
      hideInSearch: true,
      fixed: 'left',
    },
    {
      title: <FormattedMessage id="webOperation_product_faqId_tableColumn_text" />,
      dataIndex: 'instanceId',
      width: 170,
    },
    {
      title: <FormattedMessage id="webOperation_product_faqType_tableColumn_text" />,
      dataIndex: 'typeCode',
      width: 150,
      valueEnum: {
        1: intl.formatMessage({ id: 'webOperation_dictionary_question1_select_text' }),
        2: intl.formatMessage({ id: 'webOperation_dictionary_question2_select_text' }),
        3: intl.formatMessage({ id: 'webOperation_dictionary_question3_select_text' }),
        common: intl.formatMessage({ id: 'webOperation_dictionary_common_select_text' }),
        notCommon: intl.formatMessage({
          id: 'webOperation_dictionary_notCommon_select_text',
        }),
      },
    },
    {
      title: <FormattedMessage id="webOperation_product_question_tableColumn_text" />,
      dataIndex: ['title', 'message'],
      width: 150,
      render: (_, record) => {
        return (
          <Tooltip placement="bottomLeft" title={record?.['title']?.['message']}>
            {record?.['title']?.['message']}
          </Tooltip>
        );
      },
    },
    {
      title: <FormattedMessage id="webOperation_product_questionLangId_tableColumn_text" />,
      dataIndex: ['title', 'langId'],
      width: 170,
    },
    {
      title: <FormattedMessage id="webOperation_product_solution_tableColumn_text" />,
      dataIndex: ['answer', 'message'],
      width: 150,
      render: (_, record) => {
        return (
          <Tooltip placement="bottomLeft" title={record?.['answer']?.['message']}>
            {record?.['answer']?.['message']}
          </Tooltip>
        );
      },
    },
    {
      title: <FormattedMessage id="webOperation_product_solutionLangId_tableColumn_text" />,
      dataIndex: ['answer', 'langId'],
      width: 170,
    },
  ];

  const clear = () => {
    setList([]);
    setVisible(false);
  };

  const { run: handleConfirm, loading: confirmLoading } = useRequest(
    async () => {
      await sortFn({
        productId: id,
        [`${namespace}FaqIds`]: map(list, (item) => item[`${namespace}FaqId`]!),
      });
      clear();
      refresh?.();
    },
    { manual: true },
  );

  const handleDragSortEnd = (newDataSource: API.SaleFaq[]) => {
    setList(newDataSource);
  };

  return (
    <>
      <FormContainer
        title={<FormattedMessage id="webOperation_product_sortFaq_button_text" />}
        width={700}
        onCancel={clear}
        onConfirm={handleConfirm}
        open={visible}
        destroyOnClose={true}
        loading={confirmLoading}
      >
        <DragSortTable<API.SaleFaq, API.SaleFaqPageParams>
          rowKey="instanceId"
          defaultSize="small"
          dragSortKey="sort"
          actionRef={actionRef}
          search={false}
          scroll={{ x: 150 * 6 }}
          dataSource={list}
          columns={columns}
          onDragSortEnd={handleDragSortEnd}
          onChange={loadData}
          loading={loading}
          pagination={false}
          toolBarRender={false}
          className={styles.dragSort}
        />
      </FormContainer>
      {readonly ? null : (
        <Access accessible={access.checkAuth(rbac.SORT)}>
          <Button
            onClick={() => {
              setVisible(true);
              loadData();
            }}
          >
            <FormattedMessage id="webOperation_product_sortFaq_button_text" />
          </Button>
        </Access>
      )}
    </>
  );
};

export default FaqSorter;
