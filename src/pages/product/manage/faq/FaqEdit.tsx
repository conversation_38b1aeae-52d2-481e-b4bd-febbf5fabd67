import FormContainer from '@/components/Form/FormContainer';
import { len128, len5000 } from '@/utils/validate';
import { Access } from '@@/plugin-access/access';
import { useRequest } from '@@/plugin-request/request';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Button, message } from 'antd';
import React, { useContext, useImperativeHandle, useRef, useState } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { ProFormDependency } from '@ant-design/pro-form';
import { LocaleID } from '@/components/Locale';
import { DefinitionContext } from '@/utils/context';

const FaqEdit = React.forwardRef<
  APP.RefType<API.SaleFaq>,
  APP.EditFormProps & {
    createFn: Function;
    updateFn: Function;
    namespace?: string;
    rbac: Record<string, string>;
  }
>((props, ref) => {
  const { refresh, createFn, updateFn, namespace = 'product', rbac } = props;
  const intl = useIntl();
  const formRef = useRef<ProFormInstance<API.SaleFaq>>();
  const [visible, setVisible] = useState<boolean>(false);
  const [readonly, setReadonly] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState<API.SaleFaq>({} as any);
  const access = useAccess();
  const { id, readonly: releaseMode } = useContext(DefinitionContext);

  const { run: handleSubmit, loading } = useRequest(
    async (values) => {
      if (!readonly) {
        if (initialValues.instanceId) {
          await updateFn({
            id,
            operateItemId: initialValues[`${namespace}FaqId`],
            faq: {
              answer: values.answer.message,
              answerLangId: values.answer.langId,
              title: values.title.message,
              titleLangId: values.title.langId,
              typeCode: values.typeCode,
              faqId: values.instanceId,
            },
          });
        } else {
          await createFn({
            id,
            faq: {
              answer: values.answer.message,
              title: values.title.message,
              typeCode: values.typeCode,
            },
          });
        }
        message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
        refresh?.();
      }
      setVisible(false);
    },
    { manual: true },
  );

  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    edit: (item: API.SaleFaq, r) => {
      setReadonly(!!r);
      setInitialValues(item);
      setVisible(true);
    },
  }));

  const onCreate = () => {
    setReadonly(false);
    setInitialValues({} as any);
    setVisible(true);
  };

  return (
    <>
      <FormContainer
        title={
          readonly
            ? intl.formatMessage({ id: 'webOperation_product_viewFaq_modal_title' })
            : initialValues.instanceId
            ? intl.formatMessage({ id: 'webOperation_product_editFaq_modal_title' })
            : intl.formatMessage({ id: 'webOperation_product_addFaq_button_text' })
        }
        width="50%"
        onCancel={onClose}
        onConfirm={() => formRef.current?.submit()}
        open={visible}
        destroyOnClose={true}
        loading={loading}
        hiddenConfirm={readonly}
      >
        <ProForm
          initialValues={initialValues}
          onFinish={handleSubmit}
          formRef={formRef}
          labelCol={{ flex: '150px' }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
          readonly={readonly}
        >
          {initialValues.instanceId ? (
            <ProFormText
              label={intl.formatMessage({ id: 'webOperation_product_faqId_tableColumn_text' })}
              name="instanceId"
              readonly
            />
          ) : null}

          <ProFormSelect
            name="typeCode"
            label={intl.formatMessage({
              id: 'webOperation_product_faqType_tableColumn_text',
            })}
            rules={[
              {
                required: true,
                message: intl.formatMessage({
                  id: 'webOperation_product_faqType_select_placeholder',
                }),
              },
            ]}
            placeholder={intl.formatMessage({
              id: 'webOperation_product_faqType_select_placeholder',
            })}
            valueEnum={{
              1: intl.formatMessage({ id: 'webOperation_dictionary_question1_select_text' }),
              2: intl.formatMessage({ id: 'webOperation_dictionary_question2_select_text' }),
              3: intl.formatMessage({ id: 'webOperation_dictionary_question3_select_text' }),
              common: intl.formatMessage({ id: 'webOperation_dictionary_common_select_text' }),
              notCommon: intl.formatMessage({
                id: 'webOperation_dictionary_notCommon_select_text',
              }),
            }}
          />

          <ProFormText
            name={['title', 'message']}
            label={intl.formatMessage({
              id: 'webOperation_product_question_tableColumn_text',
            })}
            rules={[
              {
                required: true,
                message: intl.formatMessage({
                  id: 'webOperation_product_question_input_placeholder',
                }),
              },
              len128,
            ]}
            extra={
              readonly ? null : (
                <ProFormDependency name={['title', 'langId']}>
                  {({ title = {} }) => <LocaleID id={title?.langId} />}
                </ProFormDependency>
              )
            }
          />

          <ProFormText
            name={['title', 'langId']}
            label={intl.formatMessage({
              id: 'webOperation_product_questionLangId_tableColumn_text',
            })}
            hidden={!readonly}
          />
          <ProFormTextArea
            name={['answer', 'message']}
            label={intl.formatMessage({
              id: 'webOperation_product_solution_tableColumn_text',
            })}
            rules={[
              {
                required: true,
                message: intl.formatMessage({
                  id: 'webOperation_product_solution_input_placeholder',
                }),
              },
              len5000,
            ]}
            extra={
              readonly ? null : (
                <ProFormDependency name={['answer', 'langId']}>
                  {({ answer = {} }) => <LocaleID id={answer?.langId} />}
                </ProFormDependency>
              )
            }
          />
          <ProFormText
            name={['answer', 'langId']}
            label={intl.formatMessage({
              id: 'webOperation_product_solutionLangId_tableColumn_text',
            })}
            hidden={!readonly}
          />
        </ProForm>
      </FormContainer>
      {releaseMode ? null : (
        <Access accessible={access.checkAuth(rbac.CREATE)}>
          <Button type="primary" onClick={onCreate}>
            <PlusOutlined />
            <FormattedMessage id="webOperation_product_addFaq_button_text" />
          </Button>
        </Access>
      )}
    </>
  );
});

export default FaqEdit;
