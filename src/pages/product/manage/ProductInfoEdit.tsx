import { useRequest } from '@@/plugin-request/request';
import {
  ProForm,
  ProFormInstance,
  ProFormFieldSet,
  ProFormRadio,
  ProFormDependency,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { message, Space } from 'antd';
import { last, split, join, slice } from 'lodash-es';
import React, { useImperativeHandle, useRef, useState } from 'react';
import { useIntl } from 'umi';
import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';
import { detailColumns } from '../columns/ProductDetail';
import { getS3File, uploadImage } from '@/services/common';
import { ImageUpload } from '@/components/Form/ImageUpload';
import { len500, urlChecked } from '@/utils/validate';
import { editProduct } from '@/services/product';
import { typeColumns } from '@/hooks/column';

type productItem = API.ProductItem;

export interface EditProps {
  refresh?: () => Promise<void> | undefined;
  brandEnum: typeColumns;
}

export interface RefType {
  edit: (item: productItem) => void;
}

const EditForm = React.forwardRef<RefType, EditProps>((props, ref) => {
  const { refresh, brandEnum } = props;
  const intl = useIntl();
  const [visible, setVisible] = useState<boolean>(false);
  const formRef = useRef<ProFormInstance<productItem>>();
  const [initialValues, setInitialValues] = useState<productItem>({});

  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    edit: (item: productItem = {}) => {
      const values = { ...item };
      if (Number(values.iconType) === 1) {
        values.iconAddress = values.productIcon;
        values.productIcon = '';
      }
      values.iconType = String(values.iconType);
      values.questionTemplate = [...new Set(['commonTemplate', ...values.questionTemplate!])];
      setInitialValues(values);
      setVisible(true);
    },
  }));

  const { run: handleSubmit, loading } = useRequest(
    async (values: any) => {
      if (Number(values.iconType) === 1) {
        const { key } = await getS3File(values.productIcon);
        values.productIcon = key;
      }
      if (Number(values.iconType) === 0 && values.productIcon.includes('http')) {
        const urlNoProtocol = split(last(split(values.productIcon, '://')), '/');
        values.productIcon = join(slice(urlNoProtocol, 1, urlNoProtocol.length), '/');
      }
      values.pid = initialValues.id;
      values.productName = {};
      // values.productFullName = {};
      values.productName.langId = initialValues.productName;
      values.productName.message = values.productNameLanguage;
      // values.productFullName.langId = initialValues.productFullName;
      // values.productFullName.message = values.productFullNameLanguage;
      delete values.productNameLanguage;
      delete values.productFullNameLanguage;
      await editProduct(values);
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setVisible(false);
      refresh?.();
    },
    { manual: true },
  );

  const setFieldValue = (key: string, value: any) => {
    const data = {};
    data[key] = value;
    formRef.current?.setFieldsValue(data);
  };

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: 'webOperation_product_edit_drawer_title' })}
        width="50%"
        onCancel={onClose}
        onConfirm={() => formRef.current?.submit()}
        open={visible}
        destroyOnClose={true}
        loading={loading}
      >
        <ProForm
          formRef={formRef}
          initialValues={initialValues}
          onFinish={handleSubmit}
          labelCol={{ span: 4 }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
        >
          <FormFields columns={detailColumns(brandEnum)} pageName="product" />
          <ProFormFieldSet
            name="productIconInfo"
            initialValue=" "
            label={intl.formatMessage({ id: 'webOperation_product_productIcon_tableColumn_text' })}
            type="group"
            rules={[
              {
                required: true,
                message:
                  intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                  intl.formatMessage({ id: 'webOperation_product_productIcon_tableColumn_text' }),
              },
            ]}
            transform={(value: any) => ({})}
          >
            <Space>
              <ProFormRadio.Group
                name="iconType"
                label={false}
                valueEnum={{
                  0: intl.formatMessage({ id: 'webOperation_dictionary_imageUpload_select_text' }),
                  1: intl.formatMessage({ id: 'webOperation_dictionary_imageLink_select_text' }),
                }}
              />
              <ProFormDependency name={['iconType']}>
                {({ iconType }) => {
                  if (iconType === '1') {
                    return (
                      <ProFormTextArea
                        rules={[
                          {
                            required: true,
                            message:
                              intl.formatMessage({
                                id: 'webCommon_page_input_common_placeholder',
                              }) +
                              intl.formatMessage({
                                id: 'webCommon_page_imageUrl_input_text',
                              }),
                          },
                          urlChecked,
                          len500,
                        ]}
                        width="lg"
                        name="iconAddress"
                        label={intl.formatMessage({
                          id: 'webCommon_page_imageUrl_input_text',
                        })}
                        placeholder={
                          intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                          intl.formatMessage({
                            id: 'webCommon_page_imageUrl_input_text',
                          })
                        }
                        transform={(value: any) => ({ productIcon: value })}
                      />
                    );
                  }
                  return (
                    <ProForm.Item
                      name="productIcon"
                      label={false}
                      rules={[
                        {
                          required: true,
                          message:
                            intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                            intl.formatMessage({
                              id: 'webOperation_product_productIcon_tableColumn_text',
                            }),
                        },
                      ]}
                      addonAfter={intl.formatMessage({
                        id: 'webCommon_page_imageSize_tip_message',
                      })}
                    >
                      <ImageUpload
                        name="productIcon"
                        maxCount={1}
                        getSignedUrl={async (fileName) => {
                          const data = await uploadImage({
                            fileName,
                            fileType: 'picture',
                          });
                          const preSignedUrl = new URL(data.preSignedUrl);
                          const domain = preSignedUrl.protocol + '//' + preSignedUrl.host + '/';
                          setFieldValue?.('productIcon', domain + data.key);
                          return data;
                        }}
                        setFieldValue={setFieldValue}
                      />
                    </ProForm.Item>
                  );
                }}
              </ProFormDependency>
            </Space>
          </ProFormFieldSet>
          <ProFormTextArea
            name="operationRemark"
            label={intl.formatMessage({
              id: 'webOperation_product_operationRemark_tableColumn_text',
            })}
            placeholder={
              intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
              intl.formatMessage({ id: 'webOperation_product_operationRemark_tableColumn_text' })
            }
            rules={[len500]}
          />
        </ProForm>
      </FormContainer>
    </>
  );
});

export default EditForm;
