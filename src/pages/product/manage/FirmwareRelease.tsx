// 框架依赖引入
import { useRef, useState } from 'react';
import { FormattedMessage, useIntl } from 'umi';
import { Spin } from 'antd';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProFormInstance } from '@ant-design/pro-components';
import { map } from 'lodash-es';
// 公共自定义依赖引入
import ResizableTable from '@/components/Table';
import Modal, { RefType as modalRefType } from '@/components/Modal/index';
import { useAction } from '@/hooks/action';
import { dateFilter, extra, typeColumns, useColumn } from '@/hooks/column';
// 页面自定义依赖引入
import FirmwareReleaseConfig, { RefType } from './FirmwareReleaseConfig';
import FirmwareReleaseDetail, { RefType as FirmwareRefType } from './FirmwareReleaseDetail';
import FirmwareTaskDetail, { RefType as TaskRefType } from './FirmwareTaskDetail';
import FirmwareUpgradeResult, { RefType as ResultRefType } from './FirmwareUpgradeResult';
import { getFirmwareList, getFirmwareReleaseDetail } from '@/services/product';
import { buttonList } from '../actions/FirmwareList';

const FirmwareRelease = ({ pid }: { pid: string }) => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const modalRef = useRef<modalRefType>(null);
  const configRef = useRef<RefType>(null);
  const firmwareRef = useRef<FirmwareRefType>(null);
  const taskRef = useRef<TaskRefType>(null);
  const resultRef = useRef<ResultRefType>(null);
  const intl = useIntl();
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const { createColumns } = useColumn();
  const { createActions } = useAction('id');

  const showGroupConfig = async (id: string) => {
    setActionLoading(true);
    try {
      const res = await getFirmwareReleaseDetail(id);
      setActionLoading(false);
      configRef.current?.edit(id, res.data);
    } catch (e) {
      setActionLoading(false);
    }
  };

  const goReleaseDetail = (id: string) => {
    firmwareRef.current?.show(id);
  };

  const goTaskDetail = (id: string) => {
    taskRef.current?.show(id);
  };

  const goUpgradeResult = (id: string) => {
    resultRef.current?.show(id);
  };

  const handleAction = async (id: string, action?: APP.FnType) => {
    setActionLoading(true);
    try {
      await action?.(id, intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setActionLoading(false);
      actionRef.current?.reload();
    } catch (e) {
      setActionLoading(false);
    }
  };

  const actionColumn: ProColumns<API.FirmwareReleaseConfig> = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 310,
    fixed: 'right',
    render: (_, record) => {
      const columns = buttonList(modalRef);
      map(columns, (item) => {
        const action = item.onClick;
        switch (item.name) {
          case 'canConfigRelease':
            item.onClick = showGroupConfig;
            break;
          case 'canViewReleaseDetail':
            item.onClick = goReleaseDetail;
            break;
          case 'canViewJobDetail':
            item.onClick = goTaskDetail;
            break;
          case 'canViewOtaResult':
            item.onClick = goUpgradeResult;
            break;
          case 'canCancelApplyRelease':
          case 'canCancelStopRelease':
          case 'canApplyStopRelease':
          case 'canNullifyApply':
          case 'canNullifyApplyCancel':
            item.onClick = (id) => handleAction(id, action);
            break;
        }
      });
      return createActions(columns, record, 'product');
    },
  };

  const listColumns: typeColumns[] = [
    ...dateFilter,
    {
      dataIndex: 'jobId',
      width: 180,
      render: (_, record) => record.id,
    },
    {
      dataIndex: 'packageCount',
      width: 150,
      hideInSearch: true,
    },
    {
      dataIndex: 'upgradeContent',
      width: 150,
      hideInSearch: true,
    },
    {
      dataIndex: 'developStatus',
      width: 150,
      valueEnum: {
        CLOSE_REFUSED: intl.formatMessage({
          id: 'webOperation_dictionary_closeRefused_select_text',
        }),
        DEVELOPING: intl.formatMessage({
          id: 'webOperation_dictionary_develoging_select_text',
        }),
        CLOSING: intl.formatMessage({
          id: 'webOperation_dictionary_closing_select_text',
        }),
        CLOSED: intl.formatMessage({
          id: 'webOperation_dictionary_closed_select_text',
        }),
      },
    },
    {
      dataIndex: 'releaseStatus',
      width: 150,
      valueEnum: {
        RELEASE_REFUSED: intl.formatMessage({
          id: 'webOperation_dictionary_releaseRejected_select_text',
        }),
        NULLIFY_REJECTED: intl.formatMessage({
          id: 'webOperation_dictionary_nullifyRejected_select_text',
        }),
        NULLIFIED: intl.formatMessage({
          id: 'webOperation_dictionary_nullified_select_text',
        }),
        RELEASE_TEST_REFUSED: intl.formatMessage({
          id: 'webOperation_dictionary_testRejected_select_text',
        }),
        RELEASING: intl.formatMessage({
          id: 'webOperation_dictionary_releasing_select_text',
        }),
        STOP_REFUSED: intl.formatMessage({
          id: 'webOperation_dictionary_stopRefused_select_text',
        }),
        OVER: intl.formatMessage({
          id: 'webOperation_dictionary_over_select_text',
        }),
        RELEASE_READY: intl.formatMessage({
          id: 'webOperation_dictionary_releaseReady_select_text',
        }),
        TESTING: intl.formatMessage({
          id: 'webOperation_dictionary_testingAndVerification_select_text',
        }),
        INIT: intl.formatMessage({
          id: 'webOperation_dictionary_init_select_text',
        }),
        RELEASED: intl.formatMessage({
          id: 'webOperation_dictionary_published_select_text',
        }),
        STOPPED: intl.formatMessage({
          id: 'webOperation_dictionary_stopped_select_text',
        }),
        NULLIFY_IN_REVIEW: intl.formatMessage({
          id: 'webOperation_dictionary_nullifyInReview_select_text',
        }),
        RELEASE_WAITING: intl.formatMessage({
          id: 'webOperation_dictionary_releaseWaiting_select_text',
        }),
        STOPPING: intl.formatMessage({
          id: 'webOperation_dictionary_stopping_select_text',
        }),
      },
    },
    ...extra,
    {
      dataIndex: 'description',
      langCode: 'webCommon_page_remark_common_text',
      width: 150,
      hideInSearch: true,
    },
  ];
  const initColumns = [...listColumns, actionColumn];
  const allColumns = createColumns('firmware', initColumns);

  return (
    <>
      <Spin spinning={actionLoading}>
        <ResizableTable<API.FirmwareReleaseConfig, API.FirmwareReleaseConfigPageParams>
          actionRef={actionRef}
          formRef={formRef}
          rowKey="id"
          defaultSize="small"
          search={{
            labelWidth: 'auto',
          }}
          scroll={{ x: 1300 }}
          params={{ productId: pid }}
          request={getFirmwareList}
          columns={allColumns}
        />
      </Spin>
      <FirmwareReleaseConfig
        ref={configRef}
        key="config"
        refresh={() => {
          return actionRef.current?.reload();
        }}
      />
      <Modal ref={modalRef} parentRef={actionRef} />
      <FirmwareReleaseDetail ref={firmwareRef} key="detail" />
      <FirmwareTaskDetail ref={taskRef} key="taks" />
      <FirmwareUpgradeResult ref={resultRef} key="result" />
    </>
  );
};
export default FirmwareRelease;
