import ResizableTable from '@/components/Table';
import PartDetail from './PartDetail';
import { useIntl } from '@@/plugin-locale/localeExports';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { message, Space as AntSpace } from 'antd';
import React, { useRef } from 'react';
import { FormattedMessage } from 'umi';
import styles from '@/pages/part/part.less';
import Space from '@/components/Space';
import { AccessLink } from '@/components/Button/AccessButton';
import PartSorter from '@/pages/product/manage/part/PartSorter';
import PartPicker from '@/pages/product/manage/part/PartPicker';
import { deleteProductPart, listProductPart } from '@/services/product';
import { rbac } from '@/access';

const PartList: React.FC<{ detail: API.ProductItem }> = (props) => {
  const { detail = {} } = props;
  const actionRef = useRef<ActionType>();
  const editRef = useRef<APP.RefType<API.PartItem>>(null);
  const intl = useIntl();

  const columns: ProColumns<API.PartItem>[] = [
    {
      title: <FormattedMessage id="webOperation_part_id_tableColumn_text" />,
      dataIndex: ['parts', 'partsId'],
      fixed: 'left',
      hideInSearch: true,
      width: 170,
    },
    {
      title: <FormattedMessage id="webOperation_part_commodityModel_tableColumn_text" />,
      dataIndex: ['parts', 'commodityModel'],
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_part_name_tableColumn_text" />,
      dataIndex: ['parts', 'name'],
      width: 150,
    },
    {
      title: <FormattedMessage id="webOperation_part_partsIcon_tableColumn_text" />,
      dataIndex: ['parts', 'iconUrl'],
      width: 150,
      hideInSearch: true,
      render: (_, record) =>
        record.parts?.iconUrl ? (
          <img alt="" src={record.parts?.iconUrl as string} className={styles.partIcon} />
        ) : (
          _
        ),
    },
    {
      title: <FormattedMessage id="webOperation_part_type1_tableColumn_text" />,
      dataIndex: ['parts', 'type1'],
      width: 150,
      valueEnum: {
        battery_pack_charger: intl.formatMessage({
          id: 'webOperation_dictionary_batteryCharger_select_text',
        }),
        common_parts_type: intl.formatMessage({
          id: 'webOperation_dictionary_generalAccessories_select_text',
        }),
        special_parts_type: intl.formatMessage({
          id: 'webOperation_dictionary_specializedAccessories_select_text',
        }),
      },
    },
    {
      title: <FormattedMessage id="webOperation_part_type2_tableColumn_text" />,
      dataIndex: ['parts', 'type2'],
      width: 150,
      valueEnum: {
        battery_pack_charger: intl.formatMessage({
          id: 'webOperation_dictionary_batteryCharger_select_text',
        }),
        common_parts_type: intl.formatMessage({
          id: 'webOperation_dictionary_generalAccessories_select_text',
        }),
        special_parts_type: intl.formatMessage({
          id: 'webOperation_dictionary_specializedAccessories_select_text',
        }),
      },
    },
    {
      title: <FormattedMessage id="webOperation_part_maintenanceType_tableColumn_text" />,
      dataIndex: 'maintenanceType',
      width: 150,
      valueEnum: {
        1: intl.formatMessage({
          id: 'webOperation_dictionary_naturalDay_select_text',
        }),
        2: intl.formatMessage({
          id: 'webOperation_dictionary_useWorkingHours_select_text',
        }),
      },
    },
    {
      title: <FormattedMessage id="webOperation_part_warranty_tableColumn_text" />,
      dataIndex: 'maintenancePeriod',
      width: 150,
      hideInSearch: true,
      render: (_, record) => {
        const maintenanceType =
          record.maintenanceType === 1 ? (
            <FormattedMessage id="webOperation_part_warrantyUnit_tableColumn_text" />
          ) : (
            <FormattedMessage id="webOperation_part_warrantyUnitHour_tableColumn_text" />
          );
        return record.maintenancePeriod ? (
          <>
            {record.maintenancePeriod}
            {maintenanceType}
          </>
        ) : (
          _
        );
      },
    },
    {
      title: <FormattedMessage id="webOperation_part_warrantyRemindFrequency_tableColumn_text" />,
      dataIndex: 'maintenanceRemind',
      width: 150,
      hideInSearch: true,
      render: (_, record) => {
        const maintenanceType =
          record.maintenanceType === 1 ? (
            <FormattedMessage id="webOperation_part_warrantyUnit_tableColumn_text" />
          ) : (
            <FormattedMessage id="webOperation_part_warrantyUnitHour_tableColumn_text" />
          );
        return record.maintenanceRemind ? (
          <>
            {record.maintenanceRemind}
            {maintenanceType}
          </>
        ) : (
          _
        );
      },
    },
    {
      title: <FormattedMessage id="webOperation_part_option_tableColumn_text" />,
      dataIndex: 'option',
      valueType: 'option',
      width: detail.readonly ? 50 : 200,
      fixed: 'right',
      render: (_: any, record: API.PartItem) =>
        detail.readonly ? (
          <AccessLink
            text="webCommon_page_detail_common_text"
            code={rbac.PRODUCT_PART.VIEW}
            onClick={() => {
              editRef.current?.edit(record, true);
            }}
          />
        ) : (
          <Space>
            <AccessLink
              text="webCommon_page_detail_common_text"
              code={rbac.PRODUCT_PART.VIEW}
              onClick={() => {
                editRef.current?.edit(record, true);
              }}
            />
            <AccessLink
              text="webCommon_page_edit_common_text"
              code={rbac.PRODUCT_PART.UPDATE}
              onClick={() => {
                editRef.current?.edit(record);
              }}
            />
            <AccessLink
              text="webCommon_page_delete_tableButton_linkText"
              code={rbac.PRODUCT_PART.DELETE}
              modal={{
                content: <FormattedMessage id="webOperation_part_delete_modal_message" />,
              }}
              onClick={async () => {
                await deleteProductPart({ req: record.instanceId! });
                message.success(
                  intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }),
                );
                actionRef.current?.reload();
              }}
            />
          </Space>
        ),
    },
  ];

  return (
    <>
      <ResizableTable<API.PartItem, API.PartPageParams>
        actionRef={actionRef}
        rowKey="instanceId"
        defaultSize="small"
        search={false}
        scroll={{ x: 150 * 13 + 100 }}
        headerTitle={
          detail.readonly ? (
            false
          ) : (
            <AntSpace>
              <PartPicker key="picker" refresh={() => actionRef.current?.reload()} />
              <PartSorter key="sort" refresh={() => actionRef.current?.reload()} />
            </AntSpace>
          )
        }
        request={(queries) => listProductPart({ ...queries, productId: detail.id! })}
        columns={columns}
      />
      <PartDetail
        ref={editRef}
        key="edit"
        productId={detail.id!}
        refresh={() => actionRef.current?.reload()}
      />
    </>
  );
};

export default PartList;
