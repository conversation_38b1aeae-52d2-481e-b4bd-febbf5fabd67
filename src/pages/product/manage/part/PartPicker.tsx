import React, { useImperative<PERSON><PERSON>le, useState, useContext, useEffect } from 'react';
import { Button, message, Modal } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { FormattedMessage } from '@@/plugin-locale/localeExports';
import { useRequest } from '@@/plugin-request/request';
import { PartList } from '@/pages/part/PartList';
import './index.less';
import { createProductPart, disabledProductPart } from '@/services/product';
import { useAccess, useIntl } from 'umi';
import { Access } from '@@/plugin-access/access';
import { DefinitionContext } from '@/utils/context';

const PartPicker = React.forwardRef<APP.RefType<API.PartItem>, APP.EditFormProps>((props, ref) => {
  const { refresh } = props;
  const access = useAccess();
  const [visible, setVisible] = useState<boolean>(false);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const { id: productId } = useContext(DefinitionContext);
  const intl = useIntl();
  const { run: handleSubmit, loading: confirmLoading } = useRequest(
    async () => {
      if (selectedKeys.length <= 0) {
        message.error(intl.formatMessage({ id: 'webCommon_page_limit_select_placeholder' }));
        return;
      }

      await createProductPart({
        productId,
        partsId: selectedKeys as string[],
      });
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setVisible(false);
      refresh?.();
    },
    { manual: true },
  );

  const { data: disabledKeys, run } = useRequest(
    async () => {
      return await disabledProductPart(productId);
    },
    { manual: true },
  );

  useEffect(() => {
    if (visible) run();
  }, [run, visible]);

  useImperativeHandle(ref, () => ({
    edit: (item: API.PartItem) => {
      setVisible(true);
    },
  }));

  return (
    <>
      <Modal
        title={<FormattedMessage id="webOperation_part_add_button_text" />}
        width="50%"
        onCancel={() => setVisible(false)}
        onOk={handleSubmit}
        open={visible}
        destroyOnClose={true}
        confirmLoading={confirmLoading}
      >
        <PartList
          selectable
          selectedKeys={selectedKeys}
          onSelect={(v) => setSelectedKeys(v)}
          disabledKeys={disabledKeys}
        />
      </Modal>
      <Access accessible={access.canCreateProductPart()}>
        <Button
          type="primary"
          onClick={() => {
            setSelectedKeys([]);
            setVisible(true);
          }}
        >
          <PlusOutlined />
          <FormattedMessage id="webOperation_part_add_button_text" />
        </Button>
      </Access>
    </>
  );
});

export default PartPicker;
