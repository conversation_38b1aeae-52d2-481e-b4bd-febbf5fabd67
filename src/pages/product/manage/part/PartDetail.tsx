import FormContainer from '@/components/Form/FormContainer';
import { useRequest } from '@@/plugin-request/request';
import {
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormText,
  ProFormFieldSet,
  ProFormDependency,
  ProFormSelect,
} from '@ant-design/pro-components';
import { message, Space } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import { useIntl } from 'umi';
import { updateProductPart, viewProductPart } from '@/services/product';
import { get, pick } from 'lodash-es';

interface PartProps {
  productId: string;
  refresh?: () => Promise<void> | undefined;
}

const PartDetail = React.forwardRef<APP.RefType<API.PartItem>, PartProps>((props, ref) => {
  const { refresh } = props;
  const intl = useIntl();
  const formRef = useRef<ProFormInstance<API.PartItem>>();
  const [visible, setVisible] = useState<boolean>(false);
  const [warrantyUnit, setWarrantyUnit] = useState<string>('');
  const [initialValues, setInitialValues] = useState<API.PartItem>({} as any);
  const [readonly, setReadonly] = useState(false);
  const { run: handleSubmit, loading } = useRequest(
    async (values) => {
      await updateProductPart(
        pick(values, ['maintenancePeriod', 'instanceId', 'maintenanceType', 'maintenanceRemind']),
      );
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setVisible(false);
      refresh?.();
    },
    { manual: true },
  );

  const onClose = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    edit: (item: API.PartItem, r) => {
      setInitialValues({} as any);
      setVisible(true);
      setReadonly(!!r);
      viewProductPart({
        req: get(item, ['instanceId'], ''),
      }).then(({ data }) => {
        data.maintenanceType = data.maintenanceType ? String(data.maintenanceType) : undefined;
        formRef.current?.setFieldsValue(data);
        setInitialValues(data);
      });
    },
  }));

  return (
    <FormContainer
      title={
        readonly
          ? intl.formatMessage({ id: 'webOperation_part_view_drawer_title' })
          : intl.formatMessage({ id: 'webOperation_part_edit_drawer_title' })
      }
      width="50%"
      onCancel={onClose}
      onConfirm={() => formRef.current?.submit()}
      open={visible}
      destroyOnClose={true}
      loading={loading}
      hiddenConfirm={readonly}
    >
      <ProForm
        initialValues={initialValues}
        onFinish={handleSubmit}
        formRef={formRef}
        labelCol={{ flex: '150px' }}
        layout="horizontal"
        onReset={onClose}
        submitter={false}
        readonly={readonly}
      >
        <ProFormText name="instanceId" hidden />
        <ProFormText
          name={['parts', 'commodityModel']}
          readonly={true}
          label={intl.formatMessage({ id: 'webOperation_part_model_tableColumn_text' })}
        />
        <ProFormText
          name={['parts', 'name']}
          readonly={true}
          label={intl.formatMessage({ id: 'webOperation_part_name_tableColumn_text' })}
        />
        <ProFormFieldSet
          name="maintenanceInfo"
          initialValue=" "
          label={intl.formatMessage({ id: 'webOperation_part_warranty_tableColumn_text' })}
          type="group"
          transform={(value: any) => ({})}
        >
          <Space>
            <ProFormSelect
              name="maintenanceType"
              label={false}
              valueEnum={{
                1: intl.formatMessage({
                  id: 'webOperation_dictionary_naturalDay_select_text',
                }),
                2: intl.formatMessage({
                  id: 'webOperation_dictionary_useWorkingHours_select_text',
                }),
              }}
            />
            <ProFormDependency name={['maintenanceType']}>
              {({ maintenanceType }) => {
                if (maintenanceType === '1') {
                  setWarrantyUnit(
                    intl.formatMessage({ id: 'webOperation_part_warrantyUnit_tableColumn_text' }),
                  );
                } else if (maintenanceType === '2') {
                  setWarrantyUnit(
                    intl.formatMessage({
                      id: 'webOperation_part_warrantyUnitHour_tableColumn_text',
                    }),
                  );
                } else {
                  setWarrantyUnit('');
                }
              }}
            </ProFormDependency>
            <ProFormDigit
              name="maintenancePeriod"
              label={false}
              min={1}
              fieldProps={{ precision: 0 }}
              addonAfter={warrantyUnit}
            />
          </Space>
        </ProFormFieldSet>
        <ProFormDigit
          name="maintenanceRemind"
          label={intl.formatMessage({
            id: 'webOperation_part_warrantyRemindFrequency_tableColumn_text',
          })}
          min={1}
          fieldProps={{ precision: 0 }}
          addonAfter={warrantyUnit}
        />
      </ProForm>
    </FormContainer>
  );
});

export default PartDetail;
