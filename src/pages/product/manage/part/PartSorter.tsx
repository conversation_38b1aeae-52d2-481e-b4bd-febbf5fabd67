import { ActionType, DragSortTable, ProColumns } from '@ant-design/pro-components';
import React, { useRef, useState, useContext } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import styles from '@/pages/part/part.less';
import { useRequest } from '@@/plugin-request/request';
import { map } from 'lodash-es';
import { Button, Tooltip } from 'antd';
import FormContainer from '@/components/Form/FormContainer';
import { listSortableProductPart, sortProductPart } from '@/services/product';
import { Access } from '@@/plugin-access/access';
import { DefinitionContext } from '@/utils/context';

export interface SortTableProps {
  value?: API.PartItem[];
  refresh?: () => void;
}

export interface RefType {
  open: () => void;
}

const PartSorter = React.forwardRef<RefType, SortTableProps>((props, ref) => {
  const { refresh } = props;
  const access = useAccess();
  const actionRef = useRef<ActionType>();
  const [list, setList] = useState<API.PartItem[]>([]);
  const [visible, setVisible] = useState(false);

  const { id: productId } = useContext(DefinitionContext);
  const intl = useIntl();
  const { run: loadData, loading } = useRequest(
    async () => {
      const { data } = await listSortableProductPart({ req: productId });
      setList(data);
    },
    { manual: true },
  );

  const clear = () => {
    setList([]);
    setVisible(false);
  };

  const { run: handleConfirm, loading: confirmLoading } = useRequest(
    async () => {
      await sortProductPart(map(list, (item) => item.instanceId!));
      clear();
      refresh?.();
    },
    { manual: true },
  );

  const columns: ProColumns<API.PartItem>[] = [
    {
      title: <FormattedMessage id="webOperation_part_sort_tableColumn_text" />,
      dataIndex: 'sort',
      width: 60,
      hideInSearch: true,
      fixed: 'left',
    },
    {
      title: <FormattedMessage id="webOperation_part_id_tableColumn_text" />,
      dataIndex: ['parts', 'partsId'],
      hideInSearch: true,
      width: 200,
    },
    {
      title: <FormattedMessage id="webOperation_part_commodityModel_tableColumn_text" />,
      dataIndex: ['parts', 'commodityModel'],
      width: 150,
      render: (_, record) => {
        return (
          <Tooltip placement="bottomLeft" title={record?.['parts']?.['commodityModel']}>
            {record?.['parts']?.['commodityModel']}
          </Tooltip>
        );
      },
    },
    {
      title: <FormattedMessage id="webOperation_part_name_tableColumn_text" />,
      dataIndex: ['parts', 'name'],
      width: 150,
      render: (_, record) => {
        return (
          <Tooltip placement="bottomLeft" title={record?.['parts']?.['name']}>
            {record?.['parts']?.['name']}
          </Tooltip>
        );
      },
    },
    {
      title: <FormattedMessage id="webOperation_part_partsIcon_tableColumn_text" />,
      dataIndex: ['parts', 'iconUrl'],
      width: 150,
      hideInSearch: true,
      render: (url) =>
        url ? <img alt="" src={url as string} className={styles.partIcon} /> : null,
    },
    {
      title: <FormattedMessage id="webOperation_part_type1_tableColumn_text" />,
      dataIndex: ['parts', 'type1'],
      width: 150,
      valueEnum: {
        battery_pack_charger: intl.formatMessage({
          id: 'webOperation_dictionary_batteryCharger_select_text',
        }),
        common_parts_type: intl.formatMessage({
          id: 'webOperation_dictionary_generalAccessories_select_text',
        }),
        special_parts_type: intl.formatMessage({
          id: 'webOperation_dictionary_specializedAccessories_select_text',
        }),
      },
    },
    {
      title: <FormattedMessage id="webOperation_part_type2_tableColumn_text" />,
      dataIndex: ['parts', 'type2'],
      width: 150,
      valueEnum: {
        battery_pack_charger: intl.formatMessage({
          id: 'webOperation_dictionary_batteryCharger_select_text',
        }),
        common_parts_type: intl.formatMessage({
          id: 'webOperation_dictionary_generalAccessories_select_text',
        }),
        special_parts_type: intl.formatMessage({
          id: 'webOperation_dictionary_specializedAccessories_select_text',
        }),
      },
    },
  ];

  const handleDragSortEnd = (newDataSource: API.PartItem[]) => {
    setList(newDataSource);
  };

  return (
    <>
      <FormContainer
        title={<FormattedMessage id="webOperation_part_sort_drawer_title" />}
        width={700}
        onCancel={clear}
        onConfirm={handleConfirm}
        open={visible}
        destroyOnClose={true}
        loading={confirmLoading}
      >
        <DragSortTable<API.PartItem, API.PartPageParams>
          rowKey="instanceId"
          defaultSize="small"
          dragSortKey="sort"
          actionRef={actionRef}
          search={false}
          scroll={{ x: 150 * 6 + 50 + 60 }}
          dataSource={list}
          columns={columns}
          onDragSortEnd={handleDragSortEnd}
          onChange={loadData}
          loading={loading}
          pagination={false}
          toolBarRender={false}
          className={styles.dragSort}
        />
      </FormContainer>
      <Access accessible={access.canSortProductPart()}>
        <Button
          onClick={() => {
            setVisible(true);
            loadData();
          }}
        >
          <FormattedMessage id="webOperation_part_sort_button_text" />
        </Button>
      </Access>
    </>
  );
});

export default PartSorter;
