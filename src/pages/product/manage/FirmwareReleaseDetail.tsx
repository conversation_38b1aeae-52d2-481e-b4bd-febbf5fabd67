import { FormattedMessage } from '@@/plugin-locale/localeExports';
import React, { useState, useImperativeHandle } from 'react';
import { useAccess, useIntl } from 'umi';
import type { ProColumns } from '@ant-design/pro-components';
import { Card, Col, Row, Spin, Tooltip, message } from 'antd';
import { map } from 'lodash-es';
import { Access } from '@@/plugin-access/access';
import dayjs from 'dayjs';
import copy from 'copy-to-clipboard';

import FormContainer from '@/components/Form/FormContainer';
import ResizableTable from '@/components/Table';
import { typeColumns, useColumn } from '@/hooks/column';
import { downloadByUrl } from '@/utils/download';
import styles from '@/pages/product/styles.less';
import { getFirmwareReleaseDetail, downloadFirmware } from '@/services/product';

const ReleaseInfo = ({
  detail,
  endInfo,
}: {
  detail: API.FirmwareReleaseDetail;
  endInfo: API.selectOptions;
}) => {
  const intl = useIntl();
  const copyTechOtaVersion = () => {
    copy(detail.technologyVersion!);
    message.success(intl.formatMessage({ id: 'webCommon_page_copySuccessed_toast_text' }));
  };

  const productData = [
    {
      label: <FormattedMessage id="webOperation_product_id_tableColumn_text" />,
      value: detail.productId,
    },
    {
      label: <FormattedMessage id="webOperation_product_categoryId_tableColumn_text" />,
      value: detail.categoryName,
    },
    {
      label: <FormattedMessage id="webOperation_product_model_tableColumn_text" />,
      value: detail.productModel,
    },
    {
      label: <FormattedMessage id="webOperation_product_brandId_tableColumn_text" />,
      value: detail.brandName,
    },
    {
      label: <FormattedMessage id="webOperation_product_commodityModel_tableColumn_text" />,
      value: detail.commodityModel,
    },
    {
      label: <FormattedMessage id="webOperation_product_technologyVersion_tableColumn_text" />,
      value: (
        <div className={styles.flex_center}>
          <Tooltip placement="topLeft" title={detail?.technologyVersion}>
            <span className={styles.copy_content}>{detail?.technologyVersion}</span>
          </Tooltip>
          {detail?.technologyVersion ? (
            <a key="copy" onClick={copyTechOtaVersion}>
              <FormattedMessage id="webCommon_page_copy_button_linkText" />
            </a>
          ) : null}
        </div>
      ),
    },
  ];

  const firmwareData = [
    {
      label: <FormattedMessage id="webOperation_firmware_jobId_tableColumn_text" />,
      value: detail.jobId,
    },
    {
      label: <FormattedMessage id="webOperation_firmware_packageCount_tableColumn_text" />,
      value: detail.packageCount,
    },
    {
      label: <FormattedMessage id="webOperation_product_customVersion_tableColumn_text" />,
      value: detail.customVersion ? detail.customVersion : '--',
    },
  ];

  const configData = [
    {
      label: <FormattedMessage id="webOperation_firmware_releaseContent_tableColumn_text" />,
      value: detail.releaseContent,
    },
    {
      label: <FormattedMessage id="webCommon_page_langId_common_text" />,
      value: detail.langId,
    },
    {
      label: <FormattedMessage id="webOperation_release_prdGroup_tableColumn_text" />,
      value: detail.productGroupNames?.join(','),
    },
    {
      label: <FormattedMessage id="webOperation_release_testGroup_tableColumn_text" />,
      value: detail.testGroupNames?.join(','),
    },
    {
      label: <FormattedMessage id="webOperation_release_start_tableColumn_text" />,
      value: detail.startTime
        ? dayjs(detail.startTime as number).format('YYYY-MM-DD HH:mm:ss')
        : null,
    },
    {
      label: <FormattedMessage id="webOperation_release_end_tableColumn_text" />,
      value: detail.productId
        ? detail.endTime
          ? dayjs(detail.endTime as number).format('YYYY-MM-DD HH:mm:ss')
          : endInfo?.label
        : null,
    },
  ];

  return (
    <>
      <div className={styles.iot_ant_detail_wrapper}>
        <Card>
          <Row gutter={16}>
            {map(productData, (item, index) => {
              return (
                <Col span={8} key={index} className={styles.iot_ant_detail_col}>
                  <span>{item.label}</span>：
                  <span className={styles.iot_ant_detail_value}>{item.value}</span>
                </Col>
              );
            })}
          </Row>
          <Row gutter={16}>
            {map(firmwareData, (item, index) => {
              if(item.label.props.id !== 'webOperation_product_customVersion_tableColumn_text') {
                return (
                  <Col span={8} key={index} className={styles.iot_ant_detail_col}>
                    <span>{item.label}</span>：
                    <span className={styles.iot_ant_detail_value}>{item.value}</span>
                  </Col>
                );
              }
              else if (productData[2].value !== '51001' || '51003' || '51004' || '51005' || '51006'){
                console.log(productData[2])
                return (
                  <Col span={8} key={index} className={styles.iot_ant_detail_col}>
                    <span>{item.label}</span>：
                    <span className={styles.iot_ant_detail_value}>{item.value}</span>
                  </Col>
                )
              }
            })}
          </Row>
        </Card>
      </div>
      <div className={styles.iot_ant_detail_wrapper}>
        <Card>
          <Row gutter={16}>
            {map(configData, (item, index) => {
              return (
                <Col span={8} key={index} className={styles.iot_ant_detail_col}>
                  <span>{item.label}</span>：
                  <span className={styles.iot_ant_detail_value}>{item.value}</span>
                </Col>
              );
            })}
          </Row>
        </Card>
      </div>
    </>
  );
};

export interface DetailProps {
  dict: { [key: string]: any };
}

export interface RefType {
  show: (id: string) => void;
}

const FirmwareReleaseDetail = React.forwardRef<RefType>((props, ref) => {
  const [detail, setDetail] = useState<API.FirmwareReleaseDetail>({} as any);
  const [firmwareList, setFirmwareList] = useState<API.firmwareItem[]>([]);
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const [visible, setVisible] = useState<boolean>(false);
  const { createColumns } = useColumn();
  const access = useAccess();
  const intl = useIntl();

  const actionColumn: ProColumns = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 100,
    fixed: 'right',
    render: (_, record) => (
      <Access accessible={access.canDownloadOtaconfig()}>
        <a
          key="download"
          onClick={async () => {
            setActionLoading(true);
            try {
              const res = await downloadFirmware(record.key);
              if (res) {
                downloadByUrl(res);
              }
              setActionLoading(false);
            } catch (e) {
              setActionLoading(false);
            }
          }}
        >
          <FormattedMessage id="webCommon_page_download_common_linkText" />
        </a>
      </Access>
    ),
  };
  const onClose = () => {
    setVisible(false);
  };
  useImperativeHandle(ref, () => ({
    show: (id: string) => {
      setActionLoading(true);
      setDetail({});
      setFirmwareList([]);
      getFirmwareReleaseDetail(id).then((res) => {
        setDetail(res.data);
        setFirmwareList(res.data?.firmwares);
        setActionLoading(false);
      });
      setVisible(true);
    },
  }));
  const releaseColumns: typeColumns[] = [
    {
      dataIndex: 'componentNo',
      width: 150,
    },
    {
      dataIndex: 'componentName',
      width: 150,
    },
    {
      dataIndex: 'componentType',
      width: 150,
      valueEnum: {
        completeMachine: intl.formatMessage({
          id: 'webOperation_dictionary_completeMachine_select_text',
        }),
        bleModule: intl.formatMessage({
          id: 'webOperation_dictionary_bleModule_select_text',
        }),
        '4gModule': intl.formatMessage({
          id: 'webOperation_dictionary_4gModule_select_text',
        }),
        其他测试专用: intl.formatMessage({
          id: 'webOperation_dictionary_testOnly_select_text',
        }),
        wifiModule: intl.formatMessage({
          id: 'webOperation_dictionary_wifiModule_select_text',
        }),
        subDevice: intl.formatMessage({
          id: 'webOperation_dictionary_subDevice_select_text',
        }),
        mcu: intl.formatMessage({ id: 'webOperation_dictionary_mcu_select_text' }),
      },
    },
    {
      dataIndex: 'packageName',
      width: 150,
    },
    {
      dataIndex: 'packageVersion',
      width: 150,
    },
    {
      dataIndex: 'minimumVersion',
      width: 150,
    },
    {
      dataIndex: 'size',
      width: 150,
      render: (_, record) => Math.floor((record.size || 0) / 1024) + 'KB',
    },
    {
      dataIndex: 'packageType',
      width: 150,
      valueEnum: {
        DELTA_PACKAGE: intl.formatMessage({
          id: 'webOperation_dictionary_deltaPackage_select_text',
        }),
        FULL_PACKAGE: intl.formatMessage({
          id: 'webOperation_dictionary_fullPackage_select_text',
        }),
      },
    },
  ];
  const allColumns = createColumns('firmware', [...releaseColumns, actionColumn]);

  return (
    <FormContainer
      title={intl.formatMessage({ id: 'webOperation_firmware_releaseDetail_common_text' })}
      width="80%"
      onCancel={onClose}
      hiddenConfirm={true}
      open={visible}
      destroyOnClose={true}
    >
      <Spin spinning={actionLoading}>
        <ReleaseInfo
          detail={detail}
          endInfo={{
            label: intl.formatMessage({ id: 'webOperation_dictionary_neverExpires_select_text' }),
            value: '1',
          }}
        />

        <ResizableTable<API.firmwareItem, API.PageParams>
          rowKey="componentNo"
          defaultSize="small"
          search={false}
          options={false}
          pagination={false}
          scroll={{ x: 1300 }}
          dataSource={firmwareList}
          columns={allColumns}
        />
      </Spin>
    </FormContainer>
  );
});

export default FirmwareReleaseDetail;
