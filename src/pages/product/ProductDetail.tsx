// 框架依赖引入
import React, { useState, useImperativeHandle, useMemo } from 'react';
import { useIntl } from 'umi';
import { Card, Col, Row, Tabs, Tooltip, Button } from 'antd';
import { FormattedMessage } from '@@/plugin-locale/localeExports';
import { map } from 'lodash-es';
// 公共自定义依赖引入
import { useCategories } from '@/hooks/selectHooks';
import FormContainer from '@/components/Form/FormContainer';
import { DefinitionContext } from '@/utils/context';
// 页面自定义依赖引入
import ProductInfo from '@/pages/product/manage/ProductInfo';
import IntroductionDetail from '@/pages/product/manage/IntroductionDetail';
import DeviceProtocol from '@/pages/product/manage/protocol/DeviceProtocol';
import PartList from '@/pages/product/manage/part/PartList';
import MultiList from '@/pages/product/manage/multicode/MultiList';
import FirmwareRelease from '@/pages/product/manage/FirmwareRelease';

import styles from '@/pages/product/styles.less';
import Faq from '@/pages/product/manage/faq/FaqDetail';

const { TabPane } = Tabs;

const BasicInfo = ({ detail }: { detail: API.ProductItem }) => {
  const { list: categoryList } = useCategories();
  const intl = useIntl();

  const networkModesEnums = {
    notNetworked: intl.formatMessage({
      id: 'webOperation_dictionary_notNetworked_select_text',
    }),
    wifi: intl.formatMessage({
      id: 'webOperation_dictionary_wifi_select_text',
    }),
    '4G': intl.formatMessage({
      id: 'webOperation_dictionary_4G_select_text',
    }),
    '4GAndBle': intl.formatMessage({
      id: 'webOperation_dictionary_4GAndBle_select_text',
    }),
    lan: intl.formatMessage({
      id: 'webOperation_dictionary_lan_select_text',
    }),
    ble: intl.formatMessage({
      id: 'webOperation_dictionary_ble_select_text',
    }),
    wifiAndBle: intl.formatMessage({
      id: 'webOperation_dictionary_wifiAndBle_select_text',
    }),
    DOrT: intl.formatMessage({
      id: 'webOperation_dictionary_dOrT_select_text',
    }),
  };
  const deviceTypeEnums = {
    gatewaySubDevice: intl.formatMessage({
      id: 'webOperation_dictionary_gatewaySubDevice_select_text',
    }),
    oldIotDevice: intl.formatMessage({
      id: 'webOperation_dictionary_oldIotDevice_select_text',
    }),
    directConnectedDevice: intl.formatMessage({
      id: 'webOperation_dictionary_directConnectedDevice_select_text',
    }),
    gatewayDevice: intl.formatMessage({
      id: 'webOperation_dictionary_gatewayDevice_select_text',
    }),
    notIotDevice: intl.formatMessage({
      id: 'webOperation_dictionary_notIotDevice_select_text',
    }),
  };

  const data = [
    {
      label: <FormattedMessage id="webOperation_product_id_tableColumn_text" />,
      value: detail.id,
    },
    {
      label: <FormattedMessage id="webOperation_product_categoryId_tableColumn_text" />,
      value: map(categoryList, (item) => {
        if (item.value === String(detail.categoryId)) {
          return item.label;
        } else {
          return '';
        }
      }),
    },
    {
      label: <FormattedMessage id="webOperation_product_model_tableColumn_text" />,
      value: detail.model,
    },
    {
      label: <FormattedMessage id="webOperation_product_productType_tableColumn_text" />,
      value: deviceTypeEnums[detail.productType!],
    },
    {
      label: <FormattedMessage id="webOperation_product_networkModes_tableColumn_text" />,
      value: map(detail.networkModes, (item) => networkModesEnums[item]).join(', '),
    },
    {
      label: <FormattedMessage id="webOperation_product_commodityModel_tableColumn_text" />,
      value: detail.commodityModel,
    },
  ];

  return (
    <div className={styles.iot_ant_detail_wrapper}>
      <Card>
        <Row gutter={16}>
          {map(data, (item, index) => {
            return (
              <Col span={8} key={index} className={styles.iot_ant_detail_col}>
                <span>{item.label}</span>：
                <span className={styles.iot_ant_detail_value}>
                  <Tooltip placement="topLeft" title={item.value}>
                    {item.value}
                  </Tooltip>
                </span>
              </Col>
            );
          })}
        </Row>
      </Card>
    </div>
  );
};

export interface RefType {
  open: (record: API.ProductItem, readonly?: boolean) => void;
}

interface Props {
  refresh?: () => void;
}
const ProductDetail = React.forwardRef<RefType, Props>((props, ref) => {
  const [detail, setDetail] = useState<API.ProductItem>({} as any);
  const { refresh } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const intl = useIntl();
  const DefinitionContextProps = useMemo(() => {
    return {
      id: detail.id!,
      readonly: detail.readonly!,
    };
  }, [detail]);
  useImperativeHandle(ref, () => ({
    open: async (record: API.ProductItem, readonly?: boolean) => {
      setVisible(true);
      if (readonly) {
        record.readonly = readonly;
      }
      setDetail(record);
    },
  }));

  const onClose = () => {
    setVisible(false);
  };

  const onRefresh = (data: API.ProductItem) => {
    setDetail(data);
    refresh?.();
  };

  return (
    <FormContainer
      title={intl.formatMessage({ id: 'webOperation_product_detail_drawer_title' })}
      width="90%"
      onCancel={onClose}
      open={visible}
      destroyOnClose={true}
      extra={
        <Button type="primary" ghost onClick={onClose}>
          <FormattedMessage id="webCommon_page_close_button_text" />
        </Button>
      }
      footer={null}
    >
      <DefinitionContext.Provider value={DefinitionContextProps}>
        <BasicInfo detail={detail} />
        <Tabs type="card" size="small">
          <TabPane
            tab={<FormattedMessage id="webOperation_product_productInfo_tabs_text" />}
            key="productInfo"
          >
            <ProductInfo detail={detail} refresh={(data: API.ProductItem) => onRefresh(data)} />
          </TabPane>
          <TabPane
            tab={<FormattedMessage id="webOperation_product_introduction_tabs_text" />}
            key="introduction"
          >
            <IntroductionDetail detail={detail} />
          </TabPane>
          <TabPane
            tab={<FormattedMessage id="webOperation_product_deviceProtocol_tabs_text" />}
            key="deviceProtocol"
          >
            <DeviceProtocol detail={detail} />
          </TabPane>
          <TabPane
            tab={<FormattedMessage id="webOperation_product_partList_tabs_text" />}
            key="part"
          >
            <PartList detail={detail} />
          </TabPane>
          {detail.readonly ? null : (
            <TabPane
              tab={<FormattedMessage id="webOperation_product_multicode_tabs_text" />}
              key="multicode"
            >
              <MultiList detail={detail} />
            </TabPane>
          )}
          <TabPane tab={<FormattedMessage id="webOperation_product_faq_tabs_text" />} key="faq">
            <Faq proDetail={detail} />
          </TabPane>
          {detail.readonly ? null : (
            <TabPane
              tab={<FormattedMessage id="webOperation_product_firmwareConfig_tabs_text" />}
              key="firmwareConfig"
            >
              <FirmwareRelease pid={detail.id!} />
            </TabPane>
          )}
        </Tabs>
      </DefinitionContext.Provider>
    </FormContainer>
  );
});

export default ProductDetail;
