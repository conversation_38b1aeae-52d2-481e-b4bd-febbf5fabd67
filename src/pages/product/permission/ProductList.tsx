import React, { useRef } from 'react';
import { FormattedMessage, useAccess, useIntl } from 'umi';
import { Divider, Space, Image } from 'antd';
import { Access } from '@@/plugin-access/access';
import type { ProColumns } from '@ant-design/pro-components';
import ResizableTable from '@/components/Table';
import { useCategories, useBrands } from '@/hooks/selectHooks';
import { typeColumns, useColumn } from '@/hooks/column';
import { createKAC } from '@/components/KeepAlive';
import Settings, { RefType as SettingRef } from './Settings';
import { getProductList } from '@/services/product';

const ProductList: React.FC = () => {
  const settingRef = useRef<SettingRef>(null);
  const { createColumns } = useColumn();
  const access = useAccess();
  const intl = useIntl();
  const { valueEnum: categoryValueEnum } = useCategories();
  const { valueEnum: brandValueEnum } = useBrands();

  const actionColumn: ProColumns = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 80,
    fixed: 'right',
    render: (_, record) => [
      <Space key="setting" split={<Divider type="vertical" />}>
        <Access accessible={access.canSettingProductPermission()}>
          <a
            key="delete"
            onClick={() => {
              settingRef.current?.open(record.id);
            }}
          >
            <FormattedMessage id="webCommon_page_config_button_text" />
          </a>
        </Access>
      </Space>,
    ],
  };

  const listColumns: typeColumns[] = [
    {
      dataIndex: 'id',
      width: 160,
    },
    {
      dataIndex: 'categoryId',
      width: 150,
      valueType: 'select',
      valueEnum: categoryValueEnum,
    },
    {
      dataIndex: 'model',
      width: 150,
    },
    {
      dataIndex: 'commodityModel',
      width: 150,
    },
    {
      dataIndex: 'productType',
      width: 150,
      valueEnum: {
        gatewaySubDevice: intl.formatMessage({
          id: 'webOperation_dictionary_gatewaySubDevice_select_text',
        }),
        oldIotDevice: intl.formatMessage({
          id: 'webOperation_dictionary_oldIotDevice_select_text',
        }),
        directConnectedDevice: intl.formatMessage({
          id: 'webOperation_dictionary_directConnectedDevice_select_text',
        }),
        gatewayDevice: intl.formatMessage({
          id: 'webOperation_dictionary_gatewayDevice_select_text',
        }),
        notIotDevice: intl.formatMessage({
          id: 'webOperation_dictionary_notIotDevice_select_text',
        }),
      },
    },
    {
      dataIndex: 'status',
      width: 150,
      valueEnum: {
        PDevelopIng: intl.formatMessage({
          id: 'webOperation_dictionary_PDevelopIng_select_text',
        }),
        PReopenSuccess: intl.formatMessage({
          id: 'webOperation_dictionary_PReopenSuccess_select_text',
        }),
        PRefuseClose: intl.formatMessage({
          id: 'webOperation_dictionary_PRefuseClose_select_text',
        }),
        PApproved: intl.formatMessage({
          id: 'webOperation_dictionary_PApproved_select_text',
        }),
        PRefuseReopen: intl.formatMessage({
          id: 'webOperation_dictionary_PRefuseReopen_select_text',
        }),
        PReopenApprove: intl.formatMessage({
          id: 'webOperation_dictionary_PReopenApprove_select_text',
        }),
        PCloseApprove: intl.formatMessage({
          id: 'webOperation_dictionary_PCloseApprove_select_text',
        }),
      },
    },
    {
      dataIndex: 'releaseStatus',
      width: 150,
      valueEnum: {
        PToBeRelease: intl.formatMessage({
          id: 'webOperation_dictionary_PToBeRelease_select_text',
        }),
        PReleased: intl.formatMessage({
          id: 'webOperation_dictionary_PReleased_select_text',
        }),
        POffReleased: intl.formatMessage({
          id: 'webOperation_dictionary_POffReleased_select_text',
        }),
      },
    },
    {
      dataIndex: 'productIcon',
      width: 150,
      hideInSearch: true,
      render: (_, record) => <Image width={80} height={80} src={record.productIcon} />,
    },
    {
      dataIndex: 'brandId',
      width: 150,
      valueType: 'select',
      valueEnum: brandValueEnum,
    },
    {
      dataIndex: 'productName',
      width: 150,
      hideInTable: true,
    },
    {
      dataIndex: 'productNameLanguage',
      langCode: 'webOperation_product_productName_tableColumn_text',
      width: 150,
      hideInSearch: true,
    },
    {
      dataIndex: 'productSnCode',
      width: 150,
    },
  ];

  const initColumns = [...listColumns, actionColumn];
  const allColumns = createColumns('product', initColumns);

  return (
    <>
      <ResizableTable<API.ProductItem, API.ProductPageParams>
        rowKey="id"
        defaultSize="small"
        search={{
          labelWidth: 'auto',
        }}
        scroll={{ x: 1300 }}
        request={getProductList}
        columns={allColumns}
      />
      <Settings ref={settingRef} key="view" />
    </>
  );
};
export default createKAC(ProductList);
