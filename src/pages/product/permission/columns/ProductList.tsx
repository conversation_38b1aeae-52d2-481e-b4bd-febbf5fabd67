import type { typeColumns } from '@/hooks/column';
import { replace } from 'lodash-es';

export const userColumns: typeColumns[] = [
  {
    dataIndex: 'sysUserEmployeeNumber',
    width: 160,
    langCode: 'webOperation_employee_employeeNumber_tableColumn_text',
    render: (_, record) => {
      const reg = /^(\S+)\S{4}(\S+)$/;
      return replace(record.sysUserEmployeeNumber, reg, '$1****$2');
    },
  },
  {
    dataIndex: 'sysUsername',
    width: 150,
    langCode: 'webOperation_employee_userName_tableColumn_text',
  },
];
