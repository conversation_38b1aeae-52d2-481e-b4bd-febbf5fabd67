import type { typeColumns } from '@/hooks/column';

export const listColumns: typeColumns[] = [
  {
    dataIndex: 'employeeNumber',
    width: 160,
    hideInTable: true,
  },
  {
    dataIndex: 'username',
    width: 150,
    hideInTable: true,
    langCode: 'webOperation_employee_userName_tableColumn_text',
  },
  {
    dataIndex: 'sysUserEmployeeNumber',
    width: 160,
    hideInSearch: true,
    langCode: 'webOperation_employee_employeeNumber_tableColumn_text',
  },
  {
    dataIndex: 'sysUsername',
    width: 150,
    hideInSearch: true,
    langCode: 'webOperation_employee_userName_tableColumn_text',
  },
];
