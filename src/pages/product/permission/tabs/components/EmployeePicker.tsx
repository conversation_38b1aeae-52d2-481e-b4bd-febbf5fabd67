import React, { useState } from 'react';
import { useIntl, useAccess, FormattedMessage } from 'umi';
import { Access } from '@@/plugin-access/access';
import { Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { map } from 'lodash-es';

import ResizableTable from '@/components/Table';
import FormContainer from '@/components/Form/FormContainer';
import { useColumn } from '@/hooks/column';

import { getEmployeeList } from '@/services/product';
import { listColumns } from '../../columns/EmployeeList';

export interface EmployeeProps {
  setData: (list: API.SysUserItem[]) => void;
  selectedKeys?: API.SysUserItem[];
  accessName?: string;
}

const EmployeePicker = React.forwardRef<APP.RefType, EmployeeProps>((props, ref) => {
  const [selectedValues, setSelectedValues] = useState<API.SysUserItem[]>([]);
  const { setData, selectedKeys, accessName } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const intl = useIntl();
  const access = useAccess();
  const { createColumns } = useColumn();

  const initColumns = createColumns('employee', listColumns);

  const onSelectChange = (newSelectedRowKeys: React.Key[], selectedRows: API.SysUserItem[]) => {
    setSelectedValues(selectedRows);
  };

  const rowSelection = {
    preserveSelectedRowKeys: true,
    onChange: onSelectChange,
    getCheckboxProps: (record: API.SysUserItem) => ({
      disabled: map(selectedKeys, (item) => item.sysUserEmployeeNumber).includes(
        record.sysUserEmployeeNumber,
      ),
    }),
  };

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: 'webOperation_permission_add_button_text' })}
        width="50%"
        onCancel={() => {
          setVisible(false);
        }}
        onConfirm={() => {
          setData(selectedValues);
          setVisible(false);
        }}
        open={visible}
        destroyOnClose={true}
      >
        <ResizableTable<API.SysUserItem, API.EmployeePageParams>
          rowKey="sysUserEmployeeNumber"
          defaultSize="small"
          search={{
            defaultCollapsed: false,
            collapseRender: false,
            labelWidth: 'auto',
          }}
          rowSelection={rowSelection}
          request={getEmployeeList}
          columns={initColumns}
        />
      </FormContainer>
      <Access accessible={access['canAddPermission' + accessName]?.()}>
        <Button
          key="employee"
          type="primary"
          onClick={() => {
            setVisible(true);
          }}
        >
          <PlusOutlined />
          <FormattedMessage id="webOperation_permission_add_button_text" />
        </Button>
      </Access>
    </>
  );
});

export default EmployeePicker;
