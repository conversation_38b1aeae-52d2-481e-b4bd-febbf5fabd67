import React, { ReactNode, useRef, useState } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Divider, Spin, Space, message, Menu, Modal } from 'antd';
import { Access } from '@@/plugin-access/access';
import type { ProColumns, ActionType } from '@ant-design/pro-components';
import { useRequest } from '@@/plugin-request/request';
import ResizableTable from '@/components/Table';
import { omit } from 'lodash-es';

import { useColumn } from '@/hooks/column';
import EmployeePicker from './components/EmployeePicker';
import {
  getRnTestPermissionList,
  addRnTestPermission,
  deleteRnTestPermission,
  getRnApprovePermissionList,
  addRnApprovePermission,
  deleteRnApprovePermission,
} from '@/services/product';
import { userColumns } from '../columns/ProductList';

const Menus = (_: any, dom: ReactNode, onChange: (key: string) => void) => {
  return (
    <div
      style={{
        display: 'flex',
        width: '100%',
      }}
    >
      <Menu
        onSelect={(e) => onChange(e.key)}
        style={{ width: 150 }}
        defaultSelectedKeys={['Test']}
        mode="inline"
        items={[
          {
            key: 'Test',
            label: <FormattedMessage id="webOperation_permission_test_tabs_text" />,
          },
          {
            key: 'Approve',
            label: <FormattedMessage id="webOperation_permission_approve_tabs_text" />,
          },
        ]}
      />
      <div
        style={{
          flex: 1,
        }}
      >
        {dom}
      </div>
    </div>
  );
};
const Rn: React.FC<{ pid: string }> = ({ pid }) => {
  const actionRef = useRef<ActionType>();
  const employeeRef = useRef<APP.RefType>(null);
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const [type, setType] = useState<string>('Test');
  const [employeeNumber, setEmployeeNumber] = useState<string | undefined>();
  const [selectedData, setSelectedData] = useState<API.SysUserItem[]>([]);
  const { createColumns } = useColumn();
  const intl = useIntl();

  const access = useAccess();

  const { run: handleSubmit } = useRequest(
    async (values) => {
      setActionLoading(true);
      const params = {
        productId: pid,
        users: values,
      };
      if (type === 'Test') {
        await addRnTestPermission(params);
      } else if (type === 'Approve') {
        await addRnApprovePermission(params);
      }

      setActionLoading(false);
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      actionRef.current?.reset?.();
    },
    { manual: true },
  );

  const { run: handleDelete, loading } = useRequest(
    async () => {
      if (!employeeNumber) return;
      const params = {
        productId: pid,
        users: [
          {
            sysUserEmployeeNumber: employeeNumber,
          },
        ],
      };
      if (type === 'Test') {
        await deleteRnTestPermission(params);
      } else if (type === 'Approve') {
        await deleteRnApprovePermission(params);
      }

      setEmployeeNumber(undefined);
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      actionRef.current?.reload();
    },
    { manual: true },
  );

  const onChange = (key: string) => {
    setType(key);
    actionRef.current?.reset?.();
  };

  const getData = async (params = {}) => {
    if (type === 'Test') {
      const res = await getRnTestPermissionList(omit(params, ['pageSize', 'current']));
      setSelectedData(res?.data);
      return res;
    } else if (type === 'Approve') {
      const res = await getRnApprovePermissionList(omit(params, ['pageSize', 'current']));
      setSelectedData(res?.data);
      return res;
    }
  };
  const actionColumn: ProColumns = {
    title: <FormattedMessage id="webCommon_page_option_tableColumn_text" />,
    dataIndex: 'option',
    valueType: 'option',
    width: 80,
    fixed: 'right',
    render: (_, record) => [
      <Space key="edit" split={<Divider type="vertical" />}>
        <Access accessible={access['canDeletePermissionRn' + type]?.()}>
          <a
            key="delete"
            onClick={() => {
              setEmployeeNumber(record.sysUserEmployeeNumber);
            }}
          >
            <FormattedMessage id="webCommon_page_delete_tableButton_linkText" />
          </a>
        </Access>
      </Space>,
    ],
  };

  const initColumns = [...userColumns, actionColumn];
  const allColumns = createColumns('employee', initColumns);

  return (
    <>
      <Spin spinning={actionLoading}>
        <ResizableTable<API.SysUserItem, API.SysUserPageParams>
          actionRef={actionRef}
          rowKey="sysUserEmployeeNumber"
          defaultSize="small"
          search={false}
          pagination={false}
          params={{ req: pid }}
          tableRender={(_, dom) => Menus(_, dom, onChange)}
          headerTitle={
            <EmployeePicker
              ref={employeeRef}
              key="create"
              selectedKeys={selectedData}
              setData={handleSubmit}
              accessName={'Rn' + type}
            />
          }
          request={getData}
          columns={allColumns}
        />
      </Spin>
      <Modal
        title={intl.formatMessage({ id: 'webCommon_page_confirmToDelete_modal_title' })}
        open={!!employeeNumber}
        onOk={handleDelete}
        onCancel={() => setEmployeeNumber(undefined)}
        confirmLoading={loading}
      >
        <FormattedMessage id="webOperation_permission_delete_modal_message" />
      </Modal>
    </>
  );
};
export default Rn;
