// 框架依赖引入
import React, { useState, useImperativeHandle } from 'react';
import { useIntl } from 'umi';
import { Tabs, Button } from 'antd';
import { FormattedMessage } from '@@/plugin-locale/localeExports';
// 公共自定义依赖引入
import FormContainer from '@/components/Form/FormContainer';
// 页面自定义依赖引入
import Product from './tabs/Product';
import Ota from './tabs/Ota';
import Rn from './tabs/Rn';

const { TabPane } = Tabs;

export interface RefType {
  open: (pid: string) => void;
}

const ProductDetail = React.forwardRef<RefType, APP.EditFormProps>((props, ref) => {
  const [productId, setProductId] = useState<string>();
  const [visible, setVisible] = useState<boolean>(false);
  const intl = useIntl();

  useImperativeHandle(ref, () => ({
    open: (pid: string) => {
      setVisible(true);
      setProductId(pid);
    },
  }));

  const onClose = () => {
    setVisible(false);
  };

  return (
    <FormContainer
      title={intl.formatMessage({ id: 'webOperation_permission_setting_drawer_title' })}
      width="80%"
      onCancel={onClose}
      open={visible}
      destroyOnClose={true}
      extra={
        <Button type="primary" ghost onClick={onClose}>
          <FormattedMessage id="webCommon_page_close_button_text" />
        </Button>
      }
      footer={null}
    >
      <Tabs type="card" size="small">
        <TabPane
          tab={<FormattedMessage id="webOperation_permission_product_tabs_text" />}
          key="productInfo"
        >
          <Product pid={productId!} />
        </TabPane>
        <TabPane
          tab={<FormattedMessage id="webOperation_permission_ota_tabs_text" />}
          key="introduction"
        >
          <Ota pid={productId!} />
        </TabPane>
        <TabPane
          tab={<FormattedMessage id="webOperation_permission_rn_tabs_text" />}
          key="deviceProtocol"
        >
          <Rn pid={productId!} />
        </TabPane>
      </Tabs>
    </FormContainer>
  );
});

export default ProductDetail;
