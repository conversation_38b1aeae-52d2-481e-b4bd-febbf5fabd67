import { useRequest } from '@@/plugin-request/request';
import { PlusOutlined } from '@ant-design/icons';
import { ProForm, ProFormInstance } from '@ant-design/pro-components';
import { message, Button } from 'antd';
import React, { useRef, useState } from 'react';
import { FormattedMessage, useIntl, useAccess } from 'umi';
import { Access } from '@@/plugin-access/access';

import FormContainer from '@/components/Form/FormContainer';
import { FormFields } from '@/components/Form/FormFields';

import { createProduct } from '@/services/product';
import { typeColumns } from '@/hooks/column';

export interface ProductFormProps {
  refresh?: (resetPageIndex?: boolean | undefined) => Promise<void> | undefined;
  columns: typeColumns[];
}

export interface RefType {
  edit: (item: API.ProductItem) => void;
}

const ProductEdit = React.forwardRef<RefType, ProductFormProps>((props, ref) => {
  const { refresh, columns } = props;
  const intl = useIntl();
  const access = useAccess();
  const [visible, setVisible] = useState<boolean>(false);
  const formRef = useRef<ProFormInstance<API.ProductItem>>();
  const [initialValues, setInitialValues] = useState<API.ProductItem>({});

  const onClose = () => {
    setVisible(false);
  };

  const { run: handleSubmit, loading } = useRequest(
    async (values) => {
      delete values.networkModes;
      await createProduct(values);
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      setVisible(false);
      refresh?.();
    },
    { manual: true },
  );

  return (
    <>
      <FormContainer
        title={intl.formatMessage({ id: 'webOperation_product_create_drawer_title' })}
        width="50%"
        onCancel={onClose}
        onConfirm={() => formRef.current?.submit()}
        open={visible}
        destroyOnClose={true}
        loading={loading}
      >
        <ProForm
          formRef={formRef}
          initialValues={initialValues}
          onFinish={handleSubmit}
          labelCol={{ span: 5 }}
          layout="horizontal"
          onReset={onClose}
          submitter={false}
        >
          <FormFields columns={columns} pageName="product" />
        </ProForm>
      </FormContainer>
      <Access accessible={access.canCreateProduct()}>
        <Button
          type="primary"
          onClick={() => {
            setInitialValues({
              networkModes: ['notNetworked'],
            });
            setVisible(true);
          }}
        >
          <PlusOutlined />
          <FormattedMessage id="webOperation_product_create_drawer_title" />
        </Button>
      </Access>
    </>
  );
});

export default ProductEdit;
