import { getAllCategory, getAllBrand, getFleetDeviceCategories } from '@/services/common';
import { forEach, keyBy, map, reduce } from 'lodash-es';
import { useEffect, useState } from 'react';

/**
 * 品牌
 */
interface BrandItem {
  id: string;
  brandName: string;
  brandIcon?: any;
  description?: string;
  createTime?: number;
}

/**
 * 品类
 */
interface CategoryItem {
  id: string;
  categoryName: string;
  categoryIcon?: any;
  description?: string;
  createTime?: number;
  code?: string;
}

export const useCategories = () => {
  const [list, setList] = useState<{ label: string; value: string }[]>([] as any);
  const [valueEnum, setValueEnum] = useState({});

  useEffect(() => {
    getAllCategory().then((res: CategoryItem[]) => {
      setList(map(res, (item) => ({ label: item.categoryName, value: item.id })));
      const mapData = {};
      forEach(res, (item) => {
        mapData[item.id] = item.categoryName;
      });
      setValueEnum(mapData);
    });
  }, []);

  return { valueEnum, list };
};

export const useBrands = () => {
  const [data, setData] = useState<{ [key: string]: any }>({});
  const [valueEnum, setValueEnum] = useState({});
  const [list, setList] = useState<{ label: string; value: string }[]>([] as any);

  useEffect(() => {
    getAllBrand().then((res: BrandItem[]) => {
      setData(
        keyBy(
          map(res, (item) => ({ id: item.id, text: item.brandName })),
          'id',
        ),
      );
      setList(map(res, (item) => ({ label: item.brandName, value: item.id })));
      const mapData = {};
      forEach(res, (item) => {
        mapData[item.id] = item.brandName;
      });
      setValueEnum(mapData);
    });
  }, []);

  return { data, valueEnum, list };
};

export const useDeviceCategories = () => {
  const [list, setList] = useState<{ label: string; value: string }[]>([] as any);
  const [valueEnum, setValueEnum] = useState({});

  useEffect(() => {
    getFleetDeviceCategories().then((res: Omit<CategoryItem, 'id'>[]) => {
      reduce(
        res,
        (target, current) => {
          target[current.code!] = current.categoryName;
          return target;
        },
        {},
      );
      setList(map(res, (item) => ({ label: item.categoryName, value: item.code! })));
      const mapData = {};
      forEach(res, (item) => {
        mapData[item.code!] = item.categoryName;
      });
      setValueEnum(mapData);
    });
  }, []);

  return { list, valueEnum };
};
