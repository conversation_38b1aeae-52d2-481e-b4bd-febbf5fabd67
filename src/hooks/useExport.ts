import { MutableRefObject } from 'react';
import { ProFormInstance } from '@ant-design/pro-components';
import { useRequest } from '@@/plugin-request/request';
import { downloadByData } from '@/utils/download';
import { beforeSearchSubmit } from '@/components/Table';
import dayjs from 'dayjs';
const useExport = (
  serviceName: APP.FnType,
  formRef: MutableRefObject<ProFormInstance | undefined>,
  fileName?: string,
  params?: Object,
) => {
  const { run, loading } = useRequest(
    async () => {
      const data = await serviceName({
        ...params,
        ...beforeSearchSubmit(formRef.current?.getFieldsFormatValue?.()),
      });
      if (data.data.type === 'application/json') {
        return data;
      }
      const disposition = data.response?.headers.get('content-disposition');
      const filename = disposition ? disposition.split(';')[1].split("filename*=utf-8''")[1] : null;
      downloadByData({
        data: data.data,
        filename: filename ? decodeURI(filename) : fileName + `-${dayjs().format('YYYYMMDD')}.csv`,
      });
      return data;
    },
    { manual: true },
  );
  return {
    run,
    loading,
  };
};

export default useExport;
