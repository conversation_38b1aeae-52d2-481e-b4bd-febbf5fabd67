import { RefObject, MutableRefObject, ReactNode } from 'react';
import { useIntl, useAccess, useHistory, getIntl } from 'umi';
import { message, Space as Aspace } from 'antd';
import { ProForm, ProFormInstance, ProFormText } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-components';
import { FormattedMessage } from '@@/plugin-locale/localeExports';
import { Access } from '@@/plugin-access/access';
import { filter, map } from 'lodash-es';

import Space from '@/components/Space';
import { RefType } from '@/components/Modal/index';
import { FormFields } from '@/components/Form/FormFields';
import type { typeColumns } from '@/hooks/column';
import { len128 } from '@/utils/validate';

export interface OptionColumn {
  name: string; // 按钮名称
  onClick?: APP.FnType; // 按钮点击事件
  langCode?: string; // 为了复用已存在的多语言code，用于文案显示
  module?: string; // 模块名称，用于识别当前按钮权限函数
  hidden?: boolean; // 是否隐藏
}

interface ComponentRef {
  modalRef: RefObject<RefType>;
  formRef: MutableRefObject<ProFormInstance | undefined>;
}

interface ApiInfo {
  api: APP.FnType;
  id: any;
}

const reasonColumn: typeColumns[] = [
  {
    dataIndex: 'info',
    valueType: 'textarea',
    showInForm: true,
    readonly: true,
  },
];

const remarkColumn: typeColumns[] = [
  {
    dataIndex: 'remark',
    langCode: 'webOperation_release_remark_tableColumn_text',
    valueType: 'textarea',
    required: true,
    showInForm: true,
  },
];

export const detailAction = 'canViewDetail';
export const useAction = (
  key: string, // 行记录主键
  actionRef?: MutableRefObject<ActionType | undefined>, // 列表组件
) => {
  const intl = useIntl();
  const access = useAccess();
  const history = useHistory();
  const createActions = (
    columns: OptionColumn[], // 列表操作列
    record: Record<string, any>, // 列表行记录
    prefixIntl: string, // 用于多语言code拼接
  ) => {
    const renderButton = map(columns, (item) => {
      if (record[item.name]) {
        return (
          <Access key={item.name} accessible={access[item.name + item.module]?.()}>
            <a
              onClick={async () => {
                if (item.name === detailAction) {
                  history.push({
                    pathname: `/${prefixIntl}/${record[key]}`,
                  });
                } else {
                  await item.onClick?.(record[key], record);
                  if (actionRef) {
                    message.success(
                      intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }),
                    );
                    actionRef.current?.reload();
                  }
                }
              }}
            >
              <FormattedMessage
                id={item.langCode || `webOperation_${prefixIntl}_${item.name}_tableButton_text`}
              />
            </a>
          </Access>
        );
      } else {
        return null;
      }
    });
    return [<Space key="space">{renderButton}</Space>];
  };

  // 与 createActions 的区别是：
  // 不从接口数据来判断是否展示某个按钮，传进来的 columns 中的按钮都会展示
  const createOptimazationActions = (
    columns: OptionColumn[], // 列表操作列
    record: Record<string, any>, // 列表行记录
    prefixIntl: string, // 用于多语言code拼接
  ) => {
    const renderButton = map(
      // 1. 过滤掉没有权限的按钮
      // 2. 过滤掉配置了 hidden 隐藏的按钮
      filter(columns, (item) => !!access[item.name]?.() && !item.hidden),
      (item) => (
        <a
          key={item.name}
          onClick={async () => {
            if (item.name === detailAction) {
              history.push({
                pathname: `/${prefixIntl}/${record[key]}`,
              });
            } else {
              await item.onClick?.(record[key], record);
            }
          }}
        >
          <FormattedMessage
            id={item.langCode || `webOperation_${prefixIntl}_${item.name}_tableButton_text`}
          />
        </a>
      ),
    );
    return (
      <Space quantity={3} key="space">
        {renderButton}
      </Space>
    );
  };
  return {
    createActions,
    createOptimazationActions,
  };
};

export const showRefuseReason = (
  serviceName: APP.FnType,
  modalRef: RefObject<RefType>,
  id: string,
) => {
  modalRef.current?.setProps({
    okButtonProps: { style: { display: 'none' } },
  });
  modalRef.current?.open({
    title: 'webOperation_refuse_reason_modal_title',
    msg: (
      <ProForm params={{ req: id }} request={serviceName} submitter={false}>
        <FormFields columns={reasonColumn} pageName="refuse" />
      </ProForm>
    ),
    value: id,
  });
};

export const showRefuseForm = (refs: ComponentRef, serviceName: APP.FnType, id: string) => {
  refs.modalRef.current?.setProps({
    okButtonProps: { style: { display: 'inline-block' } },
  });
  refs.modalRef.current?.open({
    title: 'webOperation_product_refuse_modal_title',
    msg: (
      <ProForm formRef={refs.formRef} submitter={false}>
        <ProFormText name="pid" key="pid" label={false} initialValue={id} hidden={true} />
        <FormFields columns={remarkColumn} pageName="release" />
      </ProForm>
    ),
    value: id,
    onOk: () => serviceName,
  });
};

export const showConfirmModal = (
  modalRef: RefObject<RefType>,
  apiInfo: ApiInfo,
  modelName: string,
) => {
  modalRef.current?.setProps({
    okButtonProps: { style: { display: 'inline-block' } },
  });
  modalRef.current?.open({
    title: `webOperation_${modelName}_modal_title`,
    msg: <FormattedMessage id={`webOperation_${modelName}_modal_message`} />,
    value: apiInfo.id,
    onOk: () => apiInfo.api,
  });
};

export const showTwiceConfirmModal = (refs: ComponentRef, apiInfo: ApiInfo, modelName: string) => {
  refs.modalRef.current?.setProps({
    okButtonProps: { style: { display: 'inline-block' } },
  });
  refs.modalRef.current?.open({
    title: 'webCommon_page_confirmToDelete_modal_title',
    msg: (
      <Aspace direction="vertical">
        <FormattedMessage id={`webOperation_${modelName}_modal_message`} />
        <ProForm layout="horizontal" submitter={false} formRef={refs.formRef}>
          <ProFormText
            name="confirm"
            rules={[
              {
                whitespace: true,
                required: true,
                type: 'enum',
                enum: ['Yes'],
                message: <FormattedMessage id="webCommon_page_delete_input_placeholder" />,
              },
              len128,
            ]}
            transform={(value: any) => ({})}
          />
          <ProFormText name="req" key="id" label={false} initialValue={apiInfo.id} hidden={true} />
        </ProForm>
      </Aspace>
    ),
    value: apiInfo.id,
    onOk: () => apiInfo.api,
  });
};

export const showDeleteModal = (
  modalRef: RefObject<RefType>,
  apiInfo: ApiInfo,
  modelName: string,
) => {
  modalRef.current?.setProps({
    okButtonProps: { style: { display: 'inline-block' } },
  });
  modalRef.current?.open({
    title: 'webCommon_page_confirmToDelete_modal_title',
    msg: <FormattedMessage id={`webOperation_${modelName}_modal_message`} />,
    value: apiInfo.id,
    onOk: () => apiInfo.api,
  });
};

export const showDeleteFailReason = (modalRef: RefObject<RefType>, errorCode: string, e: any) => {
  try {
    const res = JSON.parse(e.message);
    if (res.responseCode === errorCode) {
      modalRef.current?.setProps({
        okButtonProps: { style: { display: 'none' } },
      });
      modalRef.current?.open({
        title: 'webCommon_page_deleteForbid_modal_title',
        msg: res.errorMessage,
      });
    } else {
      message.error(res.errorMessage);
    }
  } catch (err) {
    message.error(getIntl().formatMessage({ id: 'webCommon_page_internalError_toast_text' }));
  }
};

export const showCustomConfirmModal = (
  modalRef: RefObject<RefType>,
  apiInfo: ApiInfo,
  modelName: string,
  content: ReactNode,
) => {
  modalRef.current?.setProps({
    okButtonProps: { style: { display: 'inline-block' } },
  });
  modalRef.current?.open({
    title: `webOperation_${modelName}_modal_title`,
    msg: content,
    value: apiInfo.id,
    onOk: () => apiInfo.api,
  });
};
