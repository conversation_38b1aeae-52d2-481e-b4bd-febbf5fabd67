import { FormattedMessage } from '@@/plugin-locale/localeExports';
import { ProColumns } from '@ant-design/pro-components';
import { useIntl, getIntl } from 'umi';
import { ReactElement } from 'react';
import defaultSettings from '../../config/defaultSettings';
import { get, omit, reduce, omitBy } from 'lodash-es';
import dayjs from 'dayjs';
import { RuleObject } from 'antd/lib/form';
export type typeColumns = ProColumns & {
  showInForm?: boolean; // 增改表单是否显示该列
  hiddenInForm?: boolean; // 增改表单是否隐藏该列
  showInDetail?: boolean; // 详情是否显示该列
  required?: boolean; // 该列在增改表单中是否必填
  isRender?: boolean; // 当列值为数组且存在属性valueEnumName时为True。(不常用)
  renderInEditForm?: (intl: any) => void;
  langCode?: string; // 用于复用多语言code的别名
  readonly?: boolean; // 当表单被用于详情复用时，为可读，不可编辑
  extraRender?: ReactElement; // 在详情页字段同行未尾显示多语言ID
  addonRender?: ReactElement; // 在编辑页表单字段同行未尾显示多语言ID
  showLabel?: boolean; // 搜索是否显示label
  rule?: RuleObject;
  fieldProps?: any;
  options?: any;
};

export const rangeExtra: ProColumns[] = [
  {
    title: <FormattedMessage id="webCommon_page_createBy_tableColumn_text" />,
    dataIndex: 'createBy',
    order: 3,
    width: 150,
  },
  {
    title: <FormattedMessage id="webCommon_page_createTime_tableColumn_text" />,
    dataIndex: 'createTime',
    valueType: 'dateRange',
    order: 10,
    width: 150,
    render: (_, record) =>
      record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') : _,
  },
  {
    title: <FormattedMessage id="webCommon_page_updateBy_tableColumn_text" />,
    dataIndex: 'updateBy',
    order: 2,
    width: 150,
  },
  {
    title: <FormattedMessage id="webCommon_page_updateTime_tableColumn_text" />,
    dataIndex: 'updateTime',
    valueType: 'dateRange',
    order: 9,
    width: 150,
    render: (_, record) =>
      record.updateTime ? dayjs(record.updateTime).format('YYYY-MM-DD HH:mm:ss') : _,
  },
];

export const extra: ProColumns[] = [
  {
    title: <FormattedMessage id="webCommon_page_createBy_tableColumn_text" />,
    dataIndex: 'createBy',
    hideInSearch: true,
    width: 150,
  },
  {
    title: <FormattedMessage id="webCommon_page_createTime_tableColumn_text" />,
    hideInSearch: true,
    dataIndex: 'createTime',
    valueType: 'dateTime',
    width: 150,
  },
  {
    title: <FormattedMessage id="webCommon_page_updateBy_tableColumn_text" />,
    dataIndex: 'updateBy',
    hideInSearch: true,
    width: 150,
  },
  {
    title: <FormattedMessage id="webCommon_page_updateTime_tableColumn_text" />,
    hideInSearch: true,
    dataIndex: 'updateTime',
    valueType: 'dateTime',
    width: 150,
  },
];

export const releaseColumns: typeColumns[] = [
  {
    title: <FormattedMessage id="webOperation_product_applyUserName_tableColumn_text" />,
    dataIndex: 'applyBy',
    width: 150,
    hideInSearch: true,
  },
  {
    title: <FormattedMessage id="webOperation_product_applyTime_tableColumn_text" />,
    dataIndex: 'applyTime',
    width: 150,
    valueType: 'dateTime',
    hideInSearch: true,
  },
  {
    title: <FormattedMessage id="webOperation_product_approveUserName_tableColumn_text" />,
    dataIndex: 'approvedBy',
    width: 150,
    hideInSearch: true,
  },
  {
    title: <FormattedMessage id="webOperation_product_approveTime_tableColumn_text" />,
    dataIndex: 'approvedTime',
    width: 150,
    valueType: 'dateTime',
    hideInSearch: true,
  },
];

export const fieldProps = {
  placeholder: [
    getIntl().formatMessage({
      id: 'webCommon_page_select_common_placeholder',
    }),
    getIntl().formatMessage({
      id: 'webCommon_page_select_common_placeholder',
    }),
  ],
};
export const dateFilter: typeColumns[] = [
  {
    title: <FormattedMessage id="webCommon_page_createTime_tableColumn_text" />,
    dataIndex: 'createTimeFilter',
    valueType: 'dateRange',
    hideInTable: true,
    showLabel: true,
    fieldProps,
    langCode: 'webCommon_page_createTime_tableColumn_text',
  },
  {
    title: <FormattedMessage id="webCommon_page_updateTime_tableColumn_text" />,
    dataIndex: 'updateTimeFilter',
    valueType: 'dateRange',
    hideInTable: true,
    showLabel: true,
    fieldProps,
    langCode: 'webCommon_page_updateTime_tableColumn_text',
  },
];
export const releaseFilter: typeColumns[] = [
  {
    dataIndex: 'applyTimeFilter',
    valueType: 'dateRange',
    hideInTable: true,
    showLabel: true,
    langCode: 'webOperation_product_applyTime_tableColumn_text',
    fieldProps,
  },
  {
    dataIndex: 'approvedTimeFilter',
    valueType: 'dateRange',
    hideInTable: true,
    showLabel: true,
    langCode: 'webOperation_product_approveTime_tableColumn_text',
    fieldProps,
  },
];

export const creatorFilter: typeColumns[] = [
  {
    dataIndex: 'createByFilter',
    hideInTable: true,
    langCode: 'webCommon_page_createBy_tableColumn_text',
  },
  {
    dataIndex: 'updateByFilter',
    hideInTable: true,
    langCode: 'webCommon_page_updateBy_tableColumn_text',
  },
];

export const operatorFilter: typeColumns[] = [
  {
    dataIndex: 'applyByFilter',
    hideInTable: true,
    langCode: 'webOperation_product_applyUserName_tableColumn_text',
  },
  {
    dataIndex: 'approveByFilter',
    hideInTable: true,
    langCode: 'webOperation_product_approveUserName_tableColumn_text',
  },
];

export const useColumn = () => {
  const intl = useIntl();
  const createColumns = (prefixIntl: string, columns: typeColumns[]) => {
    let tableColumns: typeColumns[] = [];
    columns.forEach((item) => {
      const languageCode =
        item.langCode || 'webOperation_' + prefixIntl + '_' + item.dataIndex + '_tableColumn_text';
      const column: { [key: string]: any } = {
        title: item.title || <FormattedMessage id={languageCode} />,
        ...item,
      };
      if (!item.hideInSearch && item.dataIndex !== 'option') {
        /* 隐藏搜索表单项的标题 */
        if (defaultSettings.hideLabelInsearch && !item.showLabel) {
          column.formItemProps = { label: false };
        }
        /* 搜索框占位文字 */
        const pre = item.valueEnum
          ? intl.formatMessage({
              id: 'webCommon_page_select_common_placeholder',
            })
          : intl.formatMessage({
              id: 'webCommon_page_input_common_placeholder',
            });
        column.fieldProps = {
          ...{
            placeholder:
              pre +
              intl.formatMessage({
                id: languageCode,
              }),
          },
          ...(item.fieldProps || {}),
        };
      }
      tableColumns.push(column);
    });

    return tableColumns;
  };
  return {
    createColumns,
  };
};

type FN<K> = (v: Partial<K>) => Partial<K>;

export function compose<T = any>(args: T, ...funcs: FN<T>[]) {
  return reduce(
    funcs,
    (res: Partial<T>, fn) => {
      return fn(res);
    },
    args,
  );
}

export function timeParams<T extends {}>(args: T) {
  return {
    ...omit(args, ['createTime', 'updateTime']),
    ...omitBy(
      {
        createStartTime: get(args, ['createTime', 0]),
        createEndTime: get(args, ['createTime', 1]),
        updateStartTime: get(args, ['updateTime', 0]),
        updateEndTime: get(args, ['updateTime', 1]),
      },
      (v) => v === undefined,
    ),
  };
}
