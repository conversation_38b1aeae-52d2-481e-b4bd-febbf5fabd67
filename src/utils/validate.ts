import { RuleObject } from 'antd/lib/form';
import { getIntl } from 'umi';

export const str36 = {
  pattern: /^[A-Za-z0-9_\-\.\/?#]{1,36}$/,
  message: getIntl().formatMessage({ id: 'webCommon_page_str36_validator_message' }),
};

export const str128 = {
  pattern: /^[A-Za-z0-9_\-\.\/?#]{1,128}$/,
  message: getIntl().formatMessage({ id: 'webCommon_page_str128_validator_message' }),
};

// export const number128: RuleObject = {
//   type: 'number',
//   max: 128,
//   message: getIntl().formatMessage({ id: 'webCommon_page_len128_validator_message' }),
// };

export const len128: RuleObject = {
  type: 'string',
  max: 128,
  message: getIntl().formatMessage({ id: 'webCommon_page_len128_validator_message' }),
};
export const len300: RuleObject = {
  type: 'string',
  max: 300,
  message: getIntl().formatMessage({ id: 'webCommon_page_len300_validator_message' }),
};

export const len500: RuleObject = {
  type: 'string',
  max: 500,
  message: getIntl().formatMessage({ id: 'webCommon_page_len500_validator_message' }),
};

export const len3000: RuleObject = {
  type: 'string',
  max: 3000,
  message: getIntl().formatMessage({ id: 'webCommon_page_len3000_validator_message' }),
};

export const len5000: RuleObject = {
  type: 'string',
  max: 5000,
  message: getIntl().formatMessage({ id: 'webCommon_page_len5000_validator_message' }),
};

export const len36: RuleObject = {
  type: 'string',
  max: 36,
  message: getIntl().formatMessage({ id: 'webCommon_page_len36_validator_message' }),
};

export const urlChecked = {
  pattern: /^(http|https):\/\/\S*/,
  message: getIntl().formatMessage({ id: 'webCommon_page_url_validator_message' }),
};

export const s3Checked = {
  pattern: /s3.amazonaws.com/,
  message: getIntl().formatMessage({ id: 'webCommon_page_s3_validator_message' }),
};

export const digitChecked = {
  pattern: /^-?\d+(\.?\d+)?$/,
  message: getIntl().formatMessage({ id: 'webCommon_page_digit_validator_message' }),
};
