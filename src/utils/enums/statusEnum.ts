/**
 * 状态值颜色枚举值
 *
 * @remarks
 * 各业务模块关于涉及状态值的颜色枚举值定义
 *
 * @param
 * @returns
 *
 * <AUTHOR>
 *
 * @example
 *
 *
 * @throws
 *
 */
export enum StatusEnum {
  DISABLE = 'Default', // 设备停用
  NORMAL = 'Success', // 设备正常
  ONLINE = 'Success', // 设备在线
  OFFLINE = 'Default', // 设备离线
  ACTIVE = 'Success', // 设备激活
  INACTIVE = 'Default', // 设备未激活
  PCloseApprove = 'Warning', // 产品封板审核中
  PReopenApprove = 'Warning', // 产品解版审核中
  PRefuseClose = 'Error', // 产品封板被驳回
  PRefuseReopen = 'Error', // 产品解版被驳回
  PApproved = 'Success', // 产品已封板
  PReleaseApprove = 'Warning', // 产品发布审核中
  PUpdateReleaseApprove = 'Warning', // 产品更新审核中
  POffReleasedApprove = 'Warning', // 产品下架审核中
  PReleaseRefuse = 'Error', // 产品发布被驳回
  PReleaseTestRefuse = 'Error', // 产品发布测试被驳回
  PUpdateReleaseRefuse = 'Error', // 产品更新被驳回
  PUpdateReleaseTestRefuse = 'Error', // 产品更新测试被驳回
  POffReleasedRefuse = 'Error', // 产品下架被驳回
  PReleased = 'Success', // 产品已发布
  RELEASING = 'Warning', // 固件发布审核中
  STOPPING = 'Warning', // 固件停止发布审核中
  RELEASE_REFUSED = 'Error', // 固件发布被驳回
  STOP_REFUSED = 'Error', // 固件停止发布被驳回
  RELEASED = 'Success', // 固件已发布
  close_verify_ing = 'Warning', // RN封板审核中
  closed = 'Success', // RN已封版
  close_refused = 'Error', // RN封版被驳回
  release_verify_ing = 'Warning', // RN发布审核中
  test_refused = 'Error', // RN测试被驳回
  release_refused = 'Error', // RN发布被驳回
  released = 'Success', // RN已发布
  SUCCEED = 'Success', // 总成升级成功
  FAILED = 'Error', // 总成升级失败
  UPGRADING = 'Processing', // 总成升级中
  SUCCEEDED = 'Success', // 固件升级任务执行成功
  IN_PROGRESS = 'Processing', // 固件升级任务执行中
  REJECTED = 'Warning', // 固件升级任务被拒绝
  CLOSED = 'Success', // 固件已封版
  CLOSE_REFUSED = 'Error', // 固件封版被驳回
  CLOSING = 'Warning', // 固件封板审核中
  FAIL = 'Error', // 日志失败
  SUCCESS = 'Success', // 日志成功
  online = 'Success', // APP在线
  offline = 'Default', // APP离线
}
