/**
 * 单点登录请求
 *
 * @remarks 作为微前端的子应用，以创建iframe的方式请求单点登录地址
 *
 * @param
 * @returns
 *
 * @beta
 */
export const createIframe = () => {
  if (
    !document.getElementsByTagName('iframe')[0] &&
    !document.getElementById(ROOT_PATH.replace(/\//g, '') + '-iframe')
  ) {
    const iframe = document.createElement('iframe');
    const prefix = ROOT_PATH.replace(/\//g, '');
    iframe.style.cssText = 'display:none;margin:0 auto;width:100%';
    iframe.id = prefix + '-iframe';
    if (location.href.indexOf('?') > -1) {
      iframe.src =
        window['__INJECTED_PUBLIC_PATH_BY_QIANKUN__'] +
        prefix +
        '/login?back=' +
        encodeURIComponent(location.href + '&source=iframe');
    } else {
      iframe.src =
        window['__INJECTED_PUBLIC_PATH_BY_QIANKUN__'] +
        prefix +
        '/login?back=' +
        encodeURIComponent(location.href + '?source=iframe');
    }
    document.body.appendChild(iframe);
  }
};
