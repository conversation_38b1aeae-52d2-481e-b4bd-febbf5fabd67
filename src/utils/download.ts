import { last, split } from 'lodash-es';

interface DownloadProps {
  data: any;
  filename: string;
  type?: string;
}
/**
 * 导出数据
 *
 * @remarks 用于页面导出功能
 *
 * @param data - 二进制数据
 * @param filename - 导出的文件名
 * @param type - 数据格式
 * @returns
 *
 * @beta
 */
export const downloadByData = ({ data, filename, type }: DownloadProps) => {
  const blob = new Blob(type ? ['\uFEFF', data] : [data], {
    type: type || 'application/octet-stream',
  });
  const blobURL = window.URL.createObjectURL(blob);
  const tempLink = document.createElement('a');
  tempLink.style.display = 'none';
  tempLink.href = blobURL;
  tempLink.setAttribute('download', filename);
  document.body.appendChild(tempLink);
  tempLink.click();
  document.body.removeChild(tempLink);
  window.URL.revokeObjectURL(blobURL);
};
/**
 * 文件下载
 *
 * @remarks 用于页面的文件下载功能
 *
 * @param url - 文件的下载地址
 * @param filename - 下载的文件名
 * @returns
 *
 * @beta
 */
export const downloadByUrl = (url: string, name?: string) => {
  const tempLink = document.createElement('a');
  tempLink.style.display = 'none';
  tempLink.href = url;
  tempLink.target = '_blank';
  tempLink.setAttribute('download', name || last(split(url, '/')) || '');
  document.body.appendChild(tempLink);
  tempLink.click();
  document.body.removeChild(tempLink);
};
