@import '~antd/dist/antd.less';

html,
body,
#root {
  height: 100%;
}

.colorWeak {
  filter: invert(80%);
}

.dealer-map-input-wrapper .@{ant-prefix}-input-group-addon {
  vertical-align: top;
}

.@{ant-prefix}-layout {
  // min-height: 100vh;
  background: #f7f7f7 !important;
}

.@{ant-prefix}-pro-sider.@{ant-prefix}-layout-sider.@{ant-prefix}-pro-sider-fixed {
  left: unset;
}

.@{ant-prefix}-pro-basicLayout-content {
  margin: 0 15px 24px !important;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: @screen-xs) {
  .@{ant-prefix}-table {
    width: 100%;
    overflow-x: auto;

    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;

        > span {
          display: block;
        }
      }
    }
  }
}

// Compatible with IE11
// @media screen and(-ms-high-contrast: active), (-ms-high-contrast: none) {
//   body .@{ant-prefix}-design-pro > .@{ant-prefix}-layout {
//     min-height: 100vh;
//   }
// }

@pro-table-prefix-cls: ~'@{ant-prefix}-pro-table';
@pro-table-search-prefix-cls: ~'@{ant-prefix}-pro-table-search';

.@{pro-table-prefix-cls} .@{pro-table-search-prefix-cls} {
  margin-bottom: 0 !important;

  form {
    border-bottom: 1px solid #ebebeb;
  }
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  border-radius: 4px;
  -webkit-box-shadow: inset 0 0 8px rgba(119, 188, 31, 0.3);
}

::-webkit-scrollbar-thumb {
  background-color: @primary-3;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 0 8px rgba(119, 188, 31, 0.3);
}

::-webkit-scrollbar-button {
  display: none;
}

::-webkit-scrollbar-corner {
  display: none;
}

// 多语言ID样式 全局调整
.@{ant-prefix}-form-item-extra {
  position: absolute;
  right: 0;
  bottom: 0;
}
// .@{ant-prefix}-form-item-row {
//   align-items: center;
// }
// .@{ant-prefix}-form-item-label > label {
//   display: inline !important;
// }

// .@{ant-prefix}-form-item-label {
//   text-overflow: ellipsis;
// }

// .@{ant-prefix}-space-item {
//   white-space: normal;
// }
.@{ant-prefix}-view-form .@{ant-prefix}-form-item-control {
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
}
.@{ant-prefix}-view-form .@{ant-prefix}-form-item-control-input {
  align-items: flex-start;
  word-wrap: break-word;
  word-break: break-all;
}
.@{ant-prefix}-view-form .@{ant-prefix}-form-item-label > label {
  align-items: flex-start;
}
.@{ant-prefix}-view-form .@{ant-prefix}-form-item-extra {
  position: relative;
  bottom: 0;
  white-space: nowrap;
}

.@{ant-prefix}-edit-form .@{ant-prefix}-form-item-extra {
  position: absolute;
  right: 0;
  bottom: -23px;
}

.@{ant-prefix}-input-group-addon {
  color: #999 !important;
}

.@{ant-prefix}-space {
  border-style: none !important;
}

.@{ant-prefix}-image-preview-img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) !important;
}
.@{ant-prefix}-image-preview-operations li {
  display: none !important;
}
.@{ant-prefix}-image-preview-operations li:first-child {
  display: inline-block !important;
}

.@{ant-prefix}-input-group-compact > * {
  border-right-width: 0 !important;
}

.@{ant-prefix}-input-group-compact > *:not(:last-child) {
  margin-right: 15px !important;
}

.@{ant-prefix}-form-item-control-input-content {
  word-break: break-all !important;
}

.@{ant-prefix}-form-item-control-input-content .@{ant-prefix}-pro-card-body {
  padding-right: 0 !important;
  padding-left: 0 !important;
}
.@{ant-prefix}-form-item-control-input-content .@{ant-prefix}-pro-table-list-toolbar-container {
  padding-top: 0 !important;
}
// 表单label自动换行（国际化后label可能超长）
// .@{ant-prefix}-form-item-label {
//   display: flex;
//   align-items: flex-start;
//   justify-content: flex-end;
//   width: 100px;
//   line-height: 16px;
//   label {
//     height: auto !important;
//     min-height: 32px;
//     white-space: normal;
//     text-align: right;
//   }
// }

// 去掉antd中Input、Textarea组件获取焦点时的蓝色边框
.@{ant-prefix}-input:hover,
.@{ant-prefix}-input-affix-wrapper:not(.@{ant-prefix}-input-affix-wrapper-disabled):hover {
  z-index: 1;
  border-color: #93c940 !important;
  border-right-width: 1px;
}

.@{ant-prefix}-input-affix-wrapper:focus,
.@{ant-prefix}-input-affix-wrapper-focused,
.@{ant-prefix}-input:focus,
.@{ant-prefix}-input-focused {
  border-color: #93c940 !important;
  outline: 0 !important;
  box-shadow: 0 0 0 2px rgba(119, 188, 31, 0.2) !important;
}

// 选择框
.@{ant-prefix}-select:not(.@{ant-prefix}-select-disabled):hover .@{ant-prefix}-select-selector {
  border-color: #93c940 !important;
}

.@{ant-prefix}-select-focused:not(.@{ant-prefix}-select-disabled).@{ant-prefix}-select:not(.@{ant-prefix}-select-customize-input)
  .@{ant-prefix}-select-selector {
  border-color: #93c940 !important;
  outline: 0 !important;
  box-shadow: 0 0 0 2px rgba(119, 188, 31, 0.2) !important;
}

.@{ant-prefix}-picker-input > input:focus,
.@{ant-prefix}-picker-input > input-focused {
  border-color: none !important;
  box-shadow: none !important;
}

.@{ant-prefix}-picker-focused,
.@{ant-prefix}-input-number-focused {
  border-color: #93c940 !important;
  box-shadow: 0 0 0 2px rgba(119, 188, 31, 0.2) !important;
}

.@{ant-prefix}-picker:hover {
  border-color: #93c940 !important;
}

.@{ant-prefix}-input-number:hover {
  border-color: #93c940 !important;
}
.@{ant-prefix}-tooltip-inner {
  max-height: 300px;
  overflow-y: scroll;
}
