export default {
  webOperation_appsort_edit_drawer_title: 'Edit list',
  webOperation_appsort_view_drawer_title: 'View list',
  webOperation_appsort_isShow0_tableButton_text: 'Display',
  webOperation_appsort_isShow1_tableButton_text: 'Hide',
  webOperation_appsort_display_tableColumn_text: 'Show/hide',
  webOperation_appsort_elementCode_tableColumn_text: 'List item',
  webOperation_appsort_item_tableColumn_text: 'List item order',
  webOperation_appsort_itemCode_tableColumn_text: 'List name',
  webOperation_brand_brandIcon_tableColumn_text: 'Brand image',
  webOperation_brand_brandName_tableColumn_text: 'Brand name',
  webOperation_brand_description_tableColumn_text: 'Remarks',
  webOperation_brand_iconAddress_tableColumn_text: 'Image address',
  webOperation_brand_id_tableColumn_text: 'Brand ID',
  webOperation_brand_langId_tableColumn_text: 'Multilingual ID',
  webOperation_brand_delete_modal_message:
    'The brand data cannot be recovered after it is deleted. Do you want to delete it?',
  webOperation_category_categoryIcon_tableColumn_text: 'Category picture',
  webOperation_category_categoryName_tableColumn_text: 'Category name',
  webOperation_category_description_tableColumn_text: 'Note',
  webOperation_category_id_tableColumn_text: 'Category ID',
  webOperation_category_langId_tableColumn_text: 'Multilingual ID',
  webOperation_category_delete_modal_message:
    'This category data cannot be restored after it is deleted.',
  webOperation_code_add_common_text: 'Add device ID',
  webOperation_code_import_common_text: 'Import device list',
  webOperation_code_invalid_tableButton_text: 'Discard',
  webOperation_code_valid_tableButton_text: 'Restore',
  webOperation_code_importTemplate_fileName_text: 'multi-code table template',
  webOperation_code_export_fileName_text: 'multi-code table',
  webOperation_code_deviceId_tableColumn_text: 'Device ID',
  webOperation_code_itemCode_tableColumn_text: 'ITEM code',
  webOperation_code_mes_tableColumn_text: 'MES#',
  webOperation_code_moCode_tableColumn_text: 'MO code',
  webOperation_code_productionDate_tableColumn_text: 'Production Date',
  webOperation_code_iccid_tableColumn_text: 'ICCID',
  webOperation_code_sn_tableColumn_text: 'device sn',
  webOperation_code_status_tableColumn_text: 'Status',
  Weboperation_code_impor_modal_message:
    'Failed to import import_modal_message, please check the following data:',
  webOperation_code_invalid_modal_message:
    'After the device is abandoned, it cannot connect to the cloud.',
  webOperation_code_valid_modal_message:
    'The recovery device can reconnect to the cloud. Confirm the recovery?',
  webOperation_code_invalid_modal_title: 'Device disabled',
  webOperation_code_valid_modal_title: 'Device recovery',
  // Dealer
  webOperation_dealer_dealerId_tableColumn_text: 'Dealer ID',
  webOperation_dealer_name_tableColumn_text: 'Dealer name',
  webOperation_dealer_country_tableColumn_text: 'country',
  webOperation_dealer_state_tableColumn_text: 'Province',
  webOperation_dealer_city_tableColumn_text: 'City',
  webOperation_dealer_address_tableColumn_text: 'Dealer address',
  webOperation_dealer_zipcode_tableColumn_text: 'zipcode',
  webOperation_dealer_telephone_tableColumn_text: 'phone',
  webOperation_dealer_email_tableColumn_text: 'mailbox',
  webOperation_dealer_website_tableColumn_text: 'URL',
  webOperation_dealer_hours_tableColumn_text: 'Working hours',
  webOperation_dealer_lat_tableColumn_text: 'lat',
  webOperation_dealer_lng_tableColumn_text: 'lng',
  webOperation_dealer_category_tableColumn_text: 'category',
  webOperation_dealer_zoomLevel_tableColumn_text: 'Map zoom level',
  webOperation_dealer_color_tableColumn_text: 'color',
  webOperation_dealer_fontClass_tableColumn_text: 'Vector icon',
  webOperation_dealer_isActive_tableColumn_text: 'Active status',
  webOperation_dealer_image_tableColumn_text: 'Picture',
  webOperation_dealer_storeLocatorId_tableColumn_text: 'store locator',
  webOperation_dealer_delete_modal_message:
    'The dealer data cannot be recovered after it is deleted. Do you want to delete it?',
  webOperation_dealer_comfirm_import_modal_message:
    'Please confirm that the imported file content is correct and agree to delete historical data',
  webOperation_app_protocoldetail_drawer_title: 'Protocol Details',
  webOperation_brand_create_drawer_title: 'Create brand',
  webOperation_brand_detail_drawer_title: 'Brand details',
  webOperation_brand_edit_drawer_title: 'Edit brand',
  webOperation_category_create_drawer_title: 'Create category',
  webOperation_category_detail_drawer_title: 'Category details',
  webOperation_category_edit_drawer_title: 'Edit Category',
  webOperation_dealer_new_drawer_title: 'Add Dealer',
  webOperation_dealer_view_drawer_title: 'Dealer Details',
  webOperation_dealer_edit_drawer_title: 'Edit dealer',
  webOperation_dealer_hour_label_text: 'hour',
  webOperation_dealer_import_common_text: 'Import dealer',
  webOperation_dealer_importTemplate_fileName_text: 'Dealer template',
  Weboperation_dealer_impor_modal_message: 'Dealer import failed, please check the following data:',
  webOperation_group_select_drawer_title: 'Group Select',
  webOperation_faq_create_drawer_title: 'Add FAQ',
  webOperation_faq_detail_drawer_title: 'FAQ details',
  webOperation_faq_edit_drawer_title: 'Edit FAQ',
  webOperation_guide_create_drawer_title: 'Add file',
  webOperation_guide_edit_drawer_title: 'Edit file',
  webOperation_message_detail_drawer_title: 'Message details',
  webOperation_message_edit_drawer_title: 'Edit message',
  webOperation_message_new_drawer_title: 'Add message',
  webOperation_message_copy_drawer_title: 'Copy message',
  webOperation_message_result_drawer_title: 'Push result',
  webOperation_product_create_drawer_title: 'Add standalone product',
  webOperation_product_detail_drawer_title: 'Product details',
  webOperation_product_edit_drawer_title: 'Edit Product Information',
  webOperation_product_firmwareconfig_drawer_title: 'Task publishing configuration',
  webOperation_product_groupset_drawer_title: 'Product Test Group Configuration',
  webOperation_product_rnconfig_drawer_title: 'RN publish configuration',
  webOperation_product_rndetail_drawer_title: 'Publish details',
  webOperation_template_create_drawer_title: 'Add message template',
  webOperation_template_edit_drawer_title: 'Edit message template',
  webOperation_template_view_drawer_title: 'View message template',
  webOperation_page_relativeProduct_drawer_title: 'Associated Product',
  webOperation_firmware_export_filename_text: 'Firmware release list',
  webOperation_product_export_filename_text: 'Product list',
  webOperation_releaseProducts_export_filename_text: 'Publish product list',
  webOperation_rn_export_filename_text: 'RN publish list',
  // FAQ
  webOperation_faq_answer_tableColumn_text: 'Solution',
  webOperation_faq_answerLangId_tableColumn_text: 'Solution multilingual ID',
  webOperation_faq_instanceId_tableColumn_text: 'Problem ID',
  webOperation_faq_title_tableColumn_text: 'Problem',
  webOperation_faq_titleLangId_tableColumn_text: 'Problem multilingual ID',
  webOperation_faq_typeCode_tableColumn_text: 'Problem type',
  webOperation_faq_importTemplate_fileName_text: 'Common FAQ Import Template',
  webOperation_faq_export_filename_text: 'General FAQ',
  webOperation_faq_import_common_text: 'Import FAQ',
  Weboperation_faq_impor_modal_message:
    'Failed to import generic FAQ. Please check the following data:',
  webOperation_firmware_componentName_tableColumn_text: 'Assembly part name',
  webOperation_firmware_componentNo_tableColumn_text: 'Assembly part number',
  webOperation_firmware_componentType_tableColumn_text: 'Assembly part type',
  webOperation_firmware_developStatus_tableColumn_text: 'Firmware development status',
  webOperation_firmware_failed_tableColumn_text: 'Number of devices that failed to upgrade',
  webOperation_firmware_jobId_tableColumn_text: 'Task ID',
  webOperation_firmware_minimumVersion_tableColumn_text:
    'Minimum compatible firmware version number',
  webOperation_firmware_newVersion_tableColumn_text: 'Updated firmware version',
  webOperation_firmware_oldVersion_tableColumn_text: 'Firmware version before update',
  webOperation_firmware_packageCount_tableColumn_text: 'Firmware quantity',
  webOperation_firmware_packageName_tableColumn_text: 'Firmware name',
  webOperation_firmware_packageType_tableColumn_text: 'Firmware type',
  webOperation_firmware_packageVersion_tableColumn_text: 'Firmware version number',
  webOperation_firmware_releaseContent_tableColumn_text: 'Upgrade instructions',
  webOperation_firmware_releaseStatus_tableColumn_text: 'Firmware release status',
  webOperation_firmware_size_tableColumn_text: 'file volume',
  webOperation_firmware_status_tableColumn_text: 'Upgrade result',
  webOperation_firmware_resultTime_tableColumn_text: 'Upgrade result time',
  webOperation_firmware_succeed_tableColumn_text: 'Number of devices successfully upgraded',
  webOperation_firmware_total_tableColumn_text: 'Total number of devices',
  webOperation_firmware_upgradeContent_tableColumn_text: 'Upgrade content',
  webOperation_firmware_releaseDetail_common_text: 'Firmware release details',
  webOperation_group_conditionsAdd_button_text: 'Add group condition',
  webOperation_group_conditionsEdit_button_text: 'Edit group condition',
  webOperation_group_create_button_text: 'Add group',
  webOperation_group_edit_button_text: 'Edit group',
  webOperation_group_view_button_text: 'View group',
  webOperation_group_delete_modal_message:
    'Do you want to delete the group? The group cannot be retrieved after being deleted. Enter Yes in the box below for the second confirmation.',
  webOperation_group_delete_input_placeholder: 'Please enter "Yes" for secondary confirmation',
  webOperation_group_result_tableButton_text: 'Group results',
  webOperation_group_comments_tableColumn_text: 'Remarks',
  webOperation_group_conditionContent_tableColumn_text: 'Group conditionContent',
  webOperation_group_conditionContents_tableColumn_text: 'Grouping condition',
  webOperation_group_conditionRule_tableColumn_text: 'Rule of Judgment',
  webOperation_group_conditionRules_tableColumn_text: 'Rule of Judgment',
  webOperation_group_conditionType_tableColumn_text: 'Condition Type',
  webOperation_group_conditionValue_tableColumn_text: 'Condition value',
  webOperation_group_conditionValues_tableColumn_text: 'Condition Value',
  webOperation_group_groupName_tableColumn_text: 'Group name',
  webOperation_group_groupType_tableColumn_text: 'Group type',
  webOperation_group_deleteError_modal_message:
    'The current group rule is being used by the following tasks. You can delete the group only after all the tasks have been completed',
  webOperation_group_deleteError_modal_title: 'Delete failure message',
  webOperation_group_conditionValue_validator_message: 'Enter up to 5 values',
  webOperation_groupresult_deviceId_tableColumn_text: 'Device ID',
  webOperation_groupresult_onlineStatus_tableColumn_text: 'Device status',
  webOperation_groupresult_productModel_tableColumn_text: 'Product Model',
  webOperation_groupresult_userIds_tableColumn_text: 'Bind user ID',
  // General operation guide
  webOperation_guide_url_tableColumn_text: 'File link',
  webOperation_guide_desc_tableColumn_text: 'File description',
  webOperation_guide_descLangId_tableColumn_text: 'File description multilingual ID',
  webOperation_guide_description_tableColumn_text: 'Content description',
  webOperation_guide_descriptionLangId_tableColumn_text: 'Content description multilingual ID',
  webOperation_guide_file_tableColumn_text: 'Select file',
  webOperation_guide_format_tableColumn_text: 'File format',
  webOperation_guide_instanceId_tableColumn_text: 'file ID',
  webOperation_guide_name_tableColumn_text: 'File name',
  webOperation_guide_nameLangId_tableColumn_text: 'File name multilingual ID',
  webOperation_guide_size_tableColumn_text: 'file volume',
  webOperation_guide_type_tableColumn_text: 'Add mode',
  webOperation_guide_typeCode_tableColumn_text: 'Add mode',
  webOperation_guide_address_tableColumn_text: 'file address',
  webOperation_guide_typeCode_input_placeholder: 'Please select add mode',
  webOperation_guide_importTemplate_fileName_text: 'General Operation Guide Import Template',
  webOperation_guide_export_filename_text: 'General Operation Guide',
  webOperation_guide_import_common_text: 'Import General Operation Guide',
  Weboperation_guide_impor_modal_message:
    'Failed to import generic operation guidance, please check the following data:',
  // APP message
  webOperation_message_content_tableColumn_text: 'Message content',
  webOperation_message_endTime_tableColumn_text: 'Push end time',
  webOperation_message_id_tableColumn_text: 'message ID',
  webOperation_message_productModel_tableColumn_text: 'Product Model',
  webOperation_message_commodityModel_tableColumn_text: 'Item Model /Model #',
  webOperation_message_pushCount_tableColumn_text: 'Push number',
  webOperation_message_pushTime_tableColumn_text: 'Push time',
  webOperation_message_pushType_tableColumn_text: 'Push mode',
  webOperation_message_releaseStateCode_tableColumn_text: 'Release status',
  webOperation_message_result_tableColumn_text: 'Push results',
  webOperation_message_route_tableColumn_text: 'Route address',
  webOperation_message_startTime_tableColumn_text: 'Push start time',
  webOperation_message_title_tableColumn_text: 'Message title',
  webOperation_message_userId_tableColumn_text: 'User ID',
  webOperation_rn_downloadError_toast_message: 'RN failed to obtain download address',
  webOperation_faq_delete_modal_message:
    'After this FAQ is deleted, it cannot be recovered. Do you want to delete it?',
  webOperation_firmware_confirmRelease_modal_message:
    'Are you sure you want to release this firmware upgrade task? It cannot be deleted after publication',
  webOperation_firmware_ensureRelease_modal_message:
    'Currently there is a firmware upgrade task that has been released. After the new firmware upgrade task is approved, the current firmware upgrade task will be replaced by a new task. Please confirm whether to continue to apply for the release of this firmware upgrade task.',
  webOperation_firmware_release_modal_message:
    'Please confirm whether to apply for releasing this firmware upgrade task?',
  webOperation_firmware_stop_modal_message:
    'Are you sure you want to stop releasing this firmware upgrade task? Devices that have not completed the upgrade task after the release is stopped will not be able to continue the upgrade',
  webOperation_firmware_test_modal_message:
    'Are you sure that this firmware upgrade task has passed the test?',
  webOperation_firmware_nullify_modal_message:
    'Please confirm whether to cancel the firmware upgrade task? ',
  webOperation_firmware_nullify_modal_title: 'Cancel request confirmation ',
  webOperation_guide_delete_modal_message:
    'This file cannot be recovered after being deleted. Do you want to delete it?',
  webOperation_guide_ensureRelease_modal_message:
    'if you confirm to release this data? It will not be deleted after publication!',
  webOperation_guide_stop_modal_message: 'Are you sure you want to stop publishing this data?',
  webOperation_guide_test_modal_message: 'Are you sure this data passes the test?',
  webOperation_product_Off_modal_message:
    'Are you sure you want to remove this product? The product will not be visible or binding to users after removal',
  webOperation_product_delete_modal_message:
    'This standalone product cannot be recovered after being deleted. Do you want to delete?',
  webOperation_product_release_modal_message:
    'Are you sure you want to release this product? Will not be able to delete after Posting',
  webOperation_product_test_modal_message: 'Are you sure the product has passed the test?',
  webOperation_product_update_modal_message:
    'Are you sure you want to update the data for this product? The data of the original product cannot be retrieved after the update',
  webOperation_protocol_delete_modal_message:
    'Please confirm if you want to delete this protocol? This protocol cannot be retrieved after deletion. Enter Yes in the box below for the second confirmation.',
  webOperation_protocol_off_modal_message:
    'Are you sure you want to stop publishing this protocol? This agreement will expire when it is discontinued',
  webOperation_protocol_release_modal_message:
    'Are you sure you want to publish this protocol? Will not be able to delete after Posting',
  webOperation_protocol_test_modal_message: 'Do you confirm that this protocol passes the test?',
  webOperation_relativeProduct_delete_modal_message:
    'Are you sure you want to delete the associated product?',
  webOperation_rn_release_modal_message:
    'Are you sure you want to publish the RN package? Will not be able to delete after Posting',
  webOperation_rn_test_modal_message: 'Are you sure that the RN package passes the test?',
  webOperation_faq_delete_modal_title: 'Delete confirm',
  webOperation_firmware_ensureRelease_modal_title: 'firmware upgrade task release confirmation',
  webOperation_firmware_confirmRelease_modal_title: 'firmware upgrade task release confirmation',
  webOperation_firmware_release_modal_title: 'Release request confirmation',
  webOperation_firmware_stop_modal_title: 'Stop publishing confirmation',
  webOperation_firmware_test_modal_title: 'Firmware upgrade task test passed',
  webOperation_guide_delete_modal_title: 'Delete confirm',
  webOperation_guide_ensureRelease_modal_title: 'release confirmation',
  webOperation_guide_stop_modal_title: 'Stop publication confirmation',
  webOperation_guide_test_modal_title: 'Test passed confirmation',
  webOperation_product_Off_modal_title: 'Product removal confirmation',
  webOperation_product_refuse_modal_title: 'Rejected application',
  webOperation_product_release_modal_title: 'Product release Confirmation',
  webOperation_product_test_modal_title: 'Product test passed',
  webOperation_product_update_modal_title: 'Product update confirm',
  webOperation_protocol_off_modal_title: 'Stop publication confirmation',
  webOperation_protocol_release_modal_title: 'Protocol release Confirmation',
  webOperation_protocol_test_modal_title: 'Protocol test pass confirmation',
  webOperation_refuse_reason_modal_title: 'Reject Cause',
  webOperation_rn_release_modal_title: 'RN package release confirmation',
  webOperation_rn_test_modal_title: 'RN package test passed',
  webOperation_part_add_button_text: 'Add accessories',
  webOperation_part_import_common_text: 'Import',
  webOperation_partDetail_brief_input_text: 'Product short description',
  webOperation_partDetail_description_input_text: 'Product long description',
  webOperation_partDetail_manual_table_text: 'User manual',
  webOperation_partDetail_addFile_button_text: 'Add file',
  webOperation_partDetail_guide_table_text: 'Operation Guide',
  webOperation_partDetail_faq_table_text: 'FAQ',
  webOperation_partDetail_link_table_text: 'Buy link',
  webOperation_partDetail_spec_input_text: 'Technical Specification',
  webOperation_part_commodityModel_input_text: 'Item Model /Model #',
  webOperation_part_commodityModel_input_placeholder: 'Please enter the item Model /Model #',
  webOperation_part_desc_input_text: 'Remarks',
  webOperation_part_desc_input_placeholder: 'Please enter remarks',
  webOperation_file_desc_input_text: 'File description',
  webOperation_file_descLang_input_text: 'File description multilingual ID',
  webOperation_file_name_input_text: 'file name',
  webOperation_file_name_input_placeholder: 'Please enter file name',
  webOperation_file_nameLangId_input_text: 'File name multilingual ID',
  webOperation_file_id_input_text: 'file ID',
  webOperation_file_size_input_text: 'file volume',
  webOperation_file_type_input_text: 'File format',
  webOperation_file_link_input_text: 'file link',
  webOperation_file_link_input_placeholder: 'Please enter file link',
  webOperation_part_id_input_text: 'Accessory ID',
  webOperation_part_name_input_text: 'Part name',
  webOperation_part_name_input_placeholder: 'Please enter part name',
  webOperation_part_icon_select_text: 'accessory picture',
  webOperation_part_type1_select_text: 'accessory type1',
  webOperation_part_type1_select_placeholder: 'Please select accessory type1',
  webOperation_part_type2_select_text: 'accessory type2',
  webOperation_part_deleteLine1_modal_message:
    'Please confirm if you want to delete this link? The link cannot be retrieved after deletion.',
  webOperation_part_deleteLine2_modal_message:
    'Please enter Yes in the input box below for double confirmation.',
  webOperation_part_addLink_drawer_title: 'Add Purchase link',
  webOperation_part_updateLink_drawer_title: 'Edit purchase link',
  webOperation_part_link_validator_message:
    'Please enter content starting with http:// or https://',
  webOperation_part_linkId_input_text: 'Purchase link ID',
  webOperation_part_linkUrl_input_text: 'Purchase link',
  webOperation_part_linkUrl_input_placeholder: 'Please enter the purchase link',
  webOperation_part_linkId_tableColumn_text: 'Purchase link ID',
  webOperation_part_linkUrl_tableColumn_text: 'Purchase link',
  webOperation_part_commodityModel_tableColumn_text: 'Item Model /Model #',
  webOperation_part_desc_tableColumn_text: 'Remarks',
  webOperation_part_id_tableColumn_text: 'Accessory ID',
  webOperation_part_name_tableColumn_text: 'Accessory name',
  webOperation_part_option_tableColumn_text: 'operation',
  webOperation_part_partsIcon_tableColumn_text: 'accessory picture',
  webOperation_part_relate_tableColumn_text: 'Associated product',
  webOperation_part_type1_tableColumn_text: 'Accessory Type 1',
  webOperation_part_type2_tableColumn_text: 'Accessory Type 2',
  webOperation_part_maintenanceType_tableColumn_text: 'Maintenance type',
  webOperation_part_warranty_tableColumn_text: 'Maintenance period',
  webOperation_part_warrantyRemindFrequency_tableColumn_text: 'Maintenance remind',
  webOperation_part_delete_modal_message: 'This accessory cannot be recovered after being deleted',
  webOperation_part_relate_tableButton_text: 'Associated product',
  Weboperation_part_impor_modal_message:
    'Failed to import fittings table, please check the following data:',
  webOperation_introduction_deleteLine1_modal_message:
    'Do you want to delete the boot page? The boot page cannot be retrieved after deletion.',
  webOperation_introduction_deleteLine2_modal_message:
    'Enter Yes in the following text box for secondary confirmation.',
  webOperation_introduction_delete_input_placeholder: 'Please enter Yes before submitting',
  webOperation_product_import_common_text: 'Import standalone product',
  webOperation_product_applicationRelease_tableButton_text: 'Apply for publication',
  webOperation_product_canApplyOff_tableButton_text: 'Request removal',
  webOperation_product_canApplyRelease_tableButton_text: 'Apply for publication',
  webOperation_product_canApplyStopRelease_tableButton_text: 'Request to stop publishing',
  webOperation_product_canApplyUpdate_tableButton_text: 'Request update',
  webOperation_product_canCancelApplyOff_tableButton_text: 'Withdraw the takedown request',
  webOperation_product_canCancelApplyRelease_tableButton_text: 'Withdraw the release application',
  webOperation_product_canCancelApplyStopRelease_tableButton_text:
    'has withdrawn his application for stop issuing',
  webOperation_product_canCancelApplyUpdate_tableButton_text: 'Withdraw update request',
  webOperation_product_canNullifyApply_tableButton_text: 'Application cancelled ',
  webOperation_product_canNullifyApplyCancel_tableButton_text: 'Withdraw the annulled application ',
  webOperation_product_canNullifyPassed_tableButton_text: 'Confirm application is invalid ',
  webOperation_product_canNullifyRefused_tableButton_text: 'Application rejected ',
  webOperation_product_canViewNullifyRefusedReason_tableButton_text: 'invalid rejected reason',
  webOperation_product_canConfig_tableButton_text: 'RN publish configuration',
  webOperation_product_canDelete_tableButton_text: 'Delete',
  webOperation_product_canDownLoad_tableButton_text: 'Download',
  webOperation_product_canEnsureApply_tableButton_text: 'Confirm publication',
  webOperation_product_canEnsureOff_tableButton_text: 'Confirm removal',
  webOperation_product_canEnsureStopRelease_tableButton_text: 'Confirm stop publishing',
  webOperation_product_canEnsureTest_tableButton_text: 'Test passed',
  webOperation_product_canEnsureTestUpdate_tableButton_text: 'Test passed',
  webOperation_product_canEnsureUpdate_tableButton_text: 'Confirm update',
  webOperation_product_canRefuseApply_tableButton_text: 'Release rejected',
  webOperation_product_canRefuseOff_tableButton_text: 'Remove rejected',
  webOperation_product_canRefuseStopRelease_tableButton_text: 'Stop publish rejected',
  webOperation_product_canRefuseTest_tableButton_text: 'Test rejected',
  webOperation_product_canRefuseTestUpdate_tableButton_text: 'Test rejected',
  webOperation_product_canRefuseUpdate_tableButton_text: 'Update rejected',
  webOperation_product_canUpdate_tableButton_text: 'Edit',
  webOperation_product_canView_tableButton_text: 'Publish details',
  webOperation_product_canViewConfig_tableButton_text: 'RN publish details',
  webOperation_product_canViewRefuseOffReason_tableButton_text: 'Remove reject reason',
  webOperation_product_canViewRefuseReleaseReason_tableButton_text: 'release was dismissed reason',
  webOperation_product_canViewRefuseStopReleaseReason_tableButton_text:
    'stop issuing rejected reason',
  webOperation_product_canViewRefuseTestReason_tableButton_text: 'Test rejected reason',
  webOperation_product_canViewRefuseUpdateReason_tableButton_text: 'update was dismissed reason',
  webOperation_product_canViewRefuseUpdateTestReason_tableButton_text:
    'update test was dismissed reason',
  webOperation_product_confirmRelease_tableButton_text: 'Confirm release',
  webOperation_product_relative_button_text: 'Add associated product',
  webOperation_product_importTemplate_fileName_text: 'Standalone product template',
  webOperation_introduction_0_label_text: 'scan code',
  webOperation_introduction_none0_label_text: 'No scan guide currently',
  webOperation_introduction_step0_button_text: 'Add scan guide',
  webOperation_introduction_1_label_text: 'Network deployment',
  webOperation_introduction_none1_label_text: 'No network deployment step at present',
  webOperation_introduction_step1_button_text: 'Add network layout step',
  webOperation_introduction_2_label_text: 'Firmware upgrade',
  webOperation_introduction_none2_label_text: 'No firmware upgrade step currently',
  webOperation_introduction_step2_button_text: 'Add firmware upgrade step',
  webOperation_introduction_guide_label_text: 'guide',
  webOperation_introduction_img_select_text: 'picture',
  webOperation_introduction_langId_input_text: 'multilingual ID',
  webOperation_introduction_step_label_text: 'step',
  webOperation_introduction_text_input_text: 'copy',
  webOperation_introduction_text_input_placeholder: 'Please enter copy',
  webOperation_product_applyTime_tableColumn_text: 'Application time',
  webOperation_product_applyUserName_tableColumn_text: 'Applicant',
  webOperation_product_approveTime_tableColumn_text: 'Approval Time',
  webOperation_product_approveUserName_tableColumn_text: 'Approver',
  webOperation_product_brandId_tableColumn_text: 'Brand name',
  webOperation_product_categoryId_tableColumn_text: 'Category Name',
  webOperation_product_commodityModel_tableColumn_text: 'Item Model /Model #',
  webOperation_product_group_tableColumn_text: 'Test group',
  webOperation_product_id_tableColumn_text: 'PID',
  webOperation_product_model_tableColumn_text: 'Product Model',
  webOperation_product_networkModes_tableColumn_text: 'Networking mode',
  webOperation_product_operationRemark_tableColumn_text: 'Remarks',
  webOperation_product_productFullName_tableColumn_text: 'Product full name',
  webOperation_product_productIcon_tableColumn_text: 'Product image',
  webOperation_product_productId_tableColumn_text: 'PID',
  webOperation_product_productName_tableColumn_text: 'Product name',
  webOperation_product_productSnCode_tableColumn_text: 'SN code',
  webOperation_product_productType_tableColumn_text: 'Device type',
  webOperation_product_questionTemplate_tableColumn_text: 'Questionnaire template',
  webOperation_product_releaseStatus_tableColumn_text: 'Release status',
  webOperation_product_status_tableColumn_text: 'Product status',
  webOperation_product_technologyVersion_tableColumn_text: 'Technical Firmware version number',
  Weboperation_product_impor_modal_message:
    'Failed standalone product import, please check the following data:',
  webOperation_part_sort_button_text: 'Adjust the order',
  webOperation_part_sort_tableColumn_text: 'sort',
  webOperation_part_model_tableColumn_text: 'Fitting Type',
  webOperation_part_sort_drawer_title: 'Part order Adjustment',
  webOperation_part_edit_drawer_title: 'Edit accessory details',
  webOperation_part_view_drawer_title: 'Accessory Details',
  webOperation_part_warrantyUnit_tableColumn_text: 'day',
  webOperation_part_warrantyUnitHour_tableColumn_text: 'hour',
  webOperation_product_deviceProtocol_tabs_text: 'Device Protocol Management',
  webOperation_product_firmwareConfig_tabs_text: 'Firmware release configuration',
  webOperation_product_introduction_tabs_text: 'Boot page Management',
  webOperation_product_multicode_tabs_text: 'Multi-code Management',
  webOperation_product_partList_tabs_text: 'Parts list',
  webOperation_product_productInfo_tabs_text: 'Product Information Management',
  webOperation_product_rnConfig_tabs_text: 'RN publish configuration',
  webOperation_product_faq_tabs_text: 'Aftermarket Content Management',
  webOperation_protocol_create_button_text: 'Add a protocol',
  webOperation_protocol_detail_drawer_title: 'Protocol details',
  webOperation_protocol_edit_drawer_title: 'Edit a protocol',
  webOperation_protocol_new_drawer_title: 'Add a new version protocol',
  webOperation_protocol_recreate_common_text: 'Add a new version protocol',
  webOperation_protocol_addNewVersion_tableButton_linkText: 'Add new version',
  webOperation_protocol_deleteLine1_modal_message:
    'Please confirm if you want to delete this protocol? This protocol cannot be retrieved after deletion.',
  webOperation_protocol_deleteLine2_modal_message:
    'Enter Yes in the lower text box for secondary confirmation.',
  webOperation_protocol_delete_input_placeholder: 'Enter Yes before committing',
  webOperation_protocol_condition_input_text: 'Check condition',
  webOperation_protocol_content_input_text: 'Copy content',
  webOperation_protocol_content_input_placeholder: 'Enter copy content',
  webOperation_protocol_create_drawer_title: 'Add a protocol',
  webOperation_protocol_id_input_text: 'Protocol ID',
  webOperation_protocol_name_input_text: 'Protocol name',
  webOperation_protocol_name_input_placeholder: 'Enter a protocol name',
  webOperation_protocol_type_select_text: 'protocol type',
  webOperation_protocol_type_select_placeholder: 'Please select the protocol type',
  webOperation_protocol_update_drawer_title: 'Edit protocol',
  webOperation_protocol_version_input_text: 'Protocol version number',
  webOperation_protocol_version_validator_message:
    'Please enter a correct version number such as 1.0.0',
  webOperation_protocol_version_input_placeholder: 'Enter the protocol version number',
  webOperation_protocol_appAgreementContentId_tableColumn_text: 'Protocol ID',
  webOperation_protocol_content_tableColumn_text: 'Protocol content',
  webOperation_protocol_contentId_tableColumn_text: 'Multilingual ID of protocol content',
  webOperation_protocol_contentLangId_tableColumn_text: 'Multilanguage ID of protocol content',
  webOperation_protocol_id_tableColumn_text: 'Protocol ID',
  webOperation_protocol_langId_tableColumn_text: 'Multilanguage ID of protocol name',
  webOperation_protocol_name_tableColumn_text: 'Protocol name',
  webOperation_protocol_option_tableColumn_text: 'operation',
  webOperation_protocol_statusCode_tableColumn_text: 'Release status',
  webOperation_protocol_title_tableColumn_text: 'Protocol name',
  webOperation_protocol_titleLangId_tableColumn_text: 'Protocol name Multilanguage ID',
  webOperation_protocol_type_tableColumn_text: 'Protocol type',
  webOperation_protocol_typeCode_tableColumn_text: 'Protocol type',
  webOperation_protocol_version_tableColumn_text: 'Protocol version number',
  webOperation_protocol_businessType_tableColumn_text: 'Applicable App',
  webOperation_refuse_info_tableColumn_text:
    'Your application is rejected for the following reasons',
  webOperation_release_date_tableColumn_text: 'date',
  webOperation_release_end_tableColumn_text: 'End time',
  webOperation_release_operationUpdateContent_tableColumn_text: 'Operation Update Notes',
  webOperation_release_prdGroup_tableColumn_text: 'Production group',
  webOperation_release_remark_tableColumn_text: 'Reject reason',
  webOperation_release_start_tableColumn_text: 'Start time',
  webOperation_release_testGroup_tableColumn_text: 'Test group',
  webOperation_release_time_tableColumn_text: 'Time',
  webOperation_release_zone_tableColumn_text: 'Time zone',
  webOperation_rn_androidVersionMin_tableColumn_text: 'Android minimum compatible version number',
  webOperation_rn_appId_tableColumn_text: 'APP ID',
  webOperation_rn_appName_tableColumn_text: 'APP name',
  webOperation_rn_iosVersionMin_tableColumn_text: 'ios minimum compatible version number',
  webOperation_rn_releaseStateCode_tableColumn_text: 'RN package release status',
  webOperation_rn_releaseStatusCode_tableColumn_text: 'Release status',
  webOperation_rn_rnName_tableColumn_text: 'RN package name',
  webOperation_rn_rnVersion_tableColumn_text: 'RN package version number',
  webOperation_rn_startZone_tableColumn_text: 'Time zone',
  webOperation_rn_stateCode_tableColumn_text: 'RN package development status',
  webOperation_rn_updateContent_tableColumn_text: 'Update contents',
  webOperation_task_detail_common_text: 'Task details',
  webOperation_template_content_tableColumn_text: 'Template content',
  webOperation_template_id_tableColumn_text: 'Template ID',
  webOperation_template_name_tableColumn_text: 'Template name',
  webOperation_template_title_tableColumn_text: 'Template title',
  webOperation_template_type_tableColumn_text: 'Template type',
  webOperation_template_messageDisplayType_tableColumn_text: 'Display type',
  webOperation_template_used_tableColumn_text: 'Number of references',
  webOperation_template_disabled_toast_text: 'Template is in use and cannot be edited',
  webOperation_template_notDeleted_toast_text: 'The template is in use and cannot be deleted',

  webOperation_upgrade_result_common_text: 'Upgrade result',
  webOperation_user_appPresenceCode_tableColumn_text: 'APP online status',
  webOperation_user_appTypeCode_tableColumn_text: 'APP type',
  webOperation_user_appVersion_tableColumn_text: 'APP version number',
  webOperation_user_bindDeviceBrandId_tableColumn_text: 'Bound device brand',
  webOperation_user_bindDeviceCategoryId_tableColumn_text: 'Bound Device category',
  webOperation_user_bindDeviceId_tableColumn_text: 'Bound device ID',
  webOperation_user_bindDeviceSn_tableColumn_text: 'Bound device SN',
  webOperation_user_commodityModel_tableColumn_text: 'Bound device item Model /Model #',
  webOperation_user_deviceSource_tableColumn_text: 'Device source',
  webOperation_user_shareType_tableColumn_text: 'Main & sub account',
  webOperation_user_email_tableColumn_text: 'mailbox',
  webOperation_user_ip_tableColumn_text: 'IP address',
  webOperation_user_itemCode_tableColumn_text: 'List name',
  webOperation_user_lastLoginTime_tableColumn_text: 'Last login time',
  webOperation_user_model_tableColumn_text: 'Bound device product model',
  webOperation_user_phoneModel_tableColumn_text: 'Mobile phone model',
  webOperation_user_phoneOsVersion_tableColumn_text: 'System version number',
  webOperation_user_registerTime_tableColumn_text: 'Registration time',
  webOperation_user_userId_tableColumn_text: 'User ID',
  webOperation_user_userSourceCode_tableColumn_text: 'user source',
  webOperation_user_userTypeCode_tableColumn_text: 'User type',
  webOperation_user_export_fileName_text: 'APP user',
  webOperation_userdevice_export_fileName_text: 'APP userdevice',
  // APP message
  webOperation_appMessage_sysMsgId_tableColumn_text: 'Message ID',
  webOperation_appMessage_title_tableColumn_text: 'Message title',
  webOperation_appMessage_content_tableColumn_text: 'Message content',
  webOperation_appMessage_pushTypeCodes_tableColumn_text: 'Push mode',
  webOperation_appMessage_pushRateCode_tableColumn_text: 'Push frequency',
  webOperation_appMessage_pushTime_tableColumn_text: 'Push time',
  webOperation_appMessage_startTime_tableColumn_text: 'Push start time',
  webOperation_appMessage_endTime_tableColumn_text: 'Push end time',
  webOperation_appMessage_statusCode_tableColumn_text: 'Release status',
  webOperation_appMessage_rutePath_tableColumn_text: 'Route address',
  webOperation_appMessage_test_modal_title: 'Message test passed confirmation',
  webOperation_appMessage_test_modal_message: 'Are you sure this message passes the test?',
  webOperation_appMessage_ensureRelease_modal_title: 'news confirmed',
  webOperation_appMessage_ensureRelease_modal_message:
    'if you confirm to release the news? Will not be able to delete after Posting',
  webOperation_appMessage_stop_modal_title: 'Stop publication confirmation',
  webOperation_appMessage_stop_modal_message:
    'Are you sure you want to stop publishing this message? Will stop pushing messages immediately after stopping Posting',
  webOperation_appMessage_delete_modal_message:
    'Do you want to delete this message? The message cannot be retrieved after deletion. Enter Yes in the box below for the second confirmation.',
  // Product management configuration
  webOperation_permission_setting_drawer_title: 'Operation Permission Configuration',
  webOperation_permission_edit_tabs_text: 'Edit operation',
  webOperation_permission_approve_tabs_text: 'Release audit operation',
  webOperation_permission_test_tabs_text: 'Test audit operation',
  webOperation_permission_delete_modal_message:
    'Please confirm if you want to delete the operation permissions of the person?',
  webOperation_permission_add_button_text: 'Add people',
  webOperation_permission_product_tabs_text: 'Product Management',
  webOperation_permission_ota_tabs_text: 'Firmware Management',
  webOperation_permission_rn_tabs_text: 'RN Panel Management',
  webOperation_employee_employeeNumber_tableColumn_text: 'Job ID',
  webOperation_employee_userName_tableColumn_text: 'Platform user',
  webOperation_employee_organizationName_tableColumn_text: 'Organization',
  // Help Center
  webOperation_help_id_tableColumn_text: 'Help ID',
  webOperation_help_title_tableColumn_text: 'title',
  webOperation_help_title_input_placeholder: 'Please enter title',
  webOperation_help_titleLangId_tableColumn_text: 'Multilingual ID',
  webOperation_help_from_tableColumn_text: 'data source',
  webOperation_help_read_tableColumn_text: 'Read volume',
  webOperation_help_like_tableColumn_text: 'Number of likes',
  webOperation_help_appShowStatus_tableColumn_text: 'APP display status',
  webOperation_help_sync_tableColumn_text: 'Synchronize time',
  webOperation_help_model_tableColumn_text: 'Item type',
  webOperation_help_model_input_rule: 'Please enter the item model number',
  webOperation_help_model_input_placeholder: 'Item models separated by semicolons',
  webOperation_help_answer_tableColumn_text: 'Help content',
  webOperation_help_answerRule_tableColumn_text: 'Please enter help content',
  webOperation_help_readCount_tableColumn_text: 'Read volume',
  webOperation_help_praiseCount_tableColumn_text: 'Likes',
  webOperation_help_appShowCode_tableColumn_text: 'APP display status',

  webOperation_help_show_tableColumn_text: 'Display',
  webOperation_help_hide_tableColumn_text: 'hide',

  webOperation_help_import_common_text: 'Import',
  webOperation_help_update_button_text: 'Update',
  webOperation_help_create_button_text: 'Add Help',
  webOperation_help_update_drawer_title: 'Edit Help',
  webOperation_help_view_drawer_title: 'Help details',

  Weboperation_help_impor_modal_message: 'Failed to help import, please check the following data:',

  webOperation_help_deleteLine1_modal_message: 'This file cannot be recovered after being deleted',
  webOperation_help_deleteLine2_modal_message: 'Confirm delete?',
  webOperation_help_importTemplate_fileName_text: 'Help import template',
  webOperation_appUpgrade_appName_tableColumn_text: 'APP Name',
  webOperation_appUpgrade_id_tableColumn_text: 'Version ID',
  webOperation_appUpgrade_version_tableColumn_text: 'Version number',
  webOperation_appUpgrade_versionName_tableColumn_text: 'Version name',
  webOperation_appUpgrade_changelog_tableColumn_text: 'Version update content',
  webOperation_appUpgrade_changelogLangId_tableColumn_text:
    'Version update content multi-language ID',

  webOperation_appUpgrade_deleteLine1_modal_message: 'Are you sure you want to delete this data?',
  webOperation_appUpgrade_deleteLine2_modal_message: 'After deletion, it cannot be restored!',

  webOperation_appUpgrade_appName_select_placeholder: 'Please select APP name',
  webOperation_appUpgrade_version_input_text: 'Version number',
  webOperation_appUpgrade_version_input_placeholder: 'Please enter the version number',
  webOperation_appUpgrade_version_validator_message:
    'Please enter the correct version number, for example, 1.0.0',
  webOperation_appUpgrade_name_input_text: 'Version name',
  webOperation_appUpgrade_name_input_placeholder: 'Please enter version name',
  webOperation_appUpgrade_changelog_input_text: 'Version update content',
  webOperation_appUpgrade_changelog_input_placeholder: 'Please enter version update content',

  webOperation_suggestion_id_tableColumn_text: 'Suggestion ID',
  webOperation_suggestion_title_tableColumn_text: 'Processing suggestion_titles',
  webOperation_suggestion_content_tableColumn_text: 'Processing suggestions',
  webOperation_suggestion_extra_tableColumn_text: 'Additional content',
  webOperation_suggestion_name_tableColumn_text: 'Message names',
  Weboperation_option_tablecolumn_text: 'operation',

  webOperation_suggestion_import_common_text: 'Import',
  webOperation_suggestion_create_button_text: 'Adding processing suggestions',

  webOperation_suggestion_create_drawer_title: 'Adding processing suggestions',
  webOperation_suggestion_update_drawer_title: 'Editing Processing Suggestions',
  webOperation_suggestion_view_drawer_title: 'Processing suggestion_detail',

  webOperation_suggestion_id_input_text: 'Suggestion ID',
  webOperation_suggestion_title_input_text: 'Processing suggestion_title',
  webOperation_suggestion_title_input_placeholder: 'Input processing suggestion title',
  webOperation_suggestion_content_input_text: 'Processing suggestions',
  webOperation_suggestion_content_input_placeholder: 'Input processing suggestions',
  webOperation_suggestion_extra_input_text: 'Additional content',
  webOperation_suggestion_extra_input_placeholder:
    'Enter the link address starting with http:// or https://',
  webOperation_suggestion_name_input_text: 'Message name',

  webOperation_suggestion_deleteLine1_modal_message:
    "The file can't be restored after being deleted ",
  webOperation_suggestion_deleteLine2_modal_message: 'Confirm deletion?',
  Weboperation_importion_impor_modal_message:
    'Processing suggestion import failed, check the following data:',
  webOperation_suggestion_importTemplate_fileName_text: 'Processing suggestion template',

  webOperation_message_type_tableColumn_text: 'Message type',
  webOperation_message_pushMethod_tableColumn_text: 'Push mode',
  webOperation_message_successCount_tableColumn_text: 'Number of successes',
  webOperation_message_failCount_tableColumn_text: 'Number of failures',
  webOperation_message_export_filename_text: 'Message record',
  webOperation_messageDetail_export_filename_text: 'Message record details',
  webOperation_message_succeed_dictionnary_text: 'Success',
  webOperation_message_failed_dictionnary_text: 'Fail',
  webOperation_message_recordId_tableColumn_text: 'Message record ID',
  webOperation_message_targetUserId_tableColumn_text: 'Push user ID',
  webOperation_message_account_tableColumn_text: 'Push user account',
  webOperation_product_brief_input_text: 'Product short Description',
  webOperation_product_description_input_text: 'Product length description',
  webOperation_product_spec_input_text: 'Technical Specification',

  webOperation_product_faqSort_tableColumn_text: 'sort',
  webOperation_product_faqId_tableColumn_text: 'Problem ID',
  webOperation_product_faqType_tableColumn_text: 'Problem type',
  webOperation_product_question_tableColumn_text: 'Question',
  webOperation_product_questionLangId_tableColumn_text: 'Question multilingual ID',
  webOperation_product_solution_tableColumn_text: 'Solution',
  webOperation_product_solutionLangId_tableColumn_text: 'Solution multilingual ID',
  webOperation_product_deleteLine1_modal_message: 'After deleted, it will not be recoverable!',
  webOperation_product_deleteLine2_modal_message: 'Are you sure to delete?',
  webOperation_product_addFaq_button_text: 'Add FAQ',
  webOperation_product_editFaq_modal_title: 'Edit FAQ',
  webOperation_product_viewFaq_modal_title: 'FAQ details',
  webOperation_product_sortFaq_button_text: 'Sort FAQ',

  webOperation_product_faqType_select_placeholder: 'Please select the problem type',
  webOperation_product_question_input_placeholder: 'Please enter question',
  webOperation_product_solution_input_placeholder: 'Input solution',

  webOperation_product_faq_table_text: 'FAQ:',
  webOperation_product_manual_table_text: 'User Manual',
  webOperation_product_guide_table_text: 'Operation Guide',

  webOperation_file_url_tableColumn_text: 'file address',

  webOperation_file_view_drawer_title: 'View file',
  webOperation_file_edit_drawer_title: 'Edit file',
  webOperation_file_create_drawer_title: 'Add file',

  webOperation_file_add_button_title: 'Add file',
  webOperation_file_add_tooltip_title: 'Only:',
  webOperation_guide_sort_button_title: 'Sort operation Guide',

  webOperation_sms_chargingId_tableColumn_text: 'Billing ID',
  webOperation_sms_msgId_tableColumn_text: 'message ID',
  webOperation_sms_msgTitle_tableColumn_text: 'Message title',
  webOperation_sms_chargingStartTime_tableColumn_text: 'First send time',
  webOperation_sms_chargingEndTime_tableColumn_text: 'Last sent time',
  webOperation_sms_msgType_tableColumn_text: 'Message type',
  webOperation_sms_commodityModel_tableColumn_text: 'Item Model /Model #',
  webOperation_sms_totalSend_tableColumn_text: 'Total number of sent',
  webOperation_sms_createTime_tableColumn_text: 'Create time',
  webOperation_sms_export_fileName_text: 'Short message Usage Statistics',
  webOperation_sms_msgRecordId_tableColumn_text: 'Message record ID',
  webOperation_sms_toUserId_tableColumn_text: 'Push user ID',
  webOperation_sms_toUserPhoneNumber_tableColumn_text: "Push user's phone number",
  webOperation_sms_toUserRegion_tableColumn_text: 'Push user home region',
  webOperation_sms_pushTime_tableColumn_text: 'Push time',
  webOperation_sms_msgContent_tableColumn_text: 'Message content',
  webOperation_sms_pushType_tableColumn_text: 'Push mode',
  webOperation_sms_detail_modal_title: 'Short message usage details',
  webOperation_sms_pushStartTime_tableColumn_text: 'Push start time',
  webOperation_sms_pushEndTime_tableColumn_text: 'Push end time',
  webOperation_flow_totalPrice_tableColumn_text: 'Total consumption',
  webOperation_flow_deviceCount_tableColumn_text: 'Number of devices',
  webOperation_flow_chargingStartTime_tableColumn_text: 'Billing start Time',
  webOperation_flow_chargingEndTime_tableColumn_text: 'Accounting end Time',
  webOperation_flow_iccid_tableColumn_text: 'SIM ID',
  webOperation_flow_totalDevicePrice_tableColumn_text: 'Total consumption per device',
  webOperation_flow_totalFlow_tableColumn_text: 'Total Traffic (MB)',
  webOperation_flow_singleTotalFlow_tableColumn_text: 'Total Traffic on a single Device (MB)',
  webOperation_flow_deviceId_tableColumn_text: 'Device ID',
  webOperation_flow_detail_modal_title: 'Traffic charge details',

  webOperation_device_export_filename_text: 'Device list',
  webOperation_device_deviceId_tableColumn_text: 'Device ID',
  webOperation_device_sn_tableColumn_text: 'SN',
  webOperation_device_pid_tableColumn_text: 'PID',
  webOperation_device_categoryId_tableColumn_text: 'Category name ',
  webOperation_device_categoryName_tableColumn_text: 'Category name ',
  webOperation_device_brandName_tableColumn_text: 'Brand name ',
  webOperation_device_brandId_tableColumn_text: 'Brand name ',
  webOperation_device_productModel_tableColumn_text: 'Product model ',
  webOperation_device_commodityModel_tableColumn_text: 'Product Model /Model #',
  webOperation_device_productType_tableColumn_text: 'Device type ',
  webOperation_device_devicePhoneNum_tableColumn_text: 'Register mobile number ',
  webOperation_device_isOnline_tableColumn_text: 'Online status ',
  webOperation_device_status_tableColumn_text: 'Device status ',
  webOperation_device_currentBindBusinessType_tableColumn_text: 'Currently bound APP',
  webOperation_device_currentBindEgoUserId_tableColumn_text: 'Currently bound EGO user ID',
  webOperation_device_currentBindFleetCompanyId_tableColumn_text: 'Currently bound Fleet tenant ID',
  webOperation_device_usageStatus_tableColumn_text: 'Use status ',
  webOperation_device_firstBindBusinessType_tableColumn_text: 'Bind APP for the first time ',
  webOperation_device_firstBindEgoUserId_tableColumn_text: 'Bind EGO user ID for the first time ',
  webOperation_device_firstBindFleetCompanyId_tableColumn_text:
    'Bind the Fleet tenant ID for the first time ',
  webOperation_device_firstBindFleetUserId_tableColumn_text:
    'BindFleet Operation account ID for the first time ',
  webOperation_device_activationTime_tableColumn_text: 'Activation time ',
  webOperation_device_firstBindTime_tableColumn_text: 'First binding time ',
  webOperation_device_lastLoginTime_tableColumn_text: 'Last online time ',

  webOperation_device_nickName_tableColumn_text: 'Device nickname ',
  webOperation_device_productSnCode_tableColumn_text: 'SN code',
  webOperation_device_communicateMode_tableColumn_text: 'Networking mode ',
  webOperation_device_customVersion_tableColumn_text: 'User firmware version number ',
  webOperation_device_detail_tabs_text: 'Device details ',
  webOperation_device_part_table_text: 'Assembly parts ',
  webOperation_device_upgrade_table_text: 'Firmware upgrade record ',

  webOperation_part_componentName_tableColumn_text: 'Assembly part name ',
  webOperation_part_componentNo_tableColumn_text: 'Assembly part number ',
  webOperation_part_componentType_tableColumn_text: 'Assembly part type ',
  webOperation_part_componentVersion_tableColumn_text: 'Current firmware version number ',
  webOperation_device_otaUpgradeHistory_common_text: 'Firmware upgrade record ',

  webOperation_upgrade_newVersion_tableColumn_text: 'Target firmware version number ',
  webOperation_upgrade_oldVersion_tableColumn_text: 'Original firmware version number ',
  webOperation_upgrade_status_tableColumn_text: 'Upgrade result ',
  webOperation_upgrade_upgradeTime_tableColumn_text: 'Upgrade time ',
  webOperation_upgrade_userId_tableColumn_text: 'Operation user ID',
  webOperation_device_disable_modal_title: 'Device enable',
  webOperation_device_normal_modal_title: 'Device disabled',
  webOperation_device_disable_modal_message: 'Are you sure to enable the device?',
  webOperation_device_normal_modal_message:
    'Please confirm whether you want to discontinue access to this device? After the device is disabled, it cannot be controlled by the users bound to the device. This is a sensitive operation, please confirm that the owner of the equipment has been informed',
  webOperation_company_companyId_tableColumn_text: 'Tenant ID',
  webOperation_company_companyName_tableColumn_text: 'Tenant name',
  webOperation_company_adminId_tableColumn_text: 'Administrator ID',
  webOperation_company_adminEmail_tableColumn_text: 'Administrator mailbox',
  webOperation_company_adminFirstName_tableColumn_text: 'Administrator First name',
  webOperation_company_adminLastName_tableColumn_text: 'Administrator Last name',
  WebOperation_company_adminName_tableColumn_text: 'Administrator name',
  webOperation_company_companyState_tableColumn_text: 'Status',
  webOperation_company_registerTime_tableColumn_text: 'Tenant registration time',
  webOperation_company_companyCountry_tableColumn_text: "Tenant's country",
  webOperation_company_companyAddress_tableColumn_text: 'Tenant address',
  webOperation_company_companyPost_tableColumn_text: 'Tenant postcode',
  webOperation_company_changeTime_tableColumn_text: 'Change time',
  webOperation_company_detail_drawer_text: 'Tenant details',
  webOperation_company_detail_button_text: 'Tenant details',
  webOperation_company_cancel_button_text: 'logout',
  webOperation_company_viewUpdate_button_text: 'Administrator change log',
  webOperation_company_adminHistory_drawer_text: 'Administrator change record',
  webOperation_company_stop_modal_title: 'Disable tenant account',
  webOperation_company_enable_modal_title: 'Enable Tenant account',
  webOperation_company_cancel_modal_title: 'Cancel the tenant account',
  webOperation_company_sensitive_message_text:
    'This operation is sensitive, make sure the tenant administrator is informed',
  webOperation_company_confirmToStopLine1_message_text:
    'Do you want to disable the permission of the tenant account?',
  webOperation_company_confirmToStopLine2_message_text:
    'After the tenant is disabled, all employees under the tenant cannot log in to the Fleet Service platform',
  webOperation_company_confirmToCancelLine1_message_text:
    'Do you want to cancel the user permission of the tenant account?',
  webOperation_company_confirmToCancelLine2_message_text:
    'All devices and employees of this tenant will be unbound after logout',
  webOperation_company_confirmToInputPassword_message_text:
    'Please enter your login password again',
  webOperation_company_password_input_placeholder: 'Your login password',
  webOperation_company_confirmToEnable_message_text:
    'Please confirm whether the user rights of the tenant account are enabled.',
  // Relationship between the tenant and the device
  webOperation_companyDevice_companyId_tableColumn_text: 'Tenant ID',
  webOperation_companyDevice_companyName_tableColumn_text: 'Tenant name',
  webOperation_companyDevice_count_tableColumn_text: 'Number of bound devices',
  webOperation_companyDevice_deviceId_tableColumn_text: 'Bound device ID',
  webOperation_companyDevice_deviceSn_tableColumn_text: 'Bound device SN',
  webOperation_companyDevice_brandId_tableColumn_text: 'Bound device brand ',
  webOperation_companyDevice_categoryCode_tableColumn_text: 'Bound device category',
  webOperation_companyDevice_model_tableColumn_text: 'Bound device item Model /Model #',
  // Tenant staff
  webOperation_companyEmployee_userId_tableColumn_text: 'Account ID',
  webOperation_companyEmployee_userEmail_tableColumn_text: 'Employee email',
  webOperation_companyEmployee_userFirstName_tableColumn_text: 'Employee First name',
  webOperation_companyEmployee_userLastName_tableColumn_text: 'Employee Last name',
  webOperation_companyEmployee_userName_tableColumn_text: 'Employee name',
  webOperation_companyEmployee_userRole_tableColumn_text: 'Employee role',
  webOperation_companyEmployee_companyId_tableColumn_text: 'Owning tenant ID',
  webOperation_companyEmployee_companyName_tableColumn_text: 'Owning tenant name',
  webOperation_companyEmployee_userSourceType_tableColumn_text: 'Employee account source',
  webOperation_companyEmployee_invitationEmail_tableColumn_text: 'Inviter email',
  webOperation_companyEmployee_userState_tableColumn_text: 'Status',
  webOperation_companyEmployee_isEnabled_tableColumn_text: 'User enabled status',
  webOperation_companyEmployee_activeTime_tableColumn_text: 'Employee account activation time',
  webOperation_companyEmployee_registerTime_tableColumn_text: 'Employee account registration time',
  webOperation_companyEmployee_companyCountry_tableColumn_text: "Tenant's country",
  webOperation_companyEmployee_companyAddress_tableColumn_text: 'Tenant address',
  webOperation_companyEmployee_companyPost_tableColumn_text: 'Tenant postcode',
  webOperation_companyEmployee_detail_common_text: 'Employee account details',
  webOperation_companyEmployee_stop_modal_title: 'Disable employee account',
  webOperation_companyEmployee_enable_modal_title: 'Enable Employee Account',
  webOperation_companyEmployee_sensitive_message_text:
    'This operation is sensitive, please make sure the employee is informed',
  webOperation_companyEmployee_confirmToStopLine1_message_text:
    'Do you want to disable access to this employee account?',
  webOperation_companyEmployee_confirmToStopLine2_message_text:
    'After deactivation, the employee account will not be able to log in to the Fleet Service platform',
  webOperation_companyEmployee_confirmToEnable_message_text:
    'Do you want to enable access to this employee account?',

  webOperation_companyDealer_country_tableColumn_text: 'Country',
  webOperation_companyDealer_state_tableColumn_text: 'state',
  webOperation_companyDealer_city_tableColumn_text: 'City',
  webOperation_companyDealer_lat_tableColumn_text: 'latitude',
  webOperation_companyDealer_lng_tableColumn_text: 'Longitude',

  webOperation_companyDealer_title_tableColumn_text: 'Title',
  webOperation_companyDealer_addressOne_tableColumn_text: 'Address_one',
  webOperation_companyDealer_addressTwo_tableColumn_text: 'Address_two',
  webOperation_companyDealer_town_tableColumn_text: 'Town',
  webOperation_companyDealer_region_tableColumn_text: 'Region',
  webOperation_companyDealer_postcode_tableColumn_text: 'Postcode',
  webOperation_companyDealer_phoneNumber_tableColumn_text: 'Phone_number',
  webOperation_companyDealer_email_tableColumn_text: 'Email',
  webOperation_companyDealer_geolocation_tableColumn_text: 'Geolocation',
  webOperation_companyDealer_langCode_tableColumn_text: 'Langcode',
  webOperation_companyDealer_premium_tableColumn_text: 'Premium',
  webOperation_companyDealer_proXRangeSeller_tableColumn_text: 'pro_x_range_seller',
  webOperation_companyDealer_rideOnSeller_tableColumn_text: 'Ride_on_seller',
  webOperation_companyDealer_ad_tableColumn_text: 'AD',
};
