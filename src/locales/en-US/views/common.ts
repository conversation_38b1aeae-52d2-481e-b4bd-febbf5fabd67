export default {
  webCommon_page_remark_common_text: 'remark',
  webCommon_page_remark_input_placeholder: 'Note information',
  webCommon_page_remark_input_rule: 'Please input remark rule',
  webCommon_page_langId_common_text: 'multilingual ID',
  webCommon_page_deviceId_tableColumn_text: 'Device ID',
  webCommon_page_deviceName_tableColumn_text: 'Device name',
  webCommon_page_sort_tableColumn_text: 'sort',
  webCommon_page_index_tableColumn_text: 'serial number',
  webCommon_page_statusCode_tableColumn_text: 'Publication status',
  webCommon_page_view_common_text: 'View',
  webCommon_page_edit_common_text: 'Edit',
  webCommon_page_create_button_text: 'Create',
  webCommon_page_add_button_text: 'Add',
  webCommon_page_delete_tableButton_linkText: 'Delete',
  webCommon_page_release_tableButton_linkText: 'Release',
  webCommon_page_withdraw_tableButton_linkText: 'Withdraw',
  webCommon_page_batchDelete_tableButton_linkText: 'Batch Delete',
  webCommon_page_detail_common_text: 'Details',
  webCommon_page_cancel_button_text: 'Cancel',
  webCommon_page_close_button_text: 'Close',
  webCommon_page_confirm_button_text: 'OK',
  webCommon_page_search_button_text: 'Search',
  webCommon_page_reset_button_text: 'Reset',
  webCommon_page_expand_button_text: 'Expand',
  webCommon_page_collapse_button_text: 'Collapse',
  webCommon_page_export_button_text: 'Export',
  webCommon_page_import_button_text: 'Import',
  webCommon_page_config_button_text: 'Configuration',
  webCommon_page_disable_tableButton_linkText: 'Disable',
  webCommon_page_enable_tableButton_linkText: 'Enable',
  webCommon_page_copy_button_linkText: 'Copy',
  webCommon_page_copyMessage_button_linkText: 'Copy message',
  webCommon_page_copyResult_button_text: 'Copy the result',
  webCommon_page_pushResult_tableButton_linkText: 'Push results',
  webCommon_page_download_common_linkText: 'Download',
  webCommon_page_browse_common_linkText: 'brower',
  webCommon_page_more_button_linkText: 'more',
  webCommon_page_option_tableColumn_text: 'action',
  webCommon_page_downloadTemplate_button_text: 'Template download',
  webCommon_page_confirmToDelete_modal_title: 'Delete confirm',
  webCommon_page_delete_modal_message:
    'After deletion, it is unrecoverable! Do you want to delete?',
  webCommon_page_copySuccessed_toast_text: 'Copy succeeded',
  webCommon_page_operateSuccessed_toast_text: 'Operation succeeded',
  webCommon_page_operateFailed_toast_text: 'Operation failed',
  webCommon_page_downloading_toast_text: 'in process',
  webCommon_page_importSuccessed_toast_text: 'Import succeeded',
  webCommon_page_importFailed_toast_text: 'Import failed,please retry again',
  webCommon_page_exportFailed_modal_title: 'Export failure',
  webCommon_page_exportFailed_modal_message: 'Export failed, please try again',
  webCommon_page_confirmText_button_text: 'OK',
  webCommon_page_file_select_placeholder: 'Please select file',
  webCommon_page_selectToImport_button_text: 'Please select file',
  webCommon_page_delete_input_placeholder: 'Please enter "Yes" for secondary confirmation',
  webCommon_page_uploadImageType_validator_message: 'Please upload PNG images',
  webCommon_page_uploadError_toast_message: 'File format error',
  webCommon_page_uploadImageSize_validator_message: 'Upload image size cannot exceed 800K',
  webCommon_page_uploadSize_validator_message: 'Picture size is incorrect',
  webCommon_page_createBy_tableColumn_text: 'Creator',
  webCommon_page_createTime_tableColumn_text: 'Create time',
  webCommon_page_updateBy_tableColumn_text: 'Modifier',
  webCommon_page_updateTime_tableColumn_text: 'Modify time',
  webCommon_page_noAccess_toast_text: 'Insufficient permission',
  webCommon_page_digit_validator_message: 'Please enter a number',
  webCommon_page_str36_validator_message:
    'Enter 1 to 36 characters, including uppercase and lowercase letters, digits, and _-./? # special character',
  webCommon_page_str128_validator_message:
    'Enter 1 to 128 characters, including uppercase and lowercase letters, digits, and _-./? # special character',
  webCommon_page_en128_validator_message:
    'Please enter 1 to 128 characters. Only uppercase and lowercase letters, digits, and underscores are supported',
  webCommon_page_len128_validator_message: 'Please enter 1~128 characters',
  webCommon_page_len300_validator_message: 'Please enter 1~300 characters',
  webCommon_page_len500_validator_message: 'Please enter 1~500 characters',
  webCommon_page_len5000_validator_message: 'Please enter 1~5000 characters',
  webCommon_page_len3000_validator_message: 'Please enter 1~3000 characters',
  webCommon_page_len36_validator_message: 'Please enter 1~36 characters',
  webCommon_page_internalError_toast_text: 'internal error',
  webCommon_page_prefix_pagination_label: 'common',
  webCommon_page_suffix_pagination_label: 'bar',
  webCommon_page_select_common_placeholder: 'Please select ',
  webCommon_page_input_common_placeholder: 'Please enter ',
  webCommon_page_add_validator_message: 'please add ',
  webCommon_page_imageUrl_input_text: 'image address',
  webCommon_page_imageUrl_input_placeholder: 'Please enter image link',
  webCommon_page_url_validator_message:
    'Please enter the correct link starting with http:// or https://',
  webCommon_page_s3_validator_message: 'Please enter the correct S3 link',
  webCommon_page_upload_select_placeholder: 'Please upload images',
  webCommon_page_delete_validator_message: 'Please enter Yes and submit',
  webCommon_page_upload_button_text: 'Upload',
  webCommon_page_noImageData_message_text: 'No images yet',
  webCommon_page_imageSize_tip_message:
    '(Image specification: PNG image with file size less than 800KB)',
  webCommon_page_uploadFailed_toast_message: 'File failed to upload, please upload again',
  webCommon_page_comma_validator_message: 'Please separate with commas',
  webCommon_page_deleteForbid_modal_title: 'Disable deletion',
  webCommon_page_richtext_input_placeholder: 'Please enter content...',
  webCommon_page_apiError_toast_text:
    'Service unavailable, server error or network connection down',
  webCommon_page_limit_select_placeholder: 'Please select at least one piece of data',
  webCommon_page_uploadError_toast_text: 'Please upload the correct format file',
  webCommon_page_refresh_button_text: 'Refresh',
  webCommon_page_forbidDelete_toast_text: 'Disable deletion',
  webCommon_page_number_input_placeholder: 'Please enter numbers separated by ","',
  webCommon_page_languageId_label_text: 'Multilanguage ID:',
  webCommon_compare_eq_dictionnary_text: 'equals',
  webCommon_compare_include_dictionnary_text: 'include',
  webCommon_compare_gt_dictionnary_text: 'greater than',
  webCommon_compare_lt_dictionnary_text: 'less than',
  webCommon_page_deleteConfirm_input_label:
    'Please enter Yes in the lower box for double confirmation.',
  webCommon_page_startDate_searchLabel_placeholder: 'Start date',
  webCommon_page_endDate_searchLabel_placeholder: 'End date',
  webCommon_page_time_validator_message: 'The end time cannot be shorter than the start time',
};
