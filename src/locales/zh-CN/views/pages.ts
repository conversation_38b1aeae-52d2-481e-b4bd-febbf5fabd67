export default {
  webOperation_appsort_edit_drawer_title: '编辑列表',
  webOperation_appsort_view_drawer_title: '查看列表',
  webOperation_appsort_isShow0_tableButton_text: '显示',
  webOperation_appsort_isShow1_tableButton_text: '隐藏',
  webOperation_appsort_display_tableColumn_text: '显示/隐藏',
  webOperation_appsort_elementCode_tableColumn_text: '列表项',
  webOperation_appsort_item_tableColumn_text: '列表项顺序',
  webOperation_appsort_itemCode_tableColumn_text: '列表名称',
  webOperation_brand_brandIcon_tableColumn_text: '品牌图片',
  webOperation_brand_brandName_tableColumn_text: '品牌名称',
  webOperation_brand_description_tableColumn_text: '备注',
  webOperation_brand_iconAddress_tableColumn_text: '图片地址',
  webOperation_brand_id_tableColumn_text: '品牌ID',
  webOperation_brand_langId_tableColumn_text: '多语言ID',
  webOperation_brand_delete_modal_message: '该品牌数据被删除后将无法恢复，确认删除？',
  webOperation_category_categoryIcon_tableColumn_text: '品类图片',
  webOperation_category_categoryName_tableColumn_text: '品类名称',
  webOperation_category_description_tableColumn_text: '备注',
  webOperation_category_id_tableColumn_text: '品类ID',
  webOperation_category_langId_tableColumn_text: '多语言ID',
  webOperation_category_delete_modal_message: '该品类数据被删除后将无法恢复，确认删除？',
  webOperation_code_add_common_text: '添加设备ID',
  webOperation_code_import_common_text: '导入设备清单',
  webOperation_code_invalid_tableButton_text: '废弃',
  webOperation_code_valid_tableButton_text: '恢复',
  webOperation_code_importTemplate_fileName_text: '多码表模板',
  webOperation_code_export_fileName_text: '多码表',
  webOperation_code_deviceId_tableColumn_text: '设备ID',
  webOperation_code_itemCode_tableColumn_text: 'ITEM code',
  webOperation_code_mes_tableColumn_text: 'MES#',
  webOperation_code_moCode_tableColumn_text: 'MO code',
  webOperation_code_productionDate_tableColumn_text: 'Production Date',
  webOperation_code_iccid_tableColumn_text: 'ICCID',
  webOperation_code_sn_tableColumn_text: '设备sn',
  webOperation_code_status_tableColumn_text: '状态',
  webOperation_code_importError_modal_message: '多码表导入失败，请检查以下数据：',
  webOperation_code_invalid_modal_message: '设备被废弃后将无法连接云端，确认废弃？',
  webOperation_code_valid_modal_message: '恢复设备可重新连接云端，确认恢复？',
  webOperation_code_invalid_modal_title: '设备停用',
  webOperation_code_valid_modal_title: '设备恢复',
  // 经销商
  webOperation_dealer_dealerId_tableColumn_text: '经销商ID',
  webOperation_dealer_name_tableColumn_text: '经销商名称',
  webOperation_dealer_country_tableColumn_text: '所在国家',
  webOperation_dealer_state_tableColumn_text: '所在省份',
  webOperation_dealer_city_tableColumn_text: '所在市区',
  webOperation_dealer_address_tableColumn_text: '经销商地址',
  webOperation_dealer_zipcode_tableColumn_text: '邮政编码',
  webOperation_dealer_telephone_tableColumn_text: '电话',
  webOperation_dealer_email_tableColumn_text: '邮箱',
  webOperation_dealer_website_tableColumn_text: '网址',
  webOperation_dealer_hours_tableColumn_text: '工作时长',
  webOperation_dealer_lat_tableColumn_text: 'lat',
  webOperation_dealer_lng_tableColumn_text: 'lng',
  webOperation_dealer_category_tableColumn_text: '类别',
  webOperation_dealer_zoomLevel_tableColumn_text: '地图缩放等级',
  webOperation_dealer_color_tableColumn_text: '颜色',
  webOperation_dealer_fontClass_tableColumn_text: '矢量图标',
  webOperation_dealer_isActive_tableColumn_text: '激活状态',
  webOperation_dealer_image_tableColumn_text: '图片',
  webOperation_dealer_storeLocatorId_tableColumn_text: '存储定位器',
  webOperation_dealer_delete_modal_message: '该经销商数据被删除后将无法恢复，确认删除？',
  webOperation_dealer_comfirm_import_modal_message: '请确认导入文件内容无误，且同意删除历史数据',
  webOperation_app_protocoldetail_drawer_title: '协议详情',
  webOperation_brand_create_drawer_title: '创建品牌',
  webOperation_brand_detail_drawer_title: '品牌详情',
  webOperation_brand_edit_drawer_title: '编辑品牌',
  webOperation_category_create_drawer_title: '创建品类',
  webOperation_category_detail_drawer_title: '品类详情',
  webOperation_category_edit_drawer_title: '编辑品类',
  webOperation_dealer_new_drawer_title: '添加经销商',
  webOperation_dealer_view_drawer_title: '经销商详情',
  webOperation_dealer_edit_drawer_title: '编辑经销商',
  webOperation_dealer_hour_label_text: '小时',
  webOperation_dealer_import_common_text: '导入经销商',
  webOperation_dealer_importTemplate_fileName_text: '经销商模板',
  webOperation_dealer_importError_modal_message: '经销商导入失败，请检查以下数据：',
  webOperation_group_select_drawer_title: '分组选择',
  webOperation_faq_create_drawer_title: '添加FAQ',
  webOperation_faq_detail_drawer_title: 'FAQ详情',
  webOperation_faq_edit_drawer_title: '编辑FAQ',
  webOperation_guide_create_drawer_title: '添加文件',
  webOperation_guide_edit_drawer_title: '编辑文件',
  webOperation_message_detail_drawer_title: '消息详情',
  webOperation_message_edit_drawer_title: '编辑消息',
  webOperation_message_new_drawer_title: '添加消息',
  webOperation_message_copy_drawer_title: '复制消息',
  webOperation_message_result_drawer_title: '推送结果',
  webOperation_product_create_drawer_title: '添加独立产品',
  webOperation_product_detail_drawer_title: '产品详情',
  webOperation_product_edit_drawer_title: '编辑产品信息',
  webOperation_product_firmwareconfig_drawer_title: '任务发布配置',
  webOperation_product_groupset_drawer_title: '产品测试分组配置',
  webOperation_product_rnconfig_drawer_title: 'RN发布配置',
  webOperation_product_rndetail_drawer_title: '发布详情',
  webOperation_template_create_drawer_title: '添加消息模板',
  webOperation_template_edit_drawer_title: '编辑消息模板',
  webOperation_template_view_drawer_title: '查看消息模板',
  webOperation_page_relativeProduct_drawer_title: '已关联产品',
  webOperation_firmware_export_filename_text: '固件发布列表',
  webOperation_product_export_filename_text: '产品列表',
  webOperation_releaseProducts_export_filename_text: '发布产品列表',
  webOperation_rn_export_filename_text: 'RN发布列表',
  // 通用FAQ
  webOperation_faq_answer_tableColumn_text: '解决方法',
  webOperation_faq_answerLangId_tableColumn_text: '解决方法多语言ID',
  webOperation_faq_instanceId_tableColumn_text: '问题ID',
  webOperation_faq_title_tableColumn_text: '问题',
  webOperation_faq_titleLangId_tableColumn_text: '问题多语言ID',
  webOperation_faq_typeCode_tableColumn_text: '问题类型',
  webOperation_faq_importTemplate_fileName_text: '通用FAQ导入模板',
  webOperation_faq_export_filename_text: '通用FAQ',
  webOperation_faq_import_common_text: '导入FAQ',
  webOperation_faq_importError_modal_message: '通用FAQ导入失败，请检查以下数据：',
  webOperation_firmware_componentName_tableColumn_text: '总成零件名称',
  webOperation_firmware_componentNo_tableColumn_text: '总成零件号',
  webOperation_firmware_componentType_tableColumn_text: '总成零件类型',
  webOperation_firmware_developStatus_tableColumn_text: '固件开发状态',
  webOperation_firmware_failed_tableColumn_text: '升级失败设备数量',
  webOperation_firmware_jobId_tableColumn_text: '任务ID',
  webOperation_firmware_minimumVersion_tableColumn_text: '最低兼容固件版本号',
  webOperation_firmware_newVersion_tableColumn_text: '更新后固件版本',
  webOperation_firmware_oldVersion_tableColumn_text: '更新前固件版本',
  webOperation_firmware_packageCount_tableColumn_text: '固件数量',
  webOperation_firmware_packageName_tableColumn_text: '固件名称',
  webOperation_firmware_packageType_tableColumn_text: '固件类型',
  webOperation_firmware_packageVersion_tableColumn_text: '固件版本号',
  webOperation_firmware_releaseContent_tableColumn_text: '升级说明',
  webOperation_firmware_releaseStatus_tableColumn_text: '固件发布状态',
  webOperation_firmware_size_tableColumn_text: '文件体积',
  webOperation_firmware_status_tableColumn_text: '升级结果',
  webOperation_firmware_resultTime_tableColumn_text: '升级结果时间',
  webOperation_firmware_succeed_tableColumn_text: '升级成功设备数量',
  webOperation_firmware_total_tableColumn_text: '设备总数',
  webOperation_firmware_upgradeContent_tableColumn_text: '升级内容',
  webOperation_firmware_releaseDetail_common_text: '固件发布详情',
  webOperation_group_conditionsAdd_button_text: '添加分组条件',
  webOperation_group_conditionsEdit_button_text: '编辑分组条件',
  webOperation_group_create_button_text: '添加分组',
  webOperation_group_edit_button_text: '编辑分组',
  webOperation_group_view_button_text: '查看分组',
  webOperation_group_delete_modal_message:
    '请确认是否要删除该分组？删除后该分组将无法被找回。请在下方输入框中输入“Yes”进行二次确认。',
  webOperation_group_delete_input_placeholder: '请输入“Yes”进行二次确认',
  webOperation_group_result_tableButton_text: '分组结果',
  webOperation_group_comments_tableColumn_text: '备注',
  webOperation_group_conditionContent_tableColumn_text: '分组条件内容',
  webOperation_group_conditionContents_tableColumn_text: '分组条件',
  webOperation_group_conditionRule_tableColumn_text: '判断规则',
  webOperation_group_conditionRules_tableColumn_text: '判断规则',
  webOperation_group_conditionType_tableColumn_text: '条件类型',
  webOperation_group_conditionValue_tableColumn_text: '条件值',
  webOperation_group_conditionValues_tableColumn_text: '条件值',
  webOperation_group_groupName_tableColumn_text: '分组名称',
  webOperation_group_groupType_tableColumn_text: '分组类型',
  webOperation_group_deleteError_modal_message:
    '当前分组规则正在被以下任务使用，在结束全部任务后才可以对分组进行删除操作',
  webOperation_group_deleteError_modal_title: '删除失败提示',
  webOperation_group_conditionValue_validator_message: '最多输入5个值',
  webOperation_groupresult_deviceId_tableColumn_text: '设备ID',
  webOperation_groupresult_onlineStatus_tableColumn_text: '设备状态',
  webOperation_groupresult_productModel_tableColumn_text: '产品型号',
  webOperation_groupresult_userIds_tableColumn_text: '绑定用户ID',
  // 通用操作指导
  webOperation_guide_url_tableColumn_text: '文件链接',
  webOperation_guide_desc_tableColumn_text: '文件描述',
  webOperation_guide_descLangId_tableColumn_text: '文件描述多语言ID',
  webOperation_guide_description_tableColumn_text: '内容描述',
  webOperation_guide_descriptionLangId_tableColumn_text: '内容描述多语言ID',
  webOperation_guide_file_tableColumn_text: '选择文件',
  webOperation_guide_format_tableColumn_text: '文件格式',
  webOperation_guide_instanceId_tableColumn_text: '文件ID',
  webOperation_guide_name_tableColumn_text: '文件名称',
  webOperation_guide_nameLangId_tableColumn_text: '文件名称多语言ID',
  webOperation_guide_size_tableColumn_text: '文件体积',
  webOperation_guide_type_tableColumn_text: '添加方式',
  webOperation_guide_typeCode_tableColumn_text: '添加方式',
  webOperation_guide_address_tableColumn_text: '文件地址',
  webOperation_guide_typeCode_input_placeholder: '请选择添加方式',
  webOperation_guide_importTemplate_fileName_text: '通用操作指导导入模板',
  webOperation_guide_export_filename_text: '通用操作指导',
  webOperation_guide_import_common_text: '导入通用操作指导',
  webOperation_guide_importError_modal_message: '通用操作指导导入失败，请检查以下数据：',
  // APP消息
  webOperation_message_content_tableColumn_text: '消息内容',
  webOperation_message_endTime_tableColumn_text: '推送结束时间',
  webOperation_message_id_tableColumn_text: '消息ID',
  webOperation_message_productModel_tableColumn_text: '产品型号',
  webOperation_message_commodityModel_tableColumn_text: '商品型号/Model #',
  webOperation_message_pushCount_tableColumn_text: '推送条数',
  webOperation_message_pushTime_tableColumn_text: '推送时间',
  webOperation_message_pushType_tableColumn_text: '推送方式',
  webOperation_message_releaseStateCode_tableColumn_text: '发布状态',
  webOperation_message_result_tableColumn_text: '推送结果',
  webOperation_message_route_tableColumn_text: '路由地址',
  webOperation_message_startTime_tableColumn_text: '推送开始时间',
  webOperation_message_title_tableColumn_text: '消息标题',
  webOperation_message_userId_tableColumn_text: '用户ID',
  webOperation_rn_downloadError_toast_message: 'RN下载地址获取失败',
  webOperation_faq_delete_modal_message: '该FAQ被删除后将无法恢复，确认删除？',
  webOperation_firmware_confirmRelease_modal_message:
    '您是否确认要发布该固件升级任务？发布后将无法删除',
  webOperation_firmware_ensureRelease_modal_message:
    '当前已有一个已发布的固件升级任务，新的固件升级任务发布审核通过后，当前升级任务将会被新任务替代，请确认是否继续申请发布此固件升级任务？',
  webOperation_firmware_release_modal_message: '请确认是否申请发布此固件升级任务？',
  webOperation_firmware_stop_modal_message:
    '您是否确认要停止发布该固件升级任务？停止发布后未完成升级任务的设备将无法继续升级',
  webOperation_firmware_test_modal_message: '您是否确认该固件升级任务已通过测试？',
  webOperation_firmware_nullify_modal_message: '请确认是否申请作废此固件升级任务？',
  webOperation_firmware_nullify_modal_title: '作废申请确认',
  webOperation_guide_delete_modal_message: '该文件被删除后将无法恢复，确认删除？',
  webOperation_guide_ensureRelease_modal_message: '您是否确认要发布该条数据？发布后将无法删除！',
  webOperation_guide_stop_modal_message: '您是否确认要停止发布该数据？',
  webOperation_guide_test_modal_message: '您是否确认该数据已通过测试？',
  webOperation_product_Off_modal_message:
    '您是否确认要下架该产品？下架后该产品将无法被用户看到或绑定',
  webOperation_product_delete_modal_message: '该独立产品被删除后将无法恢复。确认删除？',
  webOperation_product_release_modal_message: '您是否确认要发布该产品？发布后将无法删除',
  webOperation_product_test_modal_message: '您是否确认该产品已通过测试？',
  webOperation_product_update_modal_message:
    '您是否确认要更新该产品的数据？更新后原产品数据将无法被找回',
  webOperation_protocol_delete_modal_message:
    '请确认是否要删除该协议？删除后该协议将无法被找回。请在下方输入框中输入“Yes”进行二次确认。',
  webOperation_protocol_off_modal_message: '您是否确认要停止发布该协议？停止发布后该协议将失效',
  webOperation_protocol_release_modal_message: '您是否确认要发布该协议？发布后将无法删除',
  webOperation_protocol_test_modal_message: '您是否确认该协议已通过测试？',
  webOperation_relativeProduct_delete_modal_message: '确认要删除该关联产品吗？',
  webOperation_rn_release_modal_message: '您是否确认要发布该RN包？发布后将无法删除',
  webOperation_rn_test_modal_message: '您是否确认该RN包已通过测试？',
  webOperation_faq_delete_modal_title: '删除确认',
  webOperation_firmware_ensureRelease_modal_title: '固件升级任务发布确认',
  webOperation_firmware_confirmRelease_modal_title: '固件升级任务发布确认',
  webOperation_firmware_release_modal_title: '发布申请确认',
  webOperation_firmware_stop_modal_title: '停止发布确认',
  webOperation_firmware_test_modal_title: '固件升级任务测试通过确认',
  webOperation_guide_delete_modal_title: '删除确认',
  webOperation_guide_ensureRelease_modal_title: '发布确认',
  webOperation_guide_stop_modal_title: '停止发布确认',
  webOperation_guide_test_modal_title: '测试通过确认',
  webOperation_product_Off_modal_title: '产品下架确认',
  webOperation_product_refuse_modal_title: '驳回申请',
  webOperation_product_release_modal_title: '产品发布确认',
  webOperation_product_test_modal_title: '产品测试通过确认',
  webOperation_product_update_modal_title: '产品更新确认',
  webOperation_protocol_off_modal_title: '停止发布确认',
  webOperation_protocol_release_modal_title: '协议发布确认',
  webOperation_protocol_test_modal_title: '协议测试通过确认',
  webOperation_refuse_reason_modal_title: '驳回原因',
  webOperation_rn_release_modal_title: 'RN包发布确认',
  webOperation_rn_test_modal_title: 'RN包测试通过确认',
  webOperation_part_add_button_text: '添加配件',
  webOperation_part_import_common_text: '导入',
  webOperation_partDetail_brief_input_text: '产品短描述',
  webOperation_partDetail_description_input_text: '产品长描述',
  webOperation_partDetail_manual_table_text: '用户手册',
  webOperation_partDetail_addFile_button_text: '添加文件',
  webOperation_partDetail_guide_table_text: '操作指导',
  webOperation_partDetail_faq_table_text: 'FAQ',
  webOperation_partDetail_link_table_text: '购买链接',
  webOperation_partDetail_spec_input_text: '技术规格',
  webOperation_part_commodityModel_input_text: '商品型号/Model #',
  webOperation_part_commodityModel_input_placeholder: '请输入商品型号/Model #',
  webOperation_part_desc_input_text: '备注',
  webOperation_part_desc_input_placeholder: '请输入备注',
  webOperation_file_desc_input_text: '文件描述',
  webOperation_file_descLang_input_text: '文件描述多语言ID',
  webOperation_file_name_input_text: '文件名称',
  webOperation_file_name_input_placeholder: '请输入文件名称',
  webOperation_file_nameLangId_input_text: '文件名称多语言ID',
  webOperation_file_id_input_text: '文件ID',
  webOperation_file_size_input_text: '文件体积',
  webOperation_file_type_input_text: '文件格式',
  webOperation_file_link_input_text: '文件链接',
  webOperation_file_link_input_placeholder: '请输入文件链接',
  webOperation_part_id_input_text: '配件ID',
  webOperation_part_name_input_text: '配件名称',
  webOperation_part_name_input_placeholder: '请输入配件名称',
  webOperation_part_icon_select_text: '配件图片',
  webOperation_part_type1_select_text: '配件类型1',
  webOperation_part_type1_select_placeholder: '请选择配件类型1',
  webOperation_part_type2_select_text: '配件类型2',
  webOperation_part_deleteLine1_modal_message: '请确认是否要删除该链接？删除后该链接将无法被找回。',
  webOperation_part_deleteLine2_modal_message: '请在下方输入框中输入“Yes”进行二次确认。',
  webOperation_part_addLink_drawer_title: '添加购买链接',
  webOperation_part_updateLink_drawer_title: '编辑购买链接',
  webOperation_part_link_validator_message: '请输入以http://或https://开头内容',
  webOperation_part_linkId_input_text: '购买链接ID',
  webOperation_part_linkUrl_input_text: '购买链接',
  webOperation_part_linkUrl_input_placeholder: '请输入购买链接',
  webOperation_part_linkId_tableColumn_text: '购买链接ID',
  webOperation_part_linkUrl_tableColumn_text: '购买链接',
  webOperation_part_commodityModel_tableColumn_text: '商品型号/Model #',
  webOperation_part_desc_tableColumn_text: '备注',
  webOperation_part_id_tableColumn_text: '配件ID',
  webOperation_part_name_tableColumn_text: '配件名称',
  webOperation_part_option_tableColumn_text: '操作',
  webOperation_part_partsIcon_tableColumn_text: '配件图片',
  webOperation_part_relate_tableColumn_text: '已关联产品',
  webOperation_part_type1_tableColumn_text: '配件类型1',
  webOperation_part_type2_tableColumn_text: '配件类型2',
  webOperation_part_maintenanceType_tableColumn_text: '维保类型',
  webOperation_part_warranty_tableColumn_text: '维保周期',
  webOperation_part_warrantyRemindFrequency_tableColumn_text: '维保提醒',
  webOperation_part_delete_modal_message: '该配件被删除后将无法恢复',
  webOperation_part_relate_tableButton_text: '已关联产品',
  webOperation_part_importError_modal_message: '配件表导入失败，请检查以下数据：',
  webOperation_introduction_deleteLine1_modal_message:
    '请确认是否要删除该引导页？删除后该引导页将无法被找回。',
  webOperation_introduction_deleteLine2_modal_message: '请在下方输入框中输入“Yes”进行二次确认。',
  webOperation_introduction_delete_input_placeholder: '请输入Yes后再提交',
  webOperation_product_import_common_text: '导入独立产品',
  webOperation_product_applicationRelease_tableButton_text: '申请发布',
  webOperation_product_canApplyOff_tableButton_text: '申请下架',
  webOperation_product_canApplyRelease_tableButton_text: '申请发布',
  webOperation_product_canApplyStopRelease_tableButton_text: '申请停止发布',
  webOperation_product_canApplyUpdate_tableButton_text: '申请更新',
  webOperation_product_canCancelApplyOff_tableButton_text: '撤回下架申请',
  webOperation_product_canCancelApplyRelease_tableButton_text: '撤回发布申请',
  webOperation_product_canCancelApplyStopRelease_tableButton_text: '撤回停止发布申请',
  webOperation_product_canCancelApplyUpdate_tableButton_text: '撤回更新申请',
  webOperation_product_canNullifyApply_tableButton_text: '申请作废',
  webOperation_product_canNullifyApplyCancel_tableButton_text: '撤回作废申请',
  webOperation_product_canNullifyPassed_tableButton_text: '确认申请作废',
  webOperation_product_canNullifyRefused_tableButton_text: '申请作废驳回',
  webOperation_product_canViewNullifyRefusedReason_tableButton_text: '作废被驳回原因',
  webOperation_product_canConfig_tableButton_text: 'RN发布配置',
  webOperation_product_canDelete_tableButton_text: '删除',
  webOperation_product_canDownLoad_tableButton_text: '下载',
  webOperation_product_canEnsureApply_tableButton_text: '确认发布',
  webOperation_product_canEnsureOff_tableButton_text: '确认下架',
  webOperation_product_canEnsureStopRelease_tableButton_text: '确认停止发布',
  webOperation_product_canEnsureTest_tableButton_text: '测试通过',
  webOperation_product_canEnsureTestUpdate_tableButton_text: '测试通过',
  webOperation_product_canEnsureUpdate_tableButton_text: '确认更新',
  webOperation_product_canRefuseApply_tableButton_text: '发布驳回',
  webOperation_product_canRefuseOff_tableButton_text: '下架驳回',
  webOperation_product_canRefuseStopRelease_tableButton_text: '停止发布驳回',
  webOperation_product_canRefuseTest_tableButton_text: '测试驳回',
  webOperation_product_canRefuseTestUpdate_tableButton_text: '测试驳回',
  webOperation_product_canRefuseUpdate_tableButton_text: '更新驳回',
  webOperation_product_canUpdate_tableButton_text: '编辑',
  webOperation_product_canView_tableButton_text: '发布详情',
  webOperation_product_canViewConfig_tableButton_text: 'RN发布详情',
  webOperation_product_canViewRefuseOffReason_tableButton_text: '下架驳回原因',
  webOperation_product_canViewRefuseReleaseReason_tableButton_text: '发布被驳回原因',
  webOperation_product_canViewRefuseStopReleaseReason_tableButton_text: '停止发布被驳回原因',
  webOperation_product_canViewRefuseTestReason_tableButton_text: '测试被驳回原因',
  webOperation_product_canViewRefuseUpdateReason_tableButton_text: '更新被驳回原因',
  webOperation_product_canViewRefuseUpdateTestReason_tableButton_text: '更新测试被驳回原因',
  webOperation_product_confirmRelease_tableButton_text: '确认发布',
  webOperation_product_relative_button_text: '添加关联产品',
  webOperation_product_importTemplate_fileName_text: '独立产品模板',
  webOperation_introduction_0_label_text: '扫码',
  webOperation_introduction_none0_label_text: '暂无扫码指引',
  webOperation_introduction_step0_button_text: '添加扫码指引',
  webOperation_introduction_1_label_text: '配网',
  webOperation_introduction_none1_label_text: '暂无配网步骤',
  webOperation_introduction_step1_button_text: '添加配网步骤',
  webOperation_introduction_2_label_text: '固件升级',
  webOperation_introduction_none2_label_text: '暂无固件升级步骤',
  webOperation_introduction_step2_button_text: '添加固件升级步骤',
  webOperation_introduction_guide_label_text: '引导',
  webOperation_introduction_img_select_text: '图片',
  webOperation_introduction_langId_input_text: '多语言ID',
  webOperation_introduction_step_label_text: '步骤',
  webOperation_introduction_text_input_text: '文案',
  webOperation_introduction_text_input_placeholder: '请输入文案',
  webOperation_product_applyTime_tableColumn_text: '申请时间',
  webOperation_product_applyUserName_tableColumn_text: '申请人',
  webOperation_product_approveTime_tableColumn_text: '审批时间',
  webOperation_product_approveUserName_tableColumn_text: '审批人',
  webOperation_product_brandId_tableColumn_text: '品牌名称',
  webOperation_product_categoryId_tableColumn_text: '品类名称',
  webOperation_product_commodityModel_tableColumn_text: '商品型号/Model #',
  webOperation_product_group_tableColumn_text: '测试分组',
  webOperation_product_id_tableColumn_text: 'PID',
  webOperation_product_model_tableColumn_text: '产品型号',
  webOperation_product_networkModes_tableColumn_text: '联网方式',
  webOperation_product_operationRemark_tableColumn_text: '备注',
  webOperation_product_productFullName_tableColumn_text: '产品全称',
  webOperation_product_productIcon_tableColumn_text: '产品图片',
  webOperation_product_productId_tableColumn_text: 'PID',
  webOperation_product_productName_tableColumn_text: '产品名称',
  webOperation_product_productSnCode_tableColumn_text: 'SN code',
  webOperation_product_productType_tableColumn_text: '设备类型',
  webOperation_product_questionTemplate_tableColumn_text: '问卷模板',
  webOperation_product_releaseStatus_tableColumn_text: '发布状态',
  webOperation_product_status_tableColumn_text: '产品状态',
  webOperation_product_technologyVersion_tableColumn_text: '技术固件版本号',
  webOperation_product_customVersion_tableColumn_text: '已发布固件版本',
  webOperation_product_importError_modal_message: '独立产品导入失败，请检查以下数据：',
  webOperation_part_sort_button_text: '调整顺序',
  webOperation_part_sort_tableColumn_text: '排序',
  webOperation_part_model_tableColumn_text: '配件型号',
  webOperation_part_sort_drawer_title: '配件顺序调整',
  webOperation_part_edit_drawer_title: '编辑配件详情',
  webOperation_part_view_drawer_title: '配件详情',
  webOperation_part_warrantyUnit_tableColumn_text: '天',
  webOperation_part_warrantyUnitHour_tableColumn_text: '小时',
  webOperation_product_deviceProtocol_tabs_text: '设备协议管理',
  webOperation_product_firmwareConfig_tabs_text: '固件发布配置',
  webOperation_product_introduction_tabs_text: '引导页管理',
  webOperation_product_multicode_tabs_text: '多码管理',
  webOperation_product_partList_tabs_text: '配件清单',
  webOperation_product_productInfo_tabs_text: '产品信息管理',
  webOperation_product_rnConfig_tabs_text: 'RN发布配置',
  webOperation_product_faq_tabs_text: '售后内容管理',
  webOperation_protocol_create_button_text: '添加协议',
  webOperation_protocol_detail_drawer_title: '协议详情',
  webOperation_protocol_edit_drawer_title: '编辑协议',
  webOperation_protocol_new_drawer_title: '添加新版本协议',
  webOperation_protocol_recreate_common_text: '添加新版本协议',
  webOperation_protocol_addNewVersion_tableButton_linkText: '添加新版本',
  webOperation_protocol_deleteLine1_modal_message:
    '请确认是否要删除该协议？删除后该协议将无法被找回。',
  webOperation_protocol_deleteLine2_modal_message: '请在下方输入框中输入“Yes”进行二次确认。',
  webOperation_protocol_delete_input_placeholder: '请输入Yes后再提交',
  webOperation_protocol_condition_input_text: '判断条件',
  webOperation_protocol_content_input_text: '文案内容',
  webOperation_protocol_content_input_placeholder: '请输入文案内容',
  webOperation_protocol_create_drawer_title: '添加协议',
  webOperation_protocol_id_input_text: '协议ID',
  webOperation_protocol_name_input_text: '协议名称',
  webOperation_protocol_name_input_placeholder: '请输入协议名称',
  webOperation_protocol_type_select_text: '协议类型',
  webOperation_protocol_type_select_placeholder: '请选择协议类型',
  webOperation_protocol_update_drawer_title: '编辑协议',
  webOperation_protocol_version_input_text: '协议版本号',
  webOperation_protocol_version_validator_message: '请输入正确的版本号:如 1.0.0',
  webOperation_protocol_version_input_placeholder: '请输入协议版本号',
  webOperation_protocol_appAgreementContentId_tableColumn_text: '协议ID',
  webOperation_protocol_content_tableColumn_text: '协议内容',
  webOperation_protocol_contentId_tableColumn_text: '协议内容多语言ID',
  webOperation_protocol_contentLangId_tableColumn_text: '协议内容多语言ID',
  webOperation_protocol_id_tableColumn_text: '协议ID',
  webOperation_protocol_langId_tableColumn_text: '协议名称多语言ID',
  webOperation_protocol_name_tableColumn_text: '协议名称',
  webOperation_protocol_option_tableColumn_text: '操作',
  webOperation_protocol_statusCode_tableColumn_text: '发布状态',
  webOperation_protocol_title_tableColumn_text: '协议名称',
  webOperation_protocol_titleLangId_tableColumn_text: '协议名称多语言ID',
  webOperation_protocol_type_tableColumn_text: '协议类型',
  webOperation_protocol_typeCode_tableColumn_text: '协议类型',
  webOperation_protocol_version_tableColumn_text: '协议版本号',
  webOperation_protocol_businessType_tableColumn_text: '适用APP',
  webOperation_refuse_info_tableColumn_text: '您的申请因以下原因被驳回',
  webOperation_release_date_tableColumn_text: '日期',
  webOperation_release_end_tableColumn_text: '结束时间',
  webOperation_release_operationUpdateContent_tableColumn_text: '运营更新说明',
  webOperation_release_prdGroup_tableColumn_text: '生产分组',
  webOperation_release_remark_tableColumn_text: '驳回原因',
  webOperation_release_start_tableColumn_text: '开始时间',
  webOperation_release_testGroup_tableColumn_text: '测试分组',
  webOperation_release_time_tableColumn_text: '时间',
  webOperation_release_zone_tableColumn_text: '时区',
  webOperation_rn_androidVersionMin_tableColumn_text: 'Android最低兼容版本号',
  webOperation_rn_appId_tableColumn_text: 'APP ID',
  webOperation_rn_appName_tableColumn_text: 'APP名称',
  webOperation_rn_iosVersionMin_tableColumn_text: 'ios最低兼容版本号',
  webOperation_rn_releaseStateCode_tableColumn_text: 'RN包发布状态',
  webOperation_rn_releaseStatusCode_tableColumn_text: '发布状态',
  webOperation_rn_rnName_tableColumn_text: 'RN包名称',
  webOperation_rn_rnVersion_tableColumn_text: 'RN包版本号',
  webOperation_rn_startZone_tableColumn_text: '时区',
  webOperation_rn_stateCode_tableColumn_text: 'RN包开发状态',
  webOperation_rn_updateContent_tableColumn_text: '更新内容',
  webOperation_task_detail_common_text: '任务详情',
  webOperation_template_content_tableColumn_text: '模板内容',
  webOperation_template_id_tableColumn_text: '模板ID',
  webOperation_template_name_tableColumn_text: '模板名称',
  webOperation_template_title_tableColumn_text: '模板标题',
  webOperation_template_type_tableColumn_text: '模板类型',
  webOperation_template_messageDisplayType_tableColumn_text: '展示类型',
  webOperation_template_used_tableColumn_text: '被引用数量',
  webOperation_template_disabled_toast_text: '模板已被使用，不可编辑',
  webOperation_template_notDeleted_toast_text: '模板已被使用，不可删除',

  webOperation_upgrade_result_common_text: '升级结果',
  webOperation_user_appPresenceCode_tableColumn_text: 'APP在线状态',
  webOperation_user_appTypeCode_tableColumn_text: 'APP类型',
  webOperation_user_appVersion_tableColumn_text: 'APP版本号',
  webOperation_user_bindDeviceBrandId_tableColumn_text: '已绑定设备品牌',
  webOperation_user_bindDeviceCategoryId_tableColumn_text: '已绑定设备品类',
  webOperation_user_bindDeviceId_tableColumn_text: '已绑定设备ID',
  webOperation_user_bindDeviceSn_tableColumn_text: '已绑定设备SN',
  webOperation_user_commodityModel_tableColumn_text: '已绑定设备商品型号/Model #',
  webOperation_user_deviceSource_tableColumn_text: '设备来源',
  webOperation_user_shareType_tableColumn_text: '主子账户',
  webOperation_user_email_tableColumn_text: '邮箱',
  webOperation_user_ip_tableColumn_text: 'IP地址',
  webOperation_user_itemCode_tableColumn_text: '列表名称',
  webOperation_user_lastLoginTime_tableColumn_text: '最后登录时间',
  webOperation_user_model_tableColumn_text: '已绑定设备产品型号',
  webOperation_user_phoneModel_tableColumn_text: '手机型号',
  webOperation_user_phoneOsVersion_tableColumn_text: '系统版本号',
  webOperation_user_registerTime_tableColumn_text: '注册时间',
  webOperation_user_userId_tableColumn_text: '用户ID',
  webOperation_user_userSourceCode_tableColumn_text: '用户来源',
  webOperation_user_userTypeCode_tableColumn_text: '用户类型',
  webOperation_user_export_fileName_text: 'APP用户',
  webOperation_userdevice_export_fileName_text: 'APP用户设备',

  // APP消息
  webOperation_appMessage_sysMsgId_tableColumn_text: '消息ID',
  webOperation_appMessage_title_tableColumn_text: '消息标题',
  webOperation_appMessage_content_tableColumn_text: '消息内容',
  webOperation_appMessage_pushTypeCodes_tableColumn_text: '推送方式',
  webOperation_appMessage_pushRateCode_tableColumn_text: '推送频率',
  webOperation_appMessage_pushTime_tableColumn_text: '推送时间',
  webOperation_appMessage_startTime_tableColumn_text: '推送开始时间',
  webOperation_appMessage_endTime_tableColumn_text: '推送结束时间',
  webOperation_appMessage_statusCode_tableColumn_text: '发布状态',
  webOperation_appMessage_rutePath_tableColumn_text: '路由地址',
  webOperation_appMessage_test_modal_title: '消息测试通过确认',
  webOperation_appMessage_test_modal_message: '您是否确认该消息已通过测试？',
  webOperation_appMessage_ensureRelease_modal_title: '消息发布确认',
  webOperation_appMessage_ensureRelease_modal_message: '您是否确认要发布该消息？发布后将无法删除',
  webOperation_appMessage_stop_modal_title: '停止发布确认',
  webOperation_appMessage_stop_modal_message:
    '您是否确认要停止发布该消息？停止发布后将立即停止消息推送',
  webOperation_appMessage_delete_modal_message:
    '请确认是否要删除该消息？删除后该消息将无法被找回。请在下方输入框中输入“Yes”进行二次确认。',

  // 产品管理配置
  webOperation_permission_setting_drawer_title: '操作权限配置',
  webOperation_permission_edit_tabs_text: '编辑操作',
  webOperation_permission_approve_tabs_text: '发布审核操作',
  webOperation_permission_test_tabs_text: '测试审核操作',
  webOperation_permission_delete_modal_message: '请确认是否要删除该人员的操作权限？',
  webOperation_permission_add_button_text: '添加人员',
  webOperation_permission_product_tabs_text: '产品管理',
  webOperation_permission_ota_tabs_text: '固件管理',
  webOperation_permission_rn_tabs_text: 'RN面板管理',
  webOperation_employee_employeeNumber_tableColumn_text: '工号',
  webOperation_employee_userName_tableColumn_text: '平台使用人',
  webOperation_employee_organizationName_tableColumn_text: '组织',

  // 帮助中心
  webOperation_help_id_tableColumn_text: '帮助ID',
  webOperation_help_title_tableColumn_text: '标题',
  webOperation_help_title_input_placeholder: '请输入标题',
  webOperation_help_titleLangId_tableColumn_text: '多语言ID',

  webOperation_help_from_tableColumn_text: '数据来源',
  webOperation_help_read_tableColumn_text: '阅读量',
  webOperation_help_like_tableColumn_text: '点赞量',
  webOperation_help_appShowStatus_tableColumn_text: 'APP显示状态',
  webOperation_help_sync_tableColumn_text: '同步时间',
  webOperation_help_model_tableColumn_text: '商品型号',
  webOperation_help_model_input_rule: '请输入商品型号',
  webOperation_help_model_input_placeholder: '商品型号用分号隔开',
  webOperation_help_answer_tableColumn_text: '帮助内容',
  webOperation_help_answerRule_tableColumn_text: '请输入帮助内容',
  webOperation_help_readCount_tableColumn_text: '阅读量',
  webOperation_help_praiseCount_tableColumn_text: '点赞量',
  webOperation_help_appShowCode_tableColumn_text: 'APP显示状态',

  webOperation_help_show_tableColumn_text: '显示',
  webOperation_help_hide_tableColumn_text: '隐藏',

  webOperation_help_import_common_text: '导入',
  webOperation_help_update_button_text: '更新',
  webOperation_help_create_button_text: '添加帮助',
  webOperation_help_update_drawer_title: '编辑帮助',
  webOperation_help_view_drawer_title: '帮助详情',

  webOperation_help_importError_modal_message: '帮助导入失败，请检查以下数据：',

  webOperation_help_deleteLine1_modal_message: '该文件被删除后将无法恢复',
  webOperation_help_deleteLine2_modal_message: '确认删除？',
  webOperation_help_importTemplate_fileName_text: '帮助导入模板',
  webOperation_appUpgrade_appName_tableColumn_text: 'APP名称',
  webOperation_appUpgrade_id_tableColumn_text: '版本ID',
  webOperation_appUpgrade_version_tableColumn_text: '版本号',
  webOperation_appUpgrade_versionName_tableColumn_text: '版本名称',
  webOperation_appUpgrade_changelog_tableColumn_text: '版本更新内容',
  webOperation_appUpgrade_changelogLangId_tableColumn_text: '版本更新内容多语言ID',

  webOperation_appUpgrade_deleteLine1_modal_message: '确定要删除该数据吗？',
  webOperation_appUpgrade_deleteLine2_modal_message: '删除后，将不可恢复！',
  webOperation_appUpgrade_appName_select_placeholder: '请选择APP名称',
  webOperation_appUpgrade_version_input_text: '版本号',
  webOperation_appUpgrade_version_input_placeholder: '请输入版本号',
  webOperation_appUpgrade_version_validator_message: '请输入正确的版本号:如 1.0.0',
  webOperation_appUpgrade_name_input_text: '版本名称',
  webOperation_appUpgrade_name_input_placeholder: '请输入版本名称',
  webOperation_appUpgrade_changelog_input_text: '版本更新内容',
  webOperation_appUpgrade_changelog_input_placeholder: '请输入版本更新内容',

  webOperation_suggestion_id_tableColumn_text: '建议ID',
  webOperation_suggestion_title_tableColumn_text: '处理建议标题',
  webOperation_suggestion_content_tableColumn_text: '处理建议',
  webOperation_suggestion_extra_tableColumn_text: '附加内容',
  webOperation_suggestion_name_tableColumn_text: '消息名称',
  webOperation_suggestion_option_tableColumn_text: '操作',

  webOperation_suggestion_import_common_text: '导入',
  webOperation_suggestion_create_button_text: '添加处理建议',

  webOperation_suggestion_create_drawer_title: '添加处理建议',
  webOperation_suggestion_update_drawer_title: '编辑处理建议',
  webOperation_suggestion_view_drawer_title: '处理建议详情',

  webOperation_suggestion_id_input_text: '建议ID',
  webOperation_suggestion_title_input_text: '处理建议标题',
  webOperation_suggestion_title_input_placeholder: '请输入处理建议标题',
  webOperation_suggestion_content_input_text: '处理建议',
  webOperation_suggestion_content_input_placeholder: '请输入处理建议',
  webOperation_suggestion_extra_input_text: '附加内容',
  webOperation_suggestion_extra_input_placeholder: '请输入以http://或以https://开头的链接地址',
  webOperation_suggestion_name_input_text: '消息名称',

  webOperation_suggestion_deleteLine1_modal_message: '该文件被删除后将无法恢复',
  webOperation_suggestion_deleteLine2_modal_message: '确认删除？',
  webOperation_suggestion_importError_modal_message: '处理建议导入失败，请检查以下数据：',
  webOperation_suggestion_importTemplate_fileName_text: '处理建议模板',

  webOperation_message_type_tableColumn_text: '消息类型',
  webOperation_message_pushMethod_tableColumn_text: '推送方式',
  webOperation_message_successCount_tableColumn_text: '成功条数',
  webOperation_message_failCount_tableColumn_text: '失败条数',
  webOperation_message_export_filename_text: '消息记录',
  webOperation_messageDetail_export_filename_text: '消息记录详情',
  webOperation_message_succeed_dictionnary_text: '成功',
  webOperation_message_failed_dictionnary_text: '失败',
  webOperation_message_recordId_tableColumn_text: '消息记录ID',
  webOperation_message_targetUserId_tableColumn_text: '推送用户ID',
  webOperation_message_account_tableColumn_text: '推送用户账号',
  webOperation_product_brief_input_text: '产品短描述',
  webOperation_product_description_input_text: '产品长描述',
  webOperation_product_spec_input_text: '技术规格',

  webOperation_product_faqSort_tableColumn_text: '排序',
  webOperation_product_faqId_tableColumn_text: '问题ID',
  webOperation_product_faqType_tableColumn_text: '问题类型',
  webOperation_product_question_tableColumn_text: '问题',
  webOperation_product_questionLangId_tableColumn_text: '问题多语言ID',
  webOperation_product_solution_tableColumn_text: '解决方法',
  webOperation_product_solutionLangId_tableColumn_text: '解决方法多语言ID',
  webOperation_product_deleteLine1_modal_message: '删除后，将不可恢复！',
  webOperation_product_deleteLine2_modal_message: '确认删除？',

  webOperation_product_addFaq_button_text: '添加FAQ',
  webOperation_product_editFaq_modal_title: '编辑FAQ',
  webOperation_product_viewFaq_modal_title: 'FAQ详情',
  webOperation_product_sortFaq_button_text: '排序FAQ',

  webOperation_product_faqType_select_placeholder: '请选择问题类型',
  webOperation_product_question_input_placeholder: '请输入问题',
  webOperation_product_solution_input_placeholder: '请输入解决方法',

  webOperation_product_faq_table_text: 'FAQ：',
  webOperation_product_manual_table_text: '用户手册',
  webOperation_product_guide_table_text: '操作指导',

  webOperation_file_url_tableColumn_text: '文件地址',

  webOperation_file_view_drawer_title: '查看文件',
  webOperation_file_edit_drawer_title: '编辑文件',
  webOperation_file_create_drawer_title: '添加文件',

  webOperation_file_add_button_title: '添加文件',
  webOperation_file_add_tooltip_title: '仅支持: ',
  webOperation_guide_sort_button_title: '排序操作指导',

  webOperation_sms_chargingId_tableColumn_text: '计费ID',
  webOperation_sms_msgId_tableColumn_text: '消息ID',
  webOperation_sms_msgTitle_tableColumn_text: '消息标题',
  webOperation_sms_chargingStartTime_tableColumn_text: '首条发送时间',
  webOperation_sms_chargingEndTime_tableColumn_text: '最后发送时间',
  webOperation_sms_msgType_tableColumn_text: '消息类型',
  webOperation_sms_commodityModel_tableColumn_text: '商品型号/Model #',
  webOperation_sms_totalSend_tableColumn_text: '已发送总条数',
  webOperation_sms_createTime_tableColumn_text: '创建时间',
  webOperation_sms_export_fileName_text: '短信用量统计',
  webOperation_sms_msgRecordId_tableColumn_text: '消息记录ID',
  webOperation_sms_toUserId_tableColumn_text: '推送用户ID',
  webOperation_sms_toUserPhoneNumber_tableColumn_text: '推送用户手机号',
  webOperation_sms_toUserRegion_tableColumn_text: '推送用户归属地区',
  webOperation_sms_pushTime_tableColumn_text: '推送时间',
  webOperation_sms_msgContent_tableColumn_text: '消息内容',
  webOperation_sms_pushType_tableColumn_text: '推送方式',
  webOperation_sms_detail_modal_title: '短信用量详情',
  webOperation_sms_pushStartTime_tableColumn_text: '推送开始时间',
  webOperation_sms_pushEndTime_tableColumn_text: '推送结束时间',
  webOperation_flow_totalPrice_tableColumn_text: '消费总计',
  webOperation_flow_deviceCount_tableColumn_text: '设备数量',
  webOperation_flow_chargingStartTime_tableColumn_text: '计费开始时间',
  webOperation_flow_chargingEndTime_tableColumn_text: '计费结束时间',
  webOperation_flow_iccid_tableColumn_text: 'SIM ID',
  webOperation_flow_totalDevicePrice_tableColumn_text: '单台设备消费总计',
  webOperation_flow_totalFlow_tableColumn_text: '流量总计（MB）',
  webOperation_flow_singleTotalFlow_tableColumn_text: '单台设备流量总计（MB)',
  webOperation_flow_deviceId_tableColumn_text: '设备ID',
  webOperation_flow_detail_modal_title: '流量费用详情',

  webOperation_device_export_filename_text: '设备列表',
  webOperation_device_deviceId_tableColumn_text: '设备ID',
  webOperation_device_sn_tableColumn_text: 'SN',
  webOperation_device_pid_tableColumn_text: 'PID',
  webOperation_device_categoryId_tableColumn_text: '品类名称',
  webOperation_device_categoryName_tableColumn_text: '品类名称',
  webOperation_device_brandName_tableColumn_text: '品牌名称',
  webOperation_device_brandId_tableColumn_text: '品牌名称',
  webOperation_device_productModel_tableColumn_text: '产品型号',
  webOperation_device_commodityModel_tableColumn_text: '商品型号/Model #',
  webOperation_device_productType_tableColumn_text: '设备类型',
  webOperation_device_devicePhoneNum_tableColumn_text: '注册手机号',
  webOperation_device_isOnline_tableColumn_text: '在线状态',
  webOperation_device_status_tableColumn_text: '设备状态',
  webOperation_device_currentBindBusinessType_tableColumn_text: '当前绑定APP',
  webOperation_device_currentBindEgoUserId_tableColumn_text: '当前绑定EGO用户ID',
  webOperation_device_currentBindFleetCompanyId_tableColumn_text: '当前绑定Fleet租户ID',
  webOperation_device_usageStatus_tableColumn_text: '使用状态',
  webOperation_device_firstBindBusinessType_tableColumn_text: '首次绑定APP',
  webOperation_device_firstBindEgoUserId_tableColumn_text: '首次绑定EGO用户ID',
  webOperation_device_firstBindFleetCompanyId_tableColumn_text: '首次绑定Fleet租户ID',
  webOperation_device_firstBindFleetUserId_tableColumn_text: '首次绑定Fleet操作帐户ID',
  webOperation_device_activationTime_tableColumn_text: '激活时间',
  webOperation_device_firstBindTime_tableColumn_text: '首次绑定时间',
  webOperation_device_lastLoginTime_tableColumn_text: '最后上线时间',

  webOperation_device_nickName_tableColumn_text: '设备昵称',
  webOperation_device_productSnCode_tableColumn_text: 'SN code',
  webOperation_device_communicateMode_tableColumn_text: '联网方式',
  webOperation_device_customVersion_tableColumn_text: '用户固件版本号',
  webOperation_device_detail_tabs_text: '设备详情',
  webOperation_device_part_table_text: '总成零件',
  webOperation_device_upgrade_table_text: '固件升级记录',

  webOperation_part_componentName_tableColumn_text: '总成零件名称',
  webOperation_part_componentNo_tableColumn_text: '总成零件号',
  webOperation_part_componentType_tableColumn_text: '总成零件类型',
  webOperation_part_componentVersion_tableColumn_text: '当前固件版本号',
  webOperation_device_otaUpgradeHistory_common_text: '固件升级记录',

  webOperation_upgrade_newVersion_tableColumn_text: '目标固件版本号',
  webOperation_upgrade_oldVersion_tableColumn_text: '原始固件版本号',
  webOperation_upgrade_status_tableColumn_text: '升级结果',
  webOperation_upgrade_upgradeTime_tableColumn_text: '升级时间',
  webOperation_upgrade_userId_tableColumn_text: '操作用户ID',

  webOperation_device_disable_modal_title: '设备启用',
  webOperation_device_normal_modal_title: '设备停用',
  webOperation_device_disable_modal_message: '是否确定启用该设备？',
  webOperation_device_normal_modal_message:
    '请确认是否要停止该设备的使用权限？停用后设备将无法被该设备的已绑定用户进行控制。此操作为敏感操作，请确认已告知设备主人',

  // 租户管理
  webOperation_company_companyId_tableColumn_text: '租户ID',
  webOperation_company_companyName_tableColumn_text: '租户名称',
  webOperation_company_adminId_tableColumn_text: '管理员ID',
  webOperation_company_adminEmail_tableColumn_text: '管理员邮箱',
  webOperation_company_adminFirstName_tableColumn_text: '管理员First name',
  webOperation_company_adminLastName_tableColumn_text: '管理员Last name',
  webOperation_company_adminName_tableColumn_text: '管理员姓名',
  webOperation_company_companyState_tableColumn_text: '状态',
  webOperation_company_registerTime_tableColumn_text: '租户注册时间',
  webOperation_company_companyCountry_tableColumn_text: '租户所在国家',
  webOperation_company_companyAddress_tableColumn_text: '租户地址',
  webOperation_company_companyPost_tableColumn_text: '租户邮编',
  webOperation_company_changeTime_tableColumn_text: '变更时间',
  webOperation_company_detail_drawer_text: '租户详情',
  webOperation_company_detail_button_text: '租户详情',
  webOperation_company_cancel_button_text: '注销',
  webOperation_company_viewUpdate_button_text: '管理员变更记录',
  webOperation_company_adminHistory_drawer_text: '管理员变更记录',
  webOperation_company_stop_modal_title: '停用租户账号',
  webOperation_company_enable_modal_title: '启用租户账号',
  webOperation_company_cancel_modal_title: '注销租户账号',
  webOperation_company_sensitive_message_text: '此操作为敏感操作，请确认已告知租户管理员',
  webOperation_company_confirmToStopLine1_message_text: '请确认是否要停止该租户账号的使用权限？',
  webOperation_company_confirmToStopLine2_message_text:
    '停用后该租户下的全部员工将无法正常登录Fleet Service平台',
  webOperation_company_confirmToCancelLine1_message_text: '请确认是否要注销该租户账号的使用权限？',
  webOperation_company_confirmToCancelLine2_message_text:
    '注销后该租户下的全部设备和员工都将被解除绑定关系',
  webOperation_company_confirmToInputPassword_message_text: '请再次输入您的登录密码',
  webOperation_company_password_input_placeholder: '您的登录密码',
  webOperation_company_confirmToEnable_message_text: '请确认是否启用该租户账号的使用权限？',
  // 租户与设备关系
  webOperation_companyDevice_companyId_tableColumn_text: '租户ID',
  webOperation_companyDevice_companyName_tableColumn_text: '租户名称',
  webOperation_companyDevice_count_tableColumn_text: '已绑定设备数量',
  webOperation_companyDevice_deviceId_tableColumn_text: '已绑定设备ID',
  webOperation_companyDevice_deviceSn_tableColumn_text: '已绑定设备SN',
  webOperation_companyDevice_brandId_tableColumn_text: '已绑定设备品牌',
  webOperation_companyDevice_categoryCode_tableColumn_text: '已绑定设备品类',
  webOperation_companyDevice_model_tableColumn_text: '已绑定设备商品型号/Model #',
  // 租户员工
  webOperation_companyEmployee_userId_tableColumn_text: '帐户ID',
  webOperation_companyEmployee_userEmail_tableColumn_text: '员工邮箱',
  webOperation_companyEmployee_userFirstName_tableColumn_text: '员工First name',
  webOperation_companyEmployee_userLastName_tableColumn_text: '员工Last name',
  webOperation_companyEmployee_userName_tableColumn_text: '员工姓名',
  webOperation_companyEmployee_userRole_tableColumn_text: '员工角色',
  webOperation_companyEmployee_companyId_tableColumn_text: '所属租户ID',
  webOperation_companyEmployee_companyName_tableColumn_text: '所属租户名称',
  webOperation_companyEmployee_userSourceType_tableColumn_text: '员工帐户来源',
  webOperation_companyEmployee_invitationEmail_tableColumn_text: '邀请人邮箱',
  webOperation_companyEmployee_userState_tableColumn_text: '状态',
  webOperation_companyEmployee_isEnabled_tableColumn_text: '用户启用状态',
  webOperation_companyEmployee_activeTime_tableColumn_text: '员工帐户激活时间',
  webOperation_companyEmployee_registerTime_tableColumn_text: '员工帐户注册时间',
  webOperation_companyEmployee_companyCountry_tableColumn_text: '租户所在国家',
  webOperation_companyEmployee_companyAddress_tableColumn_text: '租户地址',
  webOperation_companyEmployee_companyPost_tableColumn_text: '租户邮编',
  webOperation_companyEmployee_detail_common_text: '员工帐户详情',
  webOperation_companyEmployee_stop_modal_title: '停用员工账号',
  webOperation_companyEmployee_enable_modal_title: '启用员工账号',
  webOperation_companyEmployee_sensitive_message_text: '此操作为敏感操作，请确认已告知员工本人',
  webOperation_companyEmployee_confirmToStopLine1_message_text:
    '请确认是否要停止该员工帐户的使用权限？',
  webOperation_companyEmployee_confirmToStopLine2_message_text:
    '停用后该员工帐户将无法正常登录Fleet Service平台',
  webOperation_companyEmployee_confirmToEnable_message_text:
    '请确认是否要启用该员工帐户的使用权限？',

  webOperation_companyDealer_country_tableColumn_text: '所在国家',
  webOperation_companyDealer_state_tableColumn_text: '所在州',
  webOperation_companyDealer_city_tableColumn_text: '所在城市',
  webOperation_companyDealer_lat_tableColumn_text: '纬度',
  webOperation_companyDealer_lng_tableColumn_text: '经度',

  webOperation_companyDealer_title_tableColumn_text: '经销商名称',
  webOperation_companyDealer_addressOne_tableColumn_text: '一级地址1',
  webOperation_companyDealer_addressTwo_tableColumn_text: '二级地址2',
  webOperation_companyDealer_town_tableColumn_text: '所在镇',
  webOperation_companyDealer_region_tableColumn_text: '所在地区',
  webOperation_companyDealer_postcode_tableColumn_text: '邮编',
  webOperation_companyDealer_phoneNumber_tableColumn_text: '电话',
  webOperation_companyDealer_email_tableColumn_text: '邮箱',
  webOperation_companyDealer_geolocation_tableColumn_text: '经纬度',
  webOperation_companyDealer_langCode_tableColumn_text: '语种',
  webOperation_companyDealer_premium_tableColumn_text: '品质',
  webOperation_companyDealer_proXRangeSeller_tableColumn_text: 'Pro_x_range产品经销商',
  webOperation_companyDealer_rideOnSeller_tableColumn_text: 'Ride_on产品经销',
  webOperation_companyDealer_ad_tableColumn_text: '广告代理商',
};
