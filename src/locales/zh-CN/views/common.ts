export default {
  webCommon_page_remark_common_text: '备注',
  webCommon_page_remark_input_placeholder: '请输入备注信息',
  webCommon_page_remark_input_rule: '请输入备注',
  webCommon_page_langId_common_text: '多语言ID',
  webCommon_page_deviceId_tableColumn_text: '设备ID',
  webCommon_page_deviceName_tableColumn_text: '设备名称',
  webCommon_page_sort_tableColumn_text: '排序',
  webCommon_page_index_tableColumn_text: '序号',
  webCommon_page_statusCode_tableColumn_text: '发布状态',
  webCommon_page_view_common_text: '查看',
  webCommon_page_edit_common_text: '编辑',
  webCommon_page_create_button_text: '创建',
  webCommon_page_add_button_text: '添加',
  webCommon_page_delete_tableButton_linkText: '删除',
  webCommon_page_release_tableButton_linkText: '发布',
  webCommon_page_withdraw_tableButton_linkText: '下架',
  webCommon_page_batchDelete_tableButton_linkText: '批量删除',
  webCommon_page_detail_common_text: '详情',
  webCommon_page_cancel_button_text: '取消',
  webCommon_page_close_button_text: '关闭',
  webCommon_page_confirm_button_text: '确定',
  webCommon_page_search_button_text: '搜索',
  webCommon_page_reset_button_text: '重置',
  webCommon_page_expand_button_text: '展开',
  webCommon_page_collapse_button_text: '收起',
  webCommon_page_export_button_text: '导出',
  webCommon_page_import_button_text: '导入',
  webCommon_page_config_button_text: '配置',
  webCommon_page_disable_tableButton_linkText: '停用',
  webCommon_page_enable_tableButton_linkText: '启用',
  webCommon_page_copy_button_linkText: '复制',
  webCommon_page_copyMessage_button_linkText: '复制消息',
  webCommon_page_copyResult_button_text: '复制结果',
  webCommon_page_pushResult_tableButton_linkText: '推送结果',
  webCommon_page_download_common_linkText: '下载',
  webCommon_page_browse_common_linkText: '浏览',
  webCommon_page_more_button_linkText: '更多',
  webCommon_page_option_tableColumn_text: '操作',
  webCommon_page_downloadTemplate_button_text: '模板下载',
  webCommon_page_confirmToDelete_modal_title: '删除确认',
  webCommon_page_delete_modal_message: '删除后，将不可恢复！确认删除？',
  webCommon_page_copySuccessed_toast_text: '复制成功',
  webCommon_page_operateSuccessed_toast_text: '操作成功',
  webCommon_page_operateFailed_toast_text: '操作失败',
  webCommon_page_downloading_toast_text: '正在处理中',
  webCommon_page_importSuccessed_toast_text: '导入成功',
  webCommon_page_importFailed_toast_text: '导入失败，请重试',
  webCommon_page_exportFailed_modal_title: '导出失败',
  webCommon_page_exportFailed_modal_message: '导出失败，请重试',
  webCommon_page_confirmText_button_text: '确认',
  webCommon_page_file_select_placeholder: '请选择文件',
  webCommon_page_selectToImport_button_text: '请选择文件',
  webCommon_page_delete_input_placeholder: '请输入 "Yes" 进行二次确认',
  webCommon_page_uploadImageType_validator_message: '请上传PNG格式的图片',
  webCommon_page_uploadError_toast_message: '文件格式错误',
  webCommon_page_uploadImageSize_validator_message: '上传图片大小不能超过800K',
  webCommon_page_uploadSize_validator_message: '图片尺寸不正确',
  webCommon_page_createBy_tableColumn_text: '创建人',
  webCommon_page_createTime_tableColumn_text: '创建时间',
  webCommon_page_updateBy_tableColumn_text: '修改人',
  webCommon_page_updateTime_tableColumn_text: '修改时间',
  webCommon_page_noAccess_toast_text: '权限不足',
  webCommon_page_digit_validator_message: '请输入数字',
  webCommon_page_str36_validator_message:
    '请输入1~36位字符，支持大小写字母、数字、以及_-./?#特殊字符',
  webCommon_page_str128_validator_message:
    '请输入1~128位字符，支持大小写字母、数字、以及_-./?#特殊字符',
  webCommon_page_en128_validator_message: '请输入1~128位字符，仅支持大小写字母、数字、下划线',
  webCommon_page_len128_validator_message: '请输入1~128位字符',
  webCommon_page_len300_validator_message: '请输入1~300位字符',
  webCommon_page_len500_validator_message: '请输入1~500位字符',
  webCommon_page_len5000_validator_message: '请输入1~5000位字符',
  webCommon_page_len3000_validator_message: '请输入1~3000位字符',
  webCommon_page_len36_validator_message: '请输入1~36位字符',
  webCommon_page_internalError_toast_text: '内部错误',
  webCommon_page_prefix_pagination_label: '共',
  webCommon_page_suffix_pagination_label: '条',
  webCommon_page_select_common_placeholder: '请选择',
  webCommon_page_input_common_placeholder: '请输入',
  webCommon_page_add_validator_message: '请添加',
  webCommon_page_imageUrl_input_text: '图片地址',
  webCommon_page_imageUrl_input_placeholder: '请输入图片链接',
  webCommon_page_url_validator_message: '请输入正确的链接，以http://或https://开头',
  webCommon_page_s3_validator_message: '请输入正确的S3链接',
  webCommon_page_upload_select_placeholder: '请上传图片',
  webCommon_page_delete_validator_message: '请输入Yes后提交',
  webCommon_page_upload_button_text: '上传',
  webCommon_page_noImageData_message_text: '暂无图片',
  webCommon_page_imageSize_tip_message: '（图片规格：文件体积不大于800KB的PNG格式图片）',
  webCommon_page_uploadFailed_toast_message: '文件上传失败，请重新上传',
  webCommon_page_comma_validator_message: '请以英文逗号分隔',
  webCommon_page_deleteForbid_modal_title: '禁止删除',
  webCommon_page_richtext_input_placeholder: '请输入内容...',
  webCommon_page_apiError_toast_text: '服务不可用，服务器错误或网络未连接',
  webCommon_page_limit_select_placeholder: '请至少选择一条数据',
  webCommon_page_uploadError_toast_text: '请上传正确的格式文件',
  webCommon_page_refresh_button_text: '刷新',
  webCommon_page_forbidDelete_toast_text: '禁止删除',
  webCommon_page_number_input_placeholder: '请输入数字，以","分隔',
  webCommon_page_languageId_label_text: '多语言ID:',
  webCommon_compare_eq_dictionnary_text: '等于',
  webCommon_compare_include_dictionnary_text: '包含',
  webCommon_compare_gt_dictionnary_text: '大于',
  webCommon_compare_lt_dictionnary_text: '小于',
  webCommon_page_deleteConfirm_input_label: '请在下方输入框中输入“Yes”进行二次确认。',
  webCommon_page_startDate_searchLabel_placeholder: '开始日期',
  webCommon_page_endDate_searchLabel_placeholder: '结束日期',
  webCommon_page_time_validator_message: '结束时间不能小于开始时间',
};
