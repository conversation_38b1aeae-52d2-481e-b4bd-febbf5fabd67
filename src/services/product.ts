import { request } from 'umi';
import { assign, get, map, omit, reduce, upperFirst } from 'lodash-es';
import { downloadByUrl } from '@/utils/download';

enum Api {
  // 产品发布配置API
  GetProductList = '/operation-platform/product/list',
  DeleteProduct = '/operation-platform/product/delete',
  ExportProductList = '/operation-platform/product/export',
  GetProductById = '/operation-platform/product/detail',
  CreateProduct = '/operation-platform/product/create/independent',
  EditProduct = '/operation-platform/product/edit/operation',
  ImportTemplate = '/operation-platform/product/import/pattern/download',
  ProductRelease = '/operation-platform/product/release',
  ProductWithdraw = '/operation-platform/product/withdraw',

  GetRnConfig = '/operation-platform/rn/config/configDetail',
  CancelRnRelease = '/operation-platform/rn/config/cancelApplyRelease',

  // 固件发布配置API
  GetFirmwareList = '/operation-platform/ota/job/list/config',
  GetFirmwareReleaseDetail = '/operation-platform/ota/job/release/detail',
  GetFirmwareUpgradeResult = '/operation-platform/ota/job/result/list',
  SetFirmwareConfig = '/operation-platform/ota/job/release/config',
  UpdateFirmwareStatus = '/operation-platform/ota/job/status/update',
  ViewRefuseFirmwareReason = '/operation-platform/ota/job/refuseReason/release',
  ViewRefuseFirmwareTestReason = '/operation-platform/ota/job/refuseReason/test',
  ViewRefuseFirmwareStopReason = '/operation-platform/ota/job/refuseReason/stop',
  ViewRefuseFirmwareNullifyReason = '/operation-platform/ota/job/refuseReason/nullify',
  DownloadFirmware = '/operation-platform/ota/job/downloadUrl',

  // 配件清单
  ListProductPart = '/operation-platform/product/parts/page',
  ListSortableProductPart = '/operation-platform/product/parts/list',
  CreateProductPart = '/operation-platform/product/parts/add',
  DisabledProductPart = '/operation-platform/product/parts/findPartsIdList',
  UpdateProductPart = '/operation-platform/product/parts/edit',
  ViewProductPart = '/operation-platform/product/parts/detail',
  DeleteProductPart = '/operation-platform/product/parts/delete',
  SortProductPart = '/operation-platform/product/parts/order',

  ViewFaq = '/operation-platform/postSale/detail',
  UpdateShortDescription = '/operation-platform/postSale/edit/short',
  UpdateLongDescription = '/operation-platform/postSale/edit/long',
  UpdateTechnicalSpecification = '/operation-platform/postSale/edit/tech',
  ListFaq = '/operation-platform/postSale/faq/list',
  DeleteFaq = '/operation-platform/postSale/faq/delete',
  CreateFaq = '/operation-platform/postSale/faq/add',
  UpdateFaq = '/operation-platform/postSale/faq/edit',
  SortFaq = '/operation-platform/postSale/faq/order',

  ListManual = '/operation-platform/postSale/manual/list',
  DeleteManual = '/operation-platform/postSale/manual/delete',
  CreateManual = '/operation-platform/postSale/manual/add',
  UpdateManual = '/operation-platform/postSale/manual/edit',
  DownloadManual = '/operation-platform/postSale/manual/download',

  ListGuide = '/operation-platform/postSale/operationGuidance/list',
  DeleteGuide = '/operation-platform/postSale/operationGuidance/delete',
  CreateGuide = '/operation-platform/postSale/operationGuidance/add',
  UpdateGuide = '/operation-platform/postSale/operationGuidance/edit',
  DownloadGuide = '/operation-platform/postSale/operationGuidance/download',
  SortGuide = '/operation-platform/postSale/operationGuidance/order',

  ApproveProductPermissionList = '/operation-platform/release/product/approve/list',
  AddProductApprovePermission = '/operation-platform/release/product/approve/add',
  DeleteProductApprovePermission = '/operation-platform/release/product/approve/delete',
  EditProductPermissionList = '/operation-platform/release/product/edit/list',
  AddProductEditPermission = '/operation-platform/release/product/edit/add',
  DeleteProductEditPermission = '/operation-platform/release/product/edit/delete',
  TestProductPermissionList = '/operation-platform/release/product/testApprove/list',
  AddProductTestPermission = '/operation-platform/release/product/testApprove/add',
  DeleteProductTestPermission = '/operation-platform/release/product/testApprove/delete',

  ApproveOtaPermissionList = '/operation-platform/release/ota/approve/list',
  AddOtaApprovePermission = '/operation-platform/release/ota/approve/add',
  DeleteOtaApprovePermission = '/operation-platform/release/ota/approve/delete',
  TestOtaPermissionList = '/operation-platform/release/ota/testApprove/list',
  AddOtaTestPermission = '/operation-platform/release/ota/testApprove/add',
  DeleteOtaTestPermission = '/operation-platform/release/ota/testApprove/delete',

  ApproveRnPermissionList = '/operation-platform/release/rn/approve/list',
  AddRnApprovePermission = '/operation-platform/release/rn/approve/add',
  DeleteRnApprovePermission = '/operation-platform/release/rn/approve/delete',
  TestRnPermissionList = '/operation-platform/release/rn/testApprove/list',
  AddRnTestPermission = '/operation-platform/release/rn/testApprove/add',
  DeleteRnTestPermission = '/operation-platform/release/rn/testApprove/delete',

  EmployeeList = '/operation-platform/release/sysUser/page',
}
/**
 * 排序产品操作指导
 *
 * @remarks
 *
 * @param data - productOperationGuidanceIds：待排序的操作指导； productId：产品ID
 * @returns
 *
 * @beta
 */
export function sortGuide(data: { productOperationGuidanceIds: string[]; productId: string }) {
  return request(Api.SortGuide, {
    method: 'POST',
    data,
  });
}
/**
 * 下载产品操作指导
 *
 * @remarks
 *
 * @param data - operateItemId：操作指导ID； productId：产品ID；name：操作指导文件名
 * @returns
 *
 * @beta
 */
export function downloadGuide(data: { operateItemId: string; productId: string }, name = '') {
  return request(Api.DownloadGuide, {
    method: 'POST',
    data,
  }).then((res) => {
    const url = get(res, ['data']);
    if (url) {
      downloadByUrl(url, name);
    }
  });
}
/**
 * 产品操作指导数据格式化
 *
 * @remarks
 *
 * @param data - API.ManualItem
 * @returns
 *
 * @beta
 */
const transformGuide = (data: API.ManualItem) => {
  return {
    operateItemId: data.productOperationGuidanceId,
    productId: data.productId,
    operationGuidance: {
      description: data.description?.message,
      descriptionLangId: data.description?.langId,
      format: data.ext,
      name: data.fileName?.message,
      nameLangId: data.fileName?.langId,
      size: data.fileSize,
      typeCode: data.type,
      uploadFileName: data.s3key,
      url: data.type === '1' ? undefined : data.url,
    },
  };
};
/**
 * 编辑产品操作指导
 *
 * @remarks
 *
 * @param data - API.ManualItem
 * @returns
 *
 * @beta
 */
export function updateGuide(data: API.ManualItem) {
  return request(Api.UpdateGuide, {
    method: 'POST',
    data: transformGuide(data),
  });
}
/**
 * 添加产品操作指导
 *
 * @remarks
 *
 * @param data - API.ManualItem
 * @returns
 *
 * @beta
 */
export function createGuide(data: API.ManualItem) {
  return request(Api.CreateGuide, {
    method: 'POST',
    data: transformGuide(data),
  });
}
/**
 * 删除产品操作指导
 *
 * @remarks
 *
 * @param data - operateItemId：操作指导ID； productId：产品ID；
 * @returns
 *
 * @beta
 */
export function deleteGuide(data: { operateItemId: string; productId: string }) {
  return request(Api.DeleteGuide, {
    method: 'POST',
    data,
  });
}
/**
 * 获取产品操作指导列表
 *
 * @remarks
 *
 * @param data - req：产品ID
 * @returns
 *
 * @beta
 */
export function listGuide(data: { req: string }) {
  return request(Api.ListGuide, {
    method: 'POST',
    type: 'all',
    data,
  }).then((res) => {
    return {
      ...res,
      data: map(res.data, (item = {}) => {
        const { operationGuidance = {} } = item;
        return {
          ...item,
          ...operationGuidance,
          description: {
            message: operationGuidance.description,
            langId: operationGuidance.descriptionLangId,
          },
          fileName: {
            message: operationGuidance.name,
            langId: operationGuidance.nameLangId,
          },
          ext: operationGuidance.format,
          fileSize: operationGuidance.size,
          ...(operationGuidance.typeCode === '1'
            ? { file: operationGuidance.url }
            : { url: operationGuidance.url }),
          type: operationGuidance.typeCode,
          s3key: operationGuidance.uploadFileName,
        };
      }),
    };
  });
}
/**
 * 下载产品用户手册
 *
 * @remarks
 *
 * @param data - operateItemId：用户手册ID；productId：产品ID；name：用户手册文件名
 * @returns
 *
 * @beta
 */
export function downloadManual(data: { operateItemId: string; productId: string }, name = '') {
  return request(Api.DownloadManual, {
    method: 'POST',
    data,
  }).then((res) => {
    const url = get(res, ['data']);
    if (url) {
      // downloadByUrl(url, name);
      window.open(url);
    }
  });
}
/**
 * 产品用户手册数据格式化
 *
 * @remarks
 *
 * @param data - API.ManualItem
 * @returns
 *
 * @beta
 */
const transform = (data: API.ManualItem) => {
  return {
    manual: {
      description: data.description?.message,
      descriptionLangId: data.description?.langId,
      format: data.ext,
      name: data.fileName?.message,
      nameLangId: data.fileName?.langId,
      manualId: data.manualId,
      // productId: data.productId,
      size: data.fileSize,
      typeCode: data.type,
      uploadFileName: data.s3key,
      url: data.type === '1' ? undefined : data.url,
    },
    operateItemId: data.productManualId,
    productId: data.productId,
  };
};
/**
 * 编辑产品用户手册
 *
 * @remarks
 *
 * @param data - API.ManualItem
 * @returns
 *
 * @beta
 */
export function updateManual(data: API.ManualItem) {
  return request(Api.UpdateManual, {
    method: 'POST',
    data: transform(data),
  });
}
/**
 * 添加产品用户手册
 *
 * @remarks
 *
 * @param data - API.ManualItem
 * @returns
 *
 * @beta
 */
export function createManual(data: API.ManualItem) {
  return request(Api.CreateManual, {
    method: 'POST',
    data: transform(data),
  });
}
/**
 * 删除产品用户手册
 *
 * @remarks
 *
 * @param data - operateItemId：用户手册ID；productId：产品ID；
 * @returns
 *
 * @beta
 */
export function deleteManual(data: { operateItemId: string; productId: string }) {
  return request(Api.DeleteManual, {
    method: 'POST',
    data,
  });
}
/**
 * 获取产品用户手册列表
 *
 * @remarks
 *
 * @param data - req：产品ID
 * @returns
 *
 * @beta
 */
export function listManual(data: { req: string }) {
  return request(Api.ListManual, {
    method: 'POST',
    type: 'all',
    data,
  }).then((res) => {
    return {
      ...res,
      data: map(res.data, (item = {}) => {
        const { manual = {} } = item;
        return {
          ...item,
          ...manual,
          description: {
            message: manual.description,
            langId: manual.descriptionLangId,
          },
          fileName: {
            message: manual.name,
            langId: manual.nameLangId,
          },
          ext: manual.format,
          fileSize: manual.size,
          file: manual.url,
          type: manual.typeCode,
          s3key: manual.uploadFileName,
        };
      }),
    };
  });
}
/**
 * 排序产品FAQ
 *
 * @remarks
 *
 * @param data - productFaqIds：FAQ；productId：产品ID
 * @returns
 *
 * @beta
 */
export function sortFaq(data: { productFaqIds: string[]; productId: string }) {
  return request(Api.SortFaq, {
    method: 'POST',
    data,
  });
}
/**
 * 编辑产品FAQ
 *
 * @remarks
 *
 * @param data - API.SaleFaqDto
 * @returns
 *
 * @beta
 */
export function updateFaq(data: API.SaleFaqDto) {
  return request(Api.UpdateFaq, {
    method: 'POST',
    data: {
      ...omit(data, ['id']),
      productId: data.id,
    },
  });
}
/**
 * 添加产品FAQ
 *
 * @remarks
 *
 * @param data - API.SaleFaqDto
 * @returns
 *
 * @beta
 */
export function createFaq(data: API.SaleFaqDto) {
  return request(Api.CreateFaq, {
    method: 'POST',
    data: {
      ...omit(data, ['id']),
      productId: data.id,
    },
  });
}
/**
 * 删除产品FAQ
 *
 * @remarks
 *
 * @param data - operateItemId：FAQ；productId：产品ID
 * @returns
 *
 * @beta
 */
export function deleteFaq(data: { operateItemId: string; productId: string }) {
  return request(Api.DeleteFaq, {
    method: 'POST',
    data,
  });
}
/**
 * 获取产品FAQ
 *
 * @remarks
 *
 * @param data - faqId：faq id；productId：产品id；title：问题标题；titleLangId：标题多语言id；typeCode：问题类型
 * @returns
 *
 * @beta
 */
export function listFaq(data: {
  /** faq id */
  faqId?: string;
  /** 产品id */
  productId?: string;
  /** 问题题目 */
  title?: string;
  /** 题目多语言id */
  titleLangId?: number;
  /** 问题类型 */
  typeCode?: string;
}) {
  return request(Api.ListFaq, {
    method: 'POST',
    type: 'all',
    data,
  }).then((res = {}) => {
    return {
      ...res,
      data: map(res.data, (item = {}) => {
        return {
          ...item,
          ...item.faq,
          ...reduce(
            ['title', 'answer'],
            (result, cur) => {
              result[cur] = {
                message: get(item, ['faq', cur]),
                langId: get(item, ['faq', `${cur}LangId`]),
              };
              return result;
            },
            {},
          ),
        };
      }),
    };
  });
}
/**
 * 编辑产品售后内容
 *
 * @remarks
 *
 * @param type - 属性类型（shortDescription：短描述；longDescription：长描述；technicalSpecification：技术规格）
 * @param data - item：编辑的内容；productId：产品ID
 * @returns
 *
 * @beta
 */
export function updateDesc(type: string, data: { item: string; productId: string }) {
  return request(Api[`Update${upperFirst(type)}`], {
    method: 'POST',
    data,
  });
}
/**
 * 获取产品售后内容
 *
 * @remarks
 *
 * @param data - productId：产品ID
 * @returns
 *
 * @beta
 */
export function viewFaq(productId: string) {
  return request(Api.ViewFaq, {
    method: 'POST',
    data: { req: productId },
  }).then((res) => {
    const data = res?.data || {};
    res.data = assign({}, res, {
      data: {
        ...data,
        ...reduce(
          ['shortDescription', 'longDescription', 'technicalSpecification'],
          (result, item) => {
            result[item] = {
              message: get(data, [item]),
              langId: get(data, [`${item}LangId`]),
            };
            return result;
          },
          {},
        ),
      },
    });
    return res;
  });
}
/**
 * 分页查询产品配件清单
 *
 * @remarks
 *
 * @param data - API.PartPageParams
 * @returns
 *
 * @beta
 */
export function listProductPart(data: Partial<API.PartPageParams>) {
  return request(Api.ListProductPart, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 获取产品全部配件清单
 *
 * @remarks
 *
 * @param data - req：产品ID
 * @returns
 *
 * @beta
 */
export function listSortableProductPart(data: { req: string }) {
  return request(Api.ListSortableProductPart, {
    method: 'POST',
    type: 'all',
    data,
  });
}
/**
 * 添加产品配件
 *
 * @remarks
 *
 * @param data - productId：产品ID；partsId：添加的配件列表
 * @returns
 *
 * @beta
 */
export function createProductPart(data: { productId: string; partsId: string[] }) {
  return request(Api.CreateProductPart, {
    method: 'POST',
    data,
  });
}
/**
 * 获取已添加的产品配件
 *
 * @remarks 添加配件时，已被添加的配件不可再次选择
 *
 * @param productId - 产品ID
 * @returns
 *
 * @beta
 */
export function disabledProductPart(productId: string) {
  return request(Api.DisabledProductPart, {
    method: 'POST',
    data: { req: productId },
  }).then((res) => {
    return {
      ...res,
      data: reduce(
        res.data,
        (result, cur) => {
          result[cur] = true;
          return result;
        },
        {},
      ),
    };
  });
}
/**
 * 编辑产品配件
 *
 * @remarks
 *
 * @param data - instanceId：列表显示ID；maintenancePeriod：维保周期
 * @returns
 *
 * @beta
 */
export function updateProductPart(data: { instanceId?: string; maintenancePeriod?: string }) {
  return request(Api.UpdateProductPart, {
    method: 'POST',
    data,
  });
}
/**
 * 获取产品配件详情
 *
 * @remarks
 *
 * @param data - req：列表显示ID
 * @returns
 *
 * @beta
 */
export function viewProductPart(data: { req: string }) {
  return request(Api.ViewProductPart, {
    method: 'POST',
    data,
  });
}
/**
 * 删除产品配件
 *
 * @remarks
 *
 * @param data - req：列表显示ID
 * @returns
 *
 * @beta
 */
export function deleteProductPart(data: { req: string }) {
  return request(Api.DeleteProductPart, {
    method: 'POST',
    data,
  });
}
/**
 * 分页查询产品列表
 *
 * @remarks
 *
 * @param data - API.ProductPageParams
 * @returns
 *
 * @beta
 */
export function getProductList(data: Partial<API.ProductPageParams>) {
  return request(Api.GetProductList, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 删除产品
 *
 * @remarks
 *
 * @param req - 产品ID
 * @returns
 *
 * @beta
 */
export function deleteProduct(req: string) {
  return request(Api.DeleteProduct, {
    method: 'POST',
    data: { req },
  });
}
/**
 * 导出产品
 *
 * @remarks
 *
 * @param data - API.ProductPageParams
 * @returns
 *
 * @beta
 */
export const exportProducts = (data: Partial<API.ProductPageParams>) => {
  return request(Api.ExportProductList, {
    method: 'post',
    type: 'string',
    data,
    responseType: 'blob',
    getResponse: true,
  });
};
/**
 * 导入产品模板下载
 *
 * @remarks
 *
 * @param
 * @returns
 *
 * @beta
 */
export const getImportTemplate = () => {
  return request(Api.ImportTemplate, {
    method: 'get',
    type: 'string',
    responseType: 'blob',
    getResponse: true,
  });
};
/**
 * 获取产品详情
 *
 * @remarks
 *
 * @param id - 产品ID
 * @returns
 *
 * @beta
 */
export const getProductById = (id: string) => {
  return request(Api.GetProductById, {
    method: 'POST',
    data: { req: id },
  });
};
/**
 * 添加独立产品
 *
 * @remarks
 *
 * @param data - API.ProductItem
 * @returns
 *
 * @beta
 */
export function createProduct(data: API.ProductItem) {
  return request(Api.CreateProduct, {
    method: 'POST',
    data,
  });
}
/**
 * 编辑独立产品
 *
 * @remarks
 *
 * @param data - API.ProductItem
 * @returns
 *
 * @beta
 */
export function editProduct(data: API.ProductItem) {
  return request(Api.EditProduct, {
    method: 'POST',
    data,
  });
}
/**
 * 发布产品
 *
 * @remarks
 *
 * @param id
 * @returns
 *
 * @beta
 */
export function productRelease(id: string) {
  return request(Api.ProductRelease, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * 下架产品
 *
 * @remarks
 *
 * @param id
 * @returns
 *
 * @beta
 */
export function productWithdraw(id: string) {
  return request(Api.ProductWithdraw, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * 获取RN发布配置
 *
 * @remarks
 *
 * @param id - RN ID
 * @returns
 *
 * @beta
 */
export async function getRnConfig(id: string) {
  try {
    const res = await request(Api.GetRnConfig, {
      method: 'POST',
      data: { req: id },
    });
    return res.data;
  } catch (e) {
    return {};
  }
}
/**
 * 撤回RN发布申请
 *
 * @remarks
 *
 * @param id - RN ID
 * @returns
 *
 * @beta
 */
export function cancelRnRelease(id: string) {
  return request(Api.CancelRnRelease, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * 获取产品固件列表
 *
 * @remarks
 *
 * @param data - API.ProductPageParams
 * @returns
 *
 * @beta
 */
export function getFirmwareList(data: Partial<API.ProductPageParams>) {
  return request(Api.GetFirmwareList, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 获取产品固件发布详情
 *
 * @remarks
 *
 * @param id - 固件ID
 * @returns
 *
 * @beta
 */
export const getFirmwareReleaseDetail = (id: string) => {
  return request(Api.GetFirmwareReleaseDetail, {
    method: 'POST',
    data: { req: id },
  });
};
/**
 * 获取产品固件升级结果
 *
 * @remarks
 *
 * @param data - API.UpgradeResultPageParams
 * @returns
 *
 * @beta
 */
export const getFirmwareUpgradeResult = (data: Partial<API.UpgradeResultPageParams>) => {
  return request(Api.GetFirmwareUpgradeResult, {
    method: 'POST',
    type: 'entry',
    data,
  });
};
/**
 * 设置固件发布配置
 *
 * @remarks
 *
 * @param data - API.ProductItem
 * @returns
 *
 * @beta
 */
export function setFirmwareConfig(data: API.ProductItem) {
  return request(Api.SetFirmwareConfig, {
    method: 'POST',
    data,
  });
}
/**
 * 下载固件
 *
 * @remarks
 *
 * @param key - 固件ID
 * @returns
 *
 * @beta
 */
export async function downloadFirmware(key: string) {
  try {
    const res = await request(Api.DownloadFirmware, {
      method: 'POST',
      data: { req: key },
    });
    return res.data;
  } catch (e) {
    return {};
  }
}
/**
 * 申请发布固件
 *
 * @remarks
 *
 * @param id - 固件ID
 * @returns
 *
 * @beta
 */
export function applyFirmwareRelease(id: string) {
  return request(Api.UpdateFirmwareStatus, {
    method: 'POST',
    data: { operation: 'APPLY', jobId: id },
  });
}
/**
 * 撤回申请发布固件
 *
 * @remarks
 *
 * @param id - 固件ID
 * @returns
 *
 * @beta
 */
export function cancelFirmwareRelease(id: string) {
  return request(Api.UpdateFirmwareStatus, {
    method: 'POST',
    data: { operation: 'APPLY_CANCEL', jobId: id },
  });
}
/**
 * 申请停止发布固件
 *
 * @remarks
 *
 * @param id - 固件ID
 * @returns
 *
 * @beta
 */
export function applyFirmwareStopRelease(id: string) {
  return request(Api.UpdateFirmwareStatus, {
    method: 'POST',
    data: { operation: 'STOP_APPLY', jobId: id },
  });
}
/**
 * 撤回停止发布固件申请
 *
 * @remarks
 *
 * @param id - 固件ID
 * @returns
 *
 * @beta
 */
export function cancelFirmwareStopRelease(id: string) {
  return request(Api.UpdateFirmwareStatus, {
    method: 'POST',
    data: { operation: 'STOP_CANCEL', jobId: id },
  });
}
/**
 * 申请固件作废
 *
 * @remarks
 *
 * @param id - 固件ID
 * @returns
 *
 * @beta
 */
export function applyFirmwareNullifyRelease(id: string) {
  return request(Api.UpdateFirmwareStatus, {
    method: 'POST',
    data: { operation: 'NULLIFY_APPLY', jobId: id },
  });
}
/**
 * 撤回固件作废申请
 *
 * @remarks
 *
 * @param id - 固件ID
 * @returns
 *
 * @beta
 */
export function cancelFirmwareNullifyRelease(id: string) {
  return request(Api.UpdateFirmwareStatus, {
    method: 'POST',
    data: { operation: 'NULLIFY_APPLY_CANCEL', jobId: id },
  });
}
/**
 * 调整产品配件顺序
 *
 * @remarks
 *
 * @param data - 产品配件列表
 * @returns
 *
 * @beta
 */
export function sortProductPart(data: string[]) {
  return request(Api.SortProductPart, {
    method: 'POST',
    data,
  });
}
/**
 * 获取固件发布申请被驳回原因
 *
 * @remarks
 *
 * @param data - API.ReqCommonParams
 * @returns
 *
 * @beta
 */
export async function viewRefuseFirmwareReason(data: API.ReqCommonParams) {
  try {
    const res = await request(Api.ViewRefuseFirmwareReason, {
      method: 'POST',
      data,
    });
    return { info: res.data };
  } catch (e) {
    return {};
  }
}
/**
 * 获取固件测试不通过原因
 *
 * @remarks
 *
 * @param data - API.ReqCommonParams
 * @returns
 *
 * @beta
 */
export async function viewRefuseFirmwareTestReason(data: API.ReqCommonParams) {
  try {
    const res = await request(Api.ViewRefuseFirmwareTestReason, {
      method: 'POST',
      data,
    });
    return { info: res.data };
  } catch (e) {
    return {};
  }
}
/**
 * 获取固件停止发布申请被驳回原因
 *
 * @remarks
 *
 * @param data - API.ReqCommonParams
 * @returns
 *
 * @beta
 */
export async function viewRefuseFirmwareStopReason(data: API.ReqCommonParams) {
  try {
    const res = await request(Api.ViewRefuseFirmwareStopReason, {
      method: 'POST',
      data,
    });
    return { info: res.data };
  } catch (e) {
    return {};
  }
}
/**
 * 获取固件作废申请被驳回原因
 *
 * @remarks
 *
 * @param data - API.ReqCommonParams
 * @returns
 *
 * @beta
 */
export async function viewRefuseFirmwareNullifyReason(data: API.ReqCommonParams) {
  try {
    const res = await request(Api.ViewRefuseFirmwareNullifyReason, {
      method: 'POST',
      data,
    });
    return { info: res.data };
  } catch (e) {
    return {};
  }
}
/**
 * 查询员工列表
 *
 * @remarks
 *
 * @param data - API.EmployeePageParams
 * @returns
 *
 * @beta
 */
export async function getEmployeeList(data: API.EmployeePageParams) {
  return request(Api.EmployeeList, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 获取产品编辑操作人员列表
 *
 * @remarks
 *
 * @param data - API.SysUserPageParams
 * @returns
 *
 * @beta
 */
export async function getProductEditPermissionList(data: API.SysUserPageParams) {
  return request(Api.EditProductPermissionList, {
    method: 'POST',
    data,
  });
}
/**
 * 获取产品发布审核操作人员列表
 *
 * @remarks
 *
 * @param data - API.SysUserPageParams
 * @returns
 *
 * @beta
 */
export async function getProductApprovePermissionList(data: API.SysUserPageParams) {
  return request(Api.ApproveProductPermissionList, {
    method: 'POST',
    data,
  });
}
/**
 * 添加产品编辑操作人员
 *
 * @remarks
 *
 * @param data - productId：产品ID；users：操作人员列表
 * @returns
 *
 * @beta
 */
export function addProductEditPermission(data: { productId: string; users: API.SysUserItem[] }) {
  return request(Api.AddProductEditPermission, {
    method: 'POST',
    data,
  });
}
/**
 * 添加产品发布审核操作人员
 *
 * @remarks
 *
 * @param data - productId：产品ID；users：操作人员列表
 * @returns
 *
 * @beta
 */
export function addProductApprovePermission(data: { productId: string; users: API.SysUserItem[] }) {
  return request(Api.AddProductApprovePermission, {
    method: 'POST',
    data,
  });
}
/**
 * 删除产品编辑操作人员
 *
 * @remarks
 *
 * @param data - productId：产品ID；users：操作人员列表
 * @returns
 *
 * @beta
 */
export function deleteProductEditPermission(data: { productId: string; users: API.SysUserItem[] }) {
  return request(Api.DeleteProductEditPermission, {
    method: 'POST',
    data,
  });
}
/**
 * 删除产品发布审核操作人员
 *
 * @remarks
 *
 * @param data - productId：产品ID；users：操作人员列表
 * @returns
 *
 * @beta
 */
export function deleteProductApprovePermission(data: {
  productId: string;
  users: API.SysUserItem[];
}) {
  return request(Api.DeleteProductApprovePermission, {
    method: 'POST',
    data,
  });
}
/**
 * 获取产品测试审核操作人员列表
 *
 * @remarks
 *
 * @param data - API.SysUserPageParams
 * @returns
 *
 * @beta
 */
export async function getProductTestPermissionList(data: API.SysUserPageParams) {
  return request(Api.TestProductPermissionList, {
    method: 'POST',
    data,
  });
}
/**
 * 添加产品测试审核操作人员
 *
 * @remarks
 *
 * @param data - productId：产品ID；users：操作人员列表
 * @returns
 *
 * @beta
 */
export function addProductTestPermission(data: { productId: string; users: API.SysUserItem[] }) {
  return request(Api.AddProductTestPermission, {
    method: 'POST',
    data,
  });
}
/**
 * 删除产品测试审核操作人员
 *
 * @remarks
 *
 * @param data - productId：产品ID；users：操作人员列表
 * @returns
 *
 * @beta
 */
export function deleteProductTestPermission(data: { productId: string; users: API.SysUserItem[] }) {
  return request(Api.DeleteProductTestPermission, {
    method: 'POST',
    data,
  });
}
/**
 * 获取固件发布审核操作人员列表
 *
 * @remarks
 *
 * @param data - API.SysUserPageParams
 * @returns
 *
 * @beta
 */
export async function getOtaApprovePermissionList(data: API.SysUserPageParams) {
  return request(Api.ApproveOtaPermissionList, {
    method: 'POST',
    data,
  });
}
/**
 * 添加固件发布审核操作人员
 *
 * @remarks
 *
 * @param data - productId：产品ID；users：操作人员列表
 * @returns
 *
 * @beta
 */
export function addOtaApprovePermission(data: { productId: string; users: API.SysUserItem[] }) {
  return request(Api.AddOtaApprovePermission, {
    method: 'POST',
    data,
  });
}
/**
 * 删除固件发布审核操作人员
 *
 * @remarks
 *
 * @param data - productId：产品ID；users：操作人员列表
 * @returns
 *
 * @beta
 */
export function deleteOtaApprovePermission(data: { productId: string; users: API.SysUserItem[] }) {
  return request(Api.DeleteOtaApprovePermission, {
    method: 'POST',
    data,
  });
}
/**
 * 获取固件测试审核操作人员列表
 *
 * @remarks
 *
 * @param data - API.SysUserPageParams
 * @returns
 *
 * @beta
 */
export async function getOtaTestPermissionList(data: API.SysUserPageParams) {
  return request(Api.TestOtaPermissionList, {
    method: 'POST',
    data,
  });
}
/**
 * 添加固件测试审核操作人员
 *
 * @remarks
 *
 * @param data - productId：产品ID；users：操作人员列表
 * @returns
 *
 * @beta
 */
export function addOtaTestPermission(data: { productId: string; users: API.SysUserItem[] }) {
  return request(Api.AddOtaTestPermission, {
    method: 'POST',
    data,
  });
}
/**
 * 删除固件测试审核操作人员
 *
 * @remarks
 *
 * @param data - productId：产品ID；users：操作人员列表
 * @returns
 *
 * @beta
 */
export function deleteOtaTestPermission(data: { productId: string; users: API.SysUserItem[] }) {
  return request(Api.DeleteOtaTestPermission, {
    method: 'POST',
    data,
  });
}
/**
 * 获取RN发布审核操作人员列表
 *
 * @remarks
 *
 * @param data - API.SysUserPageParams
 * @returns
 *
 * @beta
 */
export async function getRnApprovePermissionList(data: API.SysUserPageParams) {
  return request(Api.ApproveRnPermissionList, {
    method: 'POST',
    data,
  });
}
/**
 * 添加RN发布审核操作人员
 *
 * @remarks
 *
 * @param data - productId：产品ID；users：操作人员列表
 * @returns
 *
 * @beta
 */
export function addRnApprovePermission(data: { productId: string; users: API.SysUserItem[] }) {
  return request(Api.AddRnApprovePermission, {
    method: 'POST',
    data,
  });
}
/**
 * 删除RN发布审核操作人员
 *
 * @remarks
 *
 * @param data - productId：产品ID；users：操作人员列表
 * @returns
 *
 * @beta
 */
export function deleteRnApprovePermission(data: { productId: string; users: API.SysUserItem[] }) {
  return request(Api.DeleteRnApprovePermission, {
    method: 'POST',
    data,
  });
}
/**
 * 获取RN测试审核操作人员
 *
 * @remarks
 *
 * @param data - API.SysUserPageParams
 * @returns
 *
 * @beta
 */
export async function getRnTestPermissionList(data: API.SysUserPageParams) {
  return request(Api.TestRnPermissionList, {
    method: 'POST',
    data,
  });
}
/**
 * 添加RN测试审核操作人员
 *
 * @remarks
 *
 * @param data - productId：产品ID；users：操作人员列表
 * @returns
 *
 * @beta
 */
export function addRnTestPermission(data: { productId: string; users: API.SysUserItem[] }) {
  return request(Api.AddRnTestPermission, {
    method: 'POST',
    data,
  });
}
/**
 * 删除RN测试审核操作人员
 *
 * @remarks
 *
 * @param data - productId：产品ID；users：操作人员列表
 * @returns
 *
 * @beta
 */
export function deleteRnTestPermission(data: { productId: string; users: API.SysUserItem[] }) {
  return request(Api.DeleteRnTestPermission, {
    method: 'POST',
    data,
  });
}
