import { request } from 'umi';

enum Api {
  List = '/operation-platform/charging/sms/page',
  Export = '/operation-platform/charging/sms/export',
  Details = '/operation-platform/charging/sms/detail/page',
  ExportDetails = '/operation-platform/charging/sms/detail/export',
  FlowList = '/operation-platform/charging/flow/page',
  ExportFlow = '/operation-platform/charging/flow/export',
  FlowDetails = '/operation-platform/charging/flow/detail/page',
  ExportFlowDetails = '/operation-platform/charging/flow/detail/export',
}
/**
 * 获取短信流量统计列表
 *
 * @remarks
 *
 * @param data - API.FeeSmsPageParams
 * @returns
 *
 * @beta
 */
export async function getList(data: Partial<API.FeeSmsPageParams>) {
  return request(Api.List, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 导出短信流量统计
 *
 * @remarks
 *
 * @param data - API.FeeSms
 * @returns
 *
 * @beta
 */
export function exportList(data: Partial<API.FeeSms>) {
  return request(Api.Export, {
    method: 'POST',
    type: 'string',
    data,
    responseType: 'blob',
    getResponse: true,
  });
}
/**
 * 获取短信流量统计详情
 *
 * @remarks
 *
 * @param data - API.FeeSmsPageParams
 * @returns
 *
 * @beta
 */
export async function getDetails(data: Partial<API.FeeSmsPageParams>) {
  return request(Api.Details, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 导出短信流量统计详情
 *
 * @remarks
 *
 * @param data - API.FeeSms
 * @returns
 *
 * @beta
 */
export function exportDetails(data: Partial<API.FeeSms>) {
  return request(Api.ExportDetails, {
    method: 'POST',
    type: 'string',
    data,
    responseType: 'blob',
    getResponse: true,
  });
}
/**
 * 获取流量费用统计列表
 *
 * @remarks
 *
 * @param data - API.FeeFlowPageParams
 * @returns
 *
 * @beta
 */
export async function getFlowList(data: Partial<API.FeeFlowPageParams>) {
  return request(Api.FlowList, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 导出流量费用统计
 *
 * @remarks
 *
 * @param data - API.FeeFLowItem
 * @returns
 *
 * @beta
 */
export function exportFlowList(data: Partial<API.FeeFLowItem>) {
  return request(Api.ExportFlow, {
    method: 'POST',
    type: 'string',
    data,
    responseType: 'blob',
    getResponse: true,
  });
}
/**
 * 获取流量费用统计详情
 *
 * @remarks
 *
 * @param data - API.FeeFlowPageParams
 * @returns
 *
 * @beta
 */
export async function getFlowDetails(data: Partial<API.FeeFlowPageParams>) {
  return request(Api.FlowDetails, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 导出流量费用统计
 *
 * @remarks
 *
 * @param data - API.FeeFLowItem
 * @returns
 *
 * @beta
 */
export function exportFlowDetails(data: Partial<API.FeeFLowItem>) {
  return request(Api.ExportFlowDetails, {
    method: 'POST',
    type: 'string',
    data,
    responseType: 'blob',
    getResponse: true,
  });
}
