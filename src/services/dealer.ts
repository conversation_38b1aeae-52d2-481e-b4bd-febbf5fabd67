import { request } from 'umi';

enum Api {
  List = '/operation-platform/dealer/page',
  Create = '/operation-platform/dealer/add',
  Edit = '/operation-platform/dealer/edit',
  Delete = '/operation-platform/dealer/delete',
  Detail = '/operation-platform/dealer/detail',
  ImportTemplate = '/operation-platform/dealer/template',

  AmericanList = '/operation-platform/dealer/na/page',
  AmericanCreate = '/operation-platform/dealer/na/add',
  AmericanEdit = '/operation-platform/dealer/na/edit',
  AmericanDelete = '/operation-platform/dealer/na/delete',
  AmericanDetail = '/operation-platform/dealer/na/detail',
  AmericanImportTemplate = '/operation-platform/dealer/na/template',

  EuropeList = '/operation-platform/dealer/eu/page',
  EuropeCreate = '/operation-platform/dealer/eu/add',
  EuropeEdit = '/operation-platform/dealer/eu/edit',
  EuropeDelete = '/operation-platform/dealer/eu/delete',
  EuropeDetail = '/operation-platform/dealer/eu/detail',
  EuropeImportTemplate = '/operation-platform/dealer/eu/template',
}
/**
 * 获取经销商列表
 *
 * @remarks
 *
 * @param data - API.DealerPageParams
 * @returns
 *
 * @beta
 */
export async function getList(data: Partial<API.DealerPageParams>) {
  return request(Api.List, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 创建经销商
 *
 * @remarks
 *
 * @param data - API.DealerItem
 * @returns
 *
 * @beta
 */
export function createDealer(data: Partial<API.DealerItem>) {
  return request(Api.Create, {
    method: 'POST',
    data,
  });
}
/**
 * 编辑经销商
 *
 * @remarks
 *
 * @param data - API.DealerItem
 * @returns
 *
 * @beta
 */
export function editDealer(data: Partial<API.DealerItem>) {
  return request(Api.Edit, {
    method: 'POST',
    data,
  });
}
/**
 * 删除经销商
 *
 * @remarks
 *
 * @param req - 经销商ID
 * @returns
 *
 * @beta
 */
export function deleteDealer(req: string) {
  return request(Api.Delete, {
    method: 'POST',
    data: { req },
  });
}
/**
 * 经销商导入模板下载
 *
 * @remarks
 *
 * @param
 * @returns
 *
 * @beta
 */
export const getImportTemplate = () => {
  return request(Api.ImportTemplate, {
    method: 'post',
    type: 'string',
    getResponse: true,
    responseType: 'blob',
  });
};

/**
 * 获取北美经销商列表
 *
 * @remarks
 *
 * @param data - API.DealerPageParams
 * @returns
 *
 * @beta
 */
export async function getAmericanList(data: Partial<API.DealerPageParams>) {
  return request(Api.AmericanList, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 创建北美经销商
 *
 * @remarks
 *
 * @param data - API.DealerItem
 * @returns
 *
 * @beta
 */
export function createAmericanDealer(data: Partial<API.DealerItem>) {
  return request(Api.AmericanCreate, {
    method: 'POST',
    data,
  });
}
/**
 * 编辑北美经销商
 *
 * @remarks
 *
 * @param data - API.DealerItem
 * @returns
 *
 * @beta
 */
export function editAmericanDealer(data: Partial<API.DealerItem>) {
  return request(Api.AmericanEdit, {
    method: 'POST',
    data,
  });
}
/**
 * 删除北美经销商
 *
 * @remarks
 *
 * @param req - 经销商ID
 * @returns
 *
 * @beta
 */
export function deleteAmericanDealer(req: string) {
  return request(Api.AmericanDelete, {
    method: 'POST',
    data: { req },
  });
}
/**
 * 北美经销商导入模板下载
 *
 * @remarks
 *
 * @param
 * @returns
 *
 * @beta
 */
export const getAmericanImportTemplate = () => {
  return request(Api.AmericanImportTemplate, {
    method: 'post',
    type: 'string',
    getResponse: true,
    responseType: 'blob',
  });
};

/**
 * 获取欧洲经销商列表
 *
 * @remarks
 *
 * @param data - API.DealerPageParams
 * @returns
 *
 * @beta
 */
export async function getEuropeList(data: Partial<API.DealerPageParams>) {
  return request(Api.EuropeList, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 创建欧洲经销商
 *
 * @remarks
 *
 * @param data - API.DealerItem
 * @returns
 *
 * @beta
 */
export function createEuropeDealer(data: Partial<API.DealerItem>) {
  return request(Api.EuropeCreate, {
    method: 'POST',
    data,
  });
}
/**
 * 编辑欧洲经销商
 *
 * @remarks
 *
 * @param data - API.DealerItem
 * @returns
 *
 * @beta
 */
export function editEuropeDealer(data: Partial<API.DealerItem>) {
  return request(Api.EuropeEdit, {
    method: 'POST',
    data,
  });
}
/**
 * 删除欧洲经销商
 *
 * @remarks
 *
 * @param req - 经销商ID
 * @returns
 *
 * @beta
 */
export function deleteEuropeDealer(ids: string[]) {
  return request(Api.EuropeDelete, {
    method: 'POST',
    data: { info: ids },
  });
}
/**
 * 欧洲经销商导入模板下载
 *
 * @remarks
 *
 * @param
 * @returns
 *
 * @beta
 */
export const getEuropeImportTemplate = () => {
  return request(Api.EuropeImportTemplate, {
    method: 'post',
    type: 'string',
    getResponse: true,
    responseType: 'blob',
  });
};
