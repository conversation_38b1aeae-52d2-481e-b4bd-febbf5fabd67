import { request } from 'umi';

enum Api {
  List = '/operation-platform/message/template/page',
  Create = '/operation-platform/message/template/add',
  Edit = '/operation-platform/message/template/edit',
  Delete = '/operation-platform/message/template/delete',
}
/**
 * 分页查询消息模板
 *
 * @remarks
 *
 * @param data - API.MessageTemplatePageParams
 * @returns
 *
 * @beta
 */
export async function getList(data: Partial<API.MessageTemplatePageParams>) {
  return request(Api.List, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 添加消息模板
 *
 * @remarks
 *
 * @param data - API.MessageTemplateItem
 * @returns
 *
 * @beta
 */
export function createTemplate(data: Partial<API.MessageTemplateItem>) {
  return request(Api.Create, {
    method: 'POST',
    data,
  });
}
/**
 * 编辑消息模板
 *
 * @remarks
 *
 * @param data - API.MessageTemplateItem
 * @returns
 *
 * @beta
 */
export function editTemplate(data: Partial<API.MessageTemplateItem>) {
  return request(Api.Edit, {
    method: 'POST',
    data,
  });
}
/**
 * 删除消息模板
 *
 * @remarks
 *
 * @param id - 消息模板ID
 * @returns
 *
 * @beta
 */
export function deleteTemplate(id: string) {
  return request(Api.Delete, {
    method: 'POST',
    data: { id },
  });
}
