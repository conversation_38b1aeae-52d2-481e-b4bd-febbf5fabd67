import { request } from 'umi';
import { omit } from 'lodash-es';

enum Api {
  // 通用FAQ分页查询
  GetFaqList = '/operation-platform/common/faq/manage/page',
  // 导出通用操作
  ExportFaqs = '/operation-platform/common/faq/export',
  // 导入模板
  ImportTemplate = '/operation-platform/common/faq/template',
  // 创建FAQ
  CreateFaq = '/operation-platform/common/faq/add',
  // 编辑FAQ
  EditFaq = '/operation-platform/common/faq/edit',
  // 删除FAQ
  DeleteFaq = '/operation-platform/common/faq/delete',
  // FAQ详情
  FaqDetail = '/operation-platform/common/faq/detail',
  // FAQ已关联产品分页查询
  FaqProducts = '/operation-platform/common/faq/product/page',
  // FAQ已关联全部产品
  AllFaqProducts = '/operation-platform/common/faq/productId/list',
  // 添加关联产品
  AddProducts = '/operation-platform/common/faq/product/add',
  // 删除关联产品
  DeleteProduct = '/operation-platform/common/faq/product/delete',
}
/**
 * 获取通用FAQ列表
 *
 * @remarks
 *
 * @param data - API.FaqItemPageParams
 * @returns
 *
 * @beta
 */
export async function getFaqList(data: Partial<API.FaqItemPageParams>) {
  return request(Api.GetFaqList, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 导出通用FAQ列表
 *
 * @remarks
 *
 * @param data - API.FaqItemPageParams
 * @returns
 *
 * @beta
 */
export const exportFaqs = (data: API.FaqItemPageParams) => {
  return request(Api.ExportFaqs, {
    method: 'POST',
    type: 'string',
    data: { ...omit(data, ['faq']), ...data.faq },
    responseType: 'blob',
    getResponse: true,
  });
};
/**
 * 通用FAQ导入模板下载
 *
 * @remarks
 *
 * @param
 * @returns
 *
 * @beta
 */
export const getImportTemplate = () => {
  return request(Api.ImportTemplate, {
    method: 'post',
    type: 'string',
    responseType: 'blob',
    getResponse: true,
  });
};
/**
 * 获取通用FAQ详情
 *
 * @remarks
 *
 * @param id - 通用faqID
 * @returns
 *
 * @beta
 */
export function getFaqDetail(id: string) {
  return request(Api.FaqDetail, {
    method: 'POST',
    data: { req: id },
  }).then((res) => res?.data);
}
/**
 * 创建通用FAQ
 *
 * @remarks
 *
 * @param data - API.FaqItem
 * @returns
 *
 * @beta
 */
export function createFaq(data: Partial<API.FaqItem>) {
  return request(Api.CreateFaq, {
    method: 'POST',
    data,
  });
}
/**
 * 编辑通用FAQ
 *
 * @remarks
 *
 * @param data - API.FaqItem
 * @returns
 *
 * @beta
 */
export function editFaq(data: Partial<API.FaqItem>) {
  return request(Api.EditFaq, {
    method: 'POST',
    data,
  });
}
/**
 * 删除通用FAQ
 *
 * @remarks
 *
 * @param id - 通用faqID
 * @returns
 *
 * @beta
 */
export function deleteFaq(id: string) {
  return request(Api.DeleteFaq, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * 分页获取通用FAQ关联产品列表
 *
 * @remarks
 *
 * @param data - API.ProductPageParams
 * @returns
 *
 * @beta
 */
export const getFaqProducts = async (data: Partial<API.ProductPageParams>) => {
  return request(Api.FaqProducts, {
    method: 'post',
    type: 'page',
    data,
  });
};
/**
 * 获取通用FAQ关联的全部产品列表
 *
 * @remarks
 *
 * @param data - API.ProductPageParams
 * @returns
 *
 * @beta
 */
export const getAllFaqProducts = async (data: Partial<API.ProductPageParams>) => {
  return request(Api.AllFaqProducts, {
    method: 'post',
    data,
  });
};
/**
 * 添加通用FAQ关联产品
 *
 * @remarks
 *
 * @param data - commonId: 通用FAQ; relatedProduct: 关联产品信息
 * @returns
 *
 * @beta
 */
export async function addFaqProducts(data: {
  commonId: string;
  relatedProduct: Record<string, any>[];
}) {
  return request(Api.AddProducts, {
    method: 'POST',
    data,
  });
}
/**
 * 删除通用FAQ关联产品
 *
 * @remarks
 *
 * @param data - commonId: 通用FAQ; relatedProduct: 关联产品信息
 * @returns
 *
 * @beta
 */
export async function deleteFaqProduct(data: {
  commonId: string;
  relatedProduct: Record<string, any>[];
}) {
  return request(Api.DeleteProduct, {
    method: 'POST',
    data,
  });
}
