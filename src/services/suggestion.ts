import { request } from 'umi';
import { downloadByData } from '@/utils/download';
import dayjs from 'dayjs';
import { get, map } from 'lodash-es';
import { ErrorShowType } from '@@/plugin-request/request';
enum Api {
  ListSuggestion = '/operation-platform/suggestion/page',
  CreateSuggestion = '/operation-platform/suggestion/add',
  UpdateSuggestion = '/operation-platform/suggestion/edit',
  DeleteSuggestion = '/operation-platform/suggestion/delete',
  ViewSuggestion = '/operation-platform/suggestion/detail',
  ExportSuggestion = '/operation-platform/suggestion/export',
  ImportSuggestion = '/operation-platform/suggestion/import',
  DownloadSuggestionTemplate = '/operation-platform/suggestion/template',
}
/**
 * 分页查询处理建议
 *
 * @remarks
 *
 * @param data - API.SuggestionPageParams
 * @returns
 *
 * @beta
 */
export async function listSuggestion(data: Partial<API.SuggestionPageParams>) {
  return request(Api.ListSuggestion, {
    method: 'POST',
    type: 'page',
    data,
  }).then((res) => {
    const list = get(res, 'data') || [];
    return {
      ...res,
      data: map(list, (item) => {
        return {
          ...item,
          title: {
            message: item.title,
            langId: item.titleLangId,
          },
          content: {
            message: item.content,
            langId: item.contentLangId,
          },
        };
      }),
    };
  });
}
/**
 * 添加处理建议
 *
 * @remarks
 *
 * @param data - API.SuggestionReq
 * @returns
 *
 * @beta
 */
export async function createSuggestion(data: Omit<API.SuggestionReq, 'suggestionId'>) {
  return request(Api.CreateSuggestion, {
    method: 'POST',
    data,
  });
}
/**
 * 编辑处理建议
 *
 * @remarks
 *
 * @param data - API.SuggestionReq
 * @returns
 *
 * @beta
 */
export async function updateSuggestion(data: API.SuggestionReq) {
  return request(Api.UpdateSuggestion, {
    method: 'POST',
    data,
  });
}
/**
 * 删除处理建议
 *
 * @remarks
 *
 * @param suggestionId - 处理建议ID
 * @returns
 *
 * @beta
 */
export async function deleteSuggestion(suggestionId: string) {
  return request(Api.DeleteSuggestion, {
    method: 'POST',
    data: { req: suggestionId },
    showType: ErrorShowType.SILENT,
    force: true,
  });
}
/**
 * 处理建议详情
 *
 * @remarks
 *
 * @param suggestionId - 处理建议ID
 * @returns
 *
 * @beta
 */
export async function viewSuggestion(suggestionId: string) {
  return request(Api.ViewSuggestion, {
    method: 'POST',
    data: { req: suggestionId },
  });
}
/**
 * 导入处理建议
 *
 * @remarks
 *
 * @param data - FormData
 * @returns
 *
 * @beta
 */
export async function importSuggestion(data: FormData) {
  return request(Api.ImportSuggestion, {
    method: 'POST',
    data,
  });
}
/**
 * 导出处理建议
 *
 * @remarks
 *
 * @param data - SuggestionPageParams
 * @returns
 *
 * @beta
 */
export async function exportSuggestion(data: Partial<API.SuggestionPageParams>) {
  return request(Api.ExportSuggestion, {
    method: 'POST',
    type: 'string',
    data,
  }).then((res) => {
    downloadByData({
      data: res,
      filename: `suggestion-${dayjs().format('YYYYMMDD')}.csv`,
      type: 'text/csv;charset=UTF-8',
    });
  });
}
/**
 * 处理建议导入模板下载
 *
 * @remarks
 *
 * @param
 * @returns
 *
 * @beta
 */
export async function downloadSuggestionTemplate() {
  return request(Api.DownloadSuggestionTemplate, {
    method: 'POST',
    type: 'string',
    responseType: 'blob',
    getResponse: true,
  });
}
