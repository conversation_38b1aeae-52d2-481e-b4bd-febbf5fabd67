import { request } from 'umi';

enum Api {
  GetCodeList = '/operation-platform/device/code/list',
  CreateMultiCode = '/operation-platform/device/code/add',
  EditMultiCodeStatus = '/operation-platform/device/code/edit/status',
  UploadMultiCode = '/operation-platform/device/code/upload',
  ExportMultiCodes = '/operation-platform/device/code/export',
  GetTemplate = '/operation-platform/device/code/import/pattern/download',
}
/**
 * 获取产品多码表
 *
 * @remarks
 *
 * @param data - API.MultiCodePageParams
 * @returns
 *
 * @beta
 */
export function getMultiCodeList(data: Partial<API.MultiCodePageParams>) {
  return request(Api.GetCodeList, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 创建产品多码
 *
 * @remarks
 *
 * @param data - API.MultiCodeItem
 * @returns
 *
 * @beta
 */
export function createMultiCode(data: API.MultiCodeItem) {
  return request(Api.CreateMultiCode, {
    method: 'POST',
    data,
  });
}
/**
 * 导入产品多码
 *
 * @remarks
 *
 * @param data - FormData
 * @returns
 *
 * @beta
 */
export function uploadMultiCode(data: FormData) {
  return request(Api.UploadMultiCode, {
    method: 'POST',
    data,
  });
}
/**
 * 编辑多码状态
 *
 * @remarks
 *
 * @param id - 设备ID
 * @param status - 设备状态(0:废弃；1:启用)
 * @returns
 *
 * @beta
 */
export function editStatus(id: string, status: number) {
  return request(Api.EditMultiCodeStatus, {
    method: 'POST',
    data: { deviceId: id, status },
  });
}
/**
 * 导出产品多码
 *
 * @remarks
 *
 * @param data - API.MultiCodePageParams
 * @returns
 *
 * @beta
 */
export const exportMultiCodes = (data: Partial<API.MultiCodePageParams>) => {
  return request(Api.ExportMultiCodes, {
    method: 'post',
    type: 'string',
    data,
    responseType: 'blob',
  });
};
/**
 * 产品多码导入模板
 *
 * @remarks
 *
 * @param
 * @returns
 *
 * @beta
 */
export const getTemplate = () => {
  return request(Api.GetTemplate, {
    method: 'get',
    type: 'string',
    responseType: 'blob',
    getResponse: true,
  });
};
