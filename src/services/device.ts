import { request } from 'umi';
enum Api {
  List = '/operation-platform/device/agreement/list',
  Create = '/operation-platform/device/agreement/add',
  Recreate = '/operation-platform/device/agreement/add/version',
  Delete = '/operation-platform/device/agreement/delete',
  Update = '/operation-platform/device/agreement/edit',
  View = '/operation-platform/device/agreement/detail',

  Devices = '/operation-platform/device/manage/list',
  StopDevice = '/operation-platform/device/manage/edit/status',
  ExportDevice = '/operation-platform/device/manage/export',
  GetDeviceById = '/operation-platform/device/manage/detail',
  GetDeviceComponent = '/operation-platform/device/manage/component/list',
  DeviceUpgradeList = '/operation-platform/device/manage/ota/list',
  DeviceUpgradeDetail = '/operation-platform/device/manage/ota/detail',
  GetFirmwareUpgrade = '/operation-platform/device/manage/component/ota/list',
}
/**
 * 获取设备协议列表
 *
 * @remarks
 *
 * @param data - API.DeviceProtocolPageParams
 * @returns
 *
 * @beta
 */
export async function listProtocol(data: API.DeviceProtocolPageParams) {
  return request(Api.List, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 添加设备协议
 *
 * @remarks
 *
 * @param data - API.DeviceProtocolItem
 * @returns
 *
 * @beta
 */
export async function createProtocol(data: API.DeviceProtocolItem) {
  return request(Api.Create, {
    method: 'POST',
    data,
  });
}
/**
 * 添加新版本设备协议
 *
 * @remarks
 *
 * @param data - API.DeviceProtocolItem
 * @returns
 *
 * @beta
 */
export async function recreateProtocol(data: API.DeviceProtocolItem) {
  return request(Api.Recreate, {
    method: 'POST',
    data,
  });
}
/**
 * 删除设备协议
 *
 * @remarks
 *
 * @param data - id：设备ID; productId: 产品ID
 * @returns
 *
 * @beta
 */
export async function deleteProtocol(data: { id: string; productId: string }) {
  return request(Api.Delete, {
    method: 'POST',
    data,
  });
}
/**
 * 编辑设备协议
 *
 * @remarks
 *
 * @param data - API.DeviceProtocolItem
 * @returns
 *
 * @beta
 */
export async function updateProtocol(data: API.DeviceProtocolItem) {
  return request(Api.Update, {
    method: 'POST',
    data,
  });
}
/**
 * 获取设备协议详情
 *
 * @remarks
 *
 * @param data - id：设备ID; productId: 产品ID
 * @returns
 *
 * @beta
 */
export async function viewProtocol(data: { id: string; productId: string }) {
  return request(Api.View, {
    method: 'POST',
    data,
  });
}

/**
 * 分页查询设备列表
 *
 * @remarks
 *
 * @param data - API.DevicePageParams
 * @returns
 *
 * @beta
 */
export function getDeviceList(data: Partial<API.DevicePageParams>) {
  return request(Api.Devices, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 停用/启用设备
 *
 * @remarks
 *
 * @param id - 设备ID
 * @param status - 状态值
 * @returns
 *
 * @beta
 */
export async function stopDevice(id: string, status: string) {
  return request(Api.StopDevice, {
    method: 'POST',
    data: { deviceId: id, status },
  });
}
/**
 * 获取设备详情
 *
 * @remarks
 *
 * @param id - 设备ID
 * @returns
 *
 * @beta
 */
export const getDeviceDetail = (id: string) => {
  return request(Api.GetDeviceById, {
    method: 'POST',
    data: { req: id },
  });
};
/**
 * 分页查询设备总成零件
 *
 * @remarks
 *
 * @param data - API.DevicePartPageParams
 * @returns
 *
 * @beta
 */
export const getDeviceComponents = (data: Partial<API.DevicePartPageParams>) => {
  return request(Api.GetDeviceComponent, {
    method: 'POST',
    type: 'page',
    data,
  });
};
/**
 * 分页查询设备升级记录
 *
 * @remarks
 *
 * @param data - API.DeviceUpgradePageParams
 * @returns
 *
 * @beta
 */
export const getDeviceUpgrades = (data: Partial<API.DeviceUpgradePageParams>) => {
  return request(Api.DeviceUpgradeList, {
    method: 'POST',
    type: 'page',
    data,
  });
};
/**
 * 获取设备升级记录详情
 *
 * @remarks
 *
 * @param data - API.DeviceUpgradePageParams
 * @returns
 *
 * @beta
 */
export const getDeviceUpgradeDetail = (data: Partial<API.DeviceUpgradePageParams>) => {
  return request(Api.DeviceUpgradeDetail, {
    method: 'POST',
    type: 'page',
    data,
  });
};
/**
 * 获取固件升级记录
 *
 * @remarks
 *
 * @param data - API.UpgradePageParams
 * @returns
 *
 * @beta
 */
export const getUpgradeHistory = (data: Partial<API.UpgradePageParams>) => {
  return request(Api.GetFirmwareUpgrade, {
    method: 'POST',
    type: 'page',
    data,
  });
};

/**
 * 导出设备
 *
 * @remarks
 *
 * @param data - API.DevicePageParams
 * @returns
 *
 * @beta
 */
export const exportDevices = (data: Partial<API.DevicePageParams>) => {
  return request(Api.ExportDevice, {
    method: 'post',
    type: 'string',
    data,
    responseType: 'blob',
  });
};
