import { request, ErrorShowType } from 'umi';
import { downloadByUrl } from '@/utils/download';
import { get, map, omit, reduce } from 'lodash-es';

enum Api {
  List = '/operation-platform/parts/page', //
  Create = '/operation-platform/parts/add',
  Delete = '/operation-platform/parts/delete',
  Update = '/operation-platform/parts/edit',
  Import = '/operation-platform/parts/import',
  Export = '/operation-platform/parts/export',
  View = '/operation-platform/parts/detail',

  ShortDesc = '/operation-platform/parts/edit/short',
  LongDesc = '/operation-platform/parts/edit/long',
  TechSpec = '/operation-platform/parts/edit/tech',

  ListLinks = '/operation-platform/parts/link/list',
  CreateLink = '/operation-platform/parts/link/add',
  DeleteLink = '/operation-platform/parts/link/delete',
  UpdateLink = '/operation-platform/parts/link/edit',

  // ListGuides = '/operation-platform/parts/detail/list/user/manual',
  // DeleteGuide = '/operation-platform/parts/detail/delete/user/manual',
  // CreateFileGuide = '/operation-platform/parts/detail/add/file/user/manual',
  // UpdateFileGuide = '/operation-platform/parts/detail/edit/file/user/manual',

  CreateLinkGuide = '/operation-platform/parts/detail/add/link/user/manual',
  UpdateLinkGuide = '/operation-platform/parts/detail/edit/link/user/manual',
  ListRelationProduct = '/operation-platform/parts/product/page',
  DownloadTemplate = '/operation-platform/parts/template',

  ViewFaq = '/operation-platform/postSale/detail',
  UpdateShortDescription = '/operation-platform/postSale/edit/short',
  UpdateLongDescription = '/operation-platform/postSale/edit/long',
  UpdateTechnicalSpecification = '/operation-platform/postSale/edit/tech',
  ListFaq = '/operation-platform/parts/faq/list',
  DeleteFaq = '/operation-platform/parts/faq/delete',
  CreateFaq = '/operation-platform/parts/faq/add',
  UpdateFaq = '/operation-platform/parts/faq/edit',
  SortFaq = '/operation-platform/parts/faq/order',

  ListManual = '/operation-platform/parts/manual/list',
  DeleteManual = '/operation-platform/parts/manual/delete',
  CreateManual = '/operation-platform/parts/manual/add',
  UpdateManual = '/operation-platform/parts/manual/edit',
  DownloadManual = '/operation-platform/parts/manual/download',

  ListGuide = '/operation-platform/parts/operationGuidance/list',
  DeleteGuide = '/operation-platform/parts/operationGuidance/delete',
  CreateGuide = '/operation-platform/parts/operationGuidance/add',
  UpdateGuide = '/operation-platform/parts/operationGuidance/edit',
  DownloadGuide = '/operation-platform/parts/operationGuidance/download',
  SortGuide = '/operation-platform/parts/operationGuidance/order',
}
/**
 * 分页查配件
 *
 * @remarks
 *
 * @param data - API.PartPageParams
 * @returns
 *
 * @beta
 */
export async function listPart(data: API.PartPageParams) {
  return request<{ data: API.PartItem[] }>(Api.List, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 配件关联产品
 *
 * @remarks
 *
 * @param data - API.PartPageParams
 * @returns
 *
 * @beta
 */
export async function listRelationProduct(data: API.PartPageParams) {
  return request<{ data: API.PartItem[] }>(Api.ListRelationProduct, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 添加配件
 *
 * @remarks
 *
 * @param data - API.PartItem
 * @returns
 *
 * @beta
 */
export async function createPart(data: API.PartItem) {
  return request(Api.Create, {
    method: 'POST',
    data,
  });
}
/**
 * 删除配件
 *
 * @remarks
 *
 * @param id - 配件ID
 * @returns
 *
 * @beta
 */
export async function deletePart(id: string) {
  return request(Api.Delete, {
    method: 'POST',
    data: { req: id },
    showType: ErrorShowType.SILENT,
    force: true,
  });
}
/**
 * 编辑配件
 *
 * @remarks
 *
 * @param data - API.PartItem
 * @returns
 *
 * @beta
 */
export async function updatePart(data: API.PartItem) {
  return request(Api.Update, {
    method: 'POST',
    data,
  });
}
/**
 * 导入配件
 *
 * @remarks
 *
 * @param data - FormData
 * @returns
 *
 * @beta
 */
export async function importPart(data: FormData) {
  return request(Api.Import, {
    method: 'POST',
    data,
    showType: ErrorShowType.SILENT,
  });
}
/**
 * 导出配件
 *
 * @remarks
 *
 * @param data - API.PartPageParams
 * @returns
 *
 * @beta
 */
export async function exportPart(data: Partial<API.PartPageParams>) {
  return request(Api.Export, {
    method: 'post',
    type: 'string',
    data,
    responseType: 'blob',
    getResponse: true,
  });
}
/**
 * 获取配件详情
 *
 * @remarks
 *
 * @param id - 配件ID
 * @returns
 *
 * @beta
 */
export async function getPart(id: string) {
  return request(Api.View, {
    method: 'POST',
    type: 'all',
    data: { req: id },
  }).then((res) => {
    const tmp = get(res, ['data'], {});
    return {
      ...res,
      data: {
        ...tmp,
        shortDescription: {
          message: tmp.shortDescription,
          langId: tmp.shortDescriptionLangId,
        },
        longDescription: {
          message: tmp.longDescription,
          langId: tmp.longDescriptionLangId,
        },
        technicalSpecification: {
          message: tmp.technicalSpecification,
          langId: tmp.technicalSpecificationLangId,
        },
      },
    };
  });
}
/**
 * 编辑配件
 *
 * @remarks
 *
 * @param type - 属性类型（ShortDesc：短描述；LongDesc：长描述；TechSpec：技术规格）
 * @param data - item：编辑的内容；partsId：配件ID
 * @returns
 *
 * @beta
 */
export async function updatePartDetail(type: string, data: { item: string; partsId: string }) {
  return request(Api[type], {
    method: 'POST',
    data,
  });
}
/**
 * 获取配件购买链接
 *
 * @remarks
 *
 * @param id - 配件ID
 * @returns
 *
 * @beta
 */
export async function listLinks(id: string) {
  return request(Api.ListLinks, {
    method: 'POST',
    type: 'all',
    data: { req: id },
  });
}
/**
 * 编辑配件购买链接
 *
 * @remarks
 *
 * @param data - API.PartsPurchaseLink
 * @returns
 *
 * @beta
 */
export async function updateLink(data: API.PartsPurchaseLink) {
  return request(Api.UpdateLink, {
    method: 'POST',
    data,
  });
}
/**
 * 添加配件购买链接
 *
 * @remarks
 *
 * @param data - API.PartsPurchaseLink
 * @returns
 *
 * @beta
 */
export async function createLink(data: API.PartsPurchaseLink) {
  return request(Api.CreateLink, {
    method: 'POST',
    data,
  });
}
/**
 * 删除配件购买链接
 *
 * @remarks
 *
 * @param id -购买链接ID
 * @returns
 *
 * @beta
 */
export async function deleteLink(id: string) {
  return request(Api.DeleteLink, {
    method: 'POST',
    data: { req: id },
  });
}

// export async function listGuides(id: string) {
//   return request(Api.ListGuides, {
//     method: 'POST',
//     type: 'all',
//     data: { req: id },
//   });
// }

// export async function updateGuide(data: API.PartsUserManual) {
//   return request(Api.UpdateFileGuide, {
//     method: 'POST',
//     data,
//   });
// }

// export async function createGuide(data: API.PartsUserManual) {
//   return request(Api.CreateFileGuide, {
//     method: 'POST',
//     data,
//   });
// }

// export async function deleteGuide(id: string) {
//   return request(Api.DeleteGuide, {
//     method: 'POST',
//     data: { req: id },
//   });
// }
// export async function updateLinkGuide(data: API.PartsUserManual) {
//   return request(Api.UpdateLinkGuide, {
//     method: 'POST',
//     data,
//   });
// }

// export async function createLinkGuide(data: API.PartsUserManual) {
//   return request(Api.CreateLinkGuide, {
//     method: 'POST',
//     data,
//   });
// }
/**
 * 配件导入模板下载
 *
 * @remarks
 *
 * @param
 * @returns
 *
 * @beta
 */
export async function downloadTemplate() {
  return request(Api.DownloadTemplate, {
    method: 'POST',
    type: 'string',
    responseType: 'blob',
    getResponse: true,
  });
}
/**
 * 排序配件操作指导
 *
 * @remarks
 *
 * @param data - partsOperationGuidanceIds：待排序的操作指导； productId：产品ID
 * @returns
 *
 * @beta
 */
export function sortGuide(data: { partsOperationGuidanceIds: string[]; productId: string }) {
  return request(Api.SortGuide, {
    method: 'POST',
    data: get(data, ['partsOperationGuidanceIds'], []),
  });
}
/**
 * 下载配件操作指导
 *
 * @remarks
 *
 * @param data - req：操作指导ID； name：操作指导文件名
 * @returns
 *
 * @beta
 */
export function downloadGuide(data: { req: string }, name = '') {
  return request(Api.DownloadGuide, {
    method: 'POST',
    data,
  }).then((res) => {
    const url = get(res, ['data']);
    if (url) {
      downloadByUrl(url, name);
    }
  });
}
/**
 * 配件操作指导数据格式化
 *
 * @remarks
 *
 * @param data - API.ManualItem
 * @returns
 *
 * @beta
 */
const transformGuide = (data: API.ManualItem) => {
  return {
    operateItemId: data.partsOperationGuidanceId,
    partsId: data.partsId,
    operationGuidance: {
      description: data.description?.message,
      descriptionLangId: data.description?.langId,
      format: data.ext,
      name: data.fileName?.message,
      nameLangId: data.fileName?.langId,
      size: data.fileSize,
      typeCode: data.type,
      uploadFileName: data.s3key,
      url: data.type === '1' ? undefined : data.url,
    },
  };
};
/**
 * 编辑配件操作指导
 *
 * @remarks
 *
 * @param data - API.ManualItem
 * @returns
 *
 * @beta
 */
export function updateGuide(data: API.ManualItem) {
  return request(Api.UpdateGuide, {
    method: 'POST',
    data: transformGuide(data),
  });
}
/**
 * 添加配件操作指导
 *
 * @remarks
 *
 * @param data - API.ManualItem
 * @returns
 *
 * @beta
 */
export function createGuide(data: API.ManualItem) {
  return request(Api.CreateGuide, {
    method: 'POST',
    data: transformGuide(data),
  });
}
/**
 * 删除配件操作指导
 *
 * @remarks
 *
 * @param data - req：操作指导ID
 * @returns
 *
 * @beta
 */
export function deleteGuide(data: { req: string }) {
  return request(Api.DeleteGuide, {
    method: 'POST',
    data,
  });
}
/**
 * 获取配件操作指导列表
 *
 * @remarks
 *
 * @param data - req：配件ID
 * @returns
 *
 * @beta
 */
export function listGuide(data: { req: string }) {
  return request(Api.ListGuide, {
    method: 'POST',
    type: 'all',
    data,
  }).then((res) => {
    return {
      ...res,
      data: map(res.data, (item = {}) => {
        const { operationGuidance = {} } = item;
        return {
          ...item,
          ...operationGuidance,
          description: {
            message: operationGuidance.description,
            langId: operationGuidance.descriptionLangId,
          },
          fileName: {
            message: operationGuidance.name,
            langId: operationGuidance.nameLangId,
          },
          ext: operationGuidance.format,
          fileSize: operationGuidance.size,
          ...(operationGuidance.typeCode === '1'
            ? { file: operationGuidance.url }
            : { url: operationGuidance.url }),
          type: operationGuidance.typeCode,
          s3key: operationGuidance.uploadFileName,
        };
      }),
    };
  });
}
/**
 * 下载配件用户手册
 *
 * @remarks
 *
 * @param data - req：用户手册ID； name：用户手册文件名
 * @returns
 *
 * @beta
 */
export function downloadManual(data: { req: string }, name = '') {
  return request(Api.DownloadManual, {
    method: 'POST',
    data,
  }).then((res) => {
    const url = get(res, ['data']);
    if (url) {
      downloadByUrl(url, name);
    }
  });
}
/**
 * 配件用户手册数据格式化
 *
 * @remarks
 *
 * @param data - API.ManualItem
 * @returns
 *
 * @beta
 */
const transform = (data: API.ManualItem) => {
  return {
    manual: {
      description: data.description?.message,
      descriptionLangId: data.description?.langId,
      format: data.ext,
      name: data.fileName?.message,
      nameLangId: data.fileName?.langId,
      manualId: data.manualId,
      size: data.fileSize,
      typeCode: data.type,
      uploadFileName: data.s3key,
      url: data.type === '1' ? undefined : data.url,
    },
    operateItemId: data.partsManualId,
    partsId: data.partsId,
  };
};
/**
 * 编辑配件用户手册
 *
 * @remarks
 *
 * @param data - API.ManualItem
 * @returns
 *
 * @beta
 */
export function updateManual(data: API.ManualItem) {
  return request(Api.UpdateManual, {
    method: 'POST',
    data: transform(data),
  });
}
/**
 * 添加配件用户手册
 *
 * @remarks
 *
 * @param data - API.ManualItem
 * @returns
 *
 * @beta
 */
export function createManual(data: API.ManualItem) {
  return request(Api.CreateManual, {
    method: 'POST',
    data: transform(data),
  });
}
/**
 * 删除配件用户手册
 *
 * @remarks
 *
 * @param data - req：用户手册ID
 * @returns
 *
 * @beta
 */
export function deleteManual(data: { req: string }) {
  return request(Api.DeleteManual, {
    method: 'POST',
    data,
  });
}
/**
 * 获取配件用户手册列表
 *
 * @remarks
 *
 * @param data - req：配件ID
 * @returns
 *
 * @beta
 */
export function listManual(data: { req: string }) {
  return request(Api.ListManual, {
    method: 'POST',
    type: 'all',
    data,
  }).then((res) => {
    return {
      ...res,
      data: map(res.data, (item = {}) => {
        const { manual = {} } = item;
        return {
          ...item,
          ...manual,
          description: {
            message: manual.description,
            langId: manual.descriptionLangId,
          },
          fileName: {
            message: manual.name,
            langId: manual.nameLangId,
          },
          ext: manual.format,
          fileSize: manual.size,
          file: manual.url,
          type: manual.typeCode,
          s3key: manual.uploadFileName,
        };
      }),
    };
  });
}
/**
 * 排序配件FAQ
 *
 * @remarks
 *
 * @param data - partsFaqIds：FAQ；productId：产品ID
 * @returns
 *
 * @beta
 */
export function sortFaq(data: { partsFaqIds: string[]; productId: string }) {
  return request(Api.SortFaq, {
    method: 'POST',
    data: get(data, 'partsFaqIds', []),
  });
}
/**
 * 编辑配件FAQ
 *
 * @remarks
 *
 * @param data - API.SaleFaqDto
 * @returns
 *
 * @beta
 */
export function updateFaq(data: API.SaleFaqDto) {
  return request(Api.UpdateFaq, {
    method: 'POST',
    data: {
      ...omit(data, ['id']),
      partsId: data.id,
    },
  });
}
/**
 * 添加配件FAQ
 *
 * @remarks
 *
 * @param data - API.SaleFaqDto
 * @returns
 *
 * @beta
 */
export function createFaq(data: API.SaleFaqDto) {
  return request(Api.CreateFaq, {
    method: 'POST',
    data: {
      ...omit(data, ['id']),
      partsId: data.id,
    },
  });
}
/**
 * 删除配件FAQ
 *
 * @remarks
 *
 * @param data - operateItemId：配件faqID；productId：产品ID
 * @returns
 *
 * @beta
 */
export function deleteFaq(data: { operateItemId: string; productId: string }) {
  return request(Api.DeleteFaq, {
    method: 'POST',
    data: { req: get(data, 'operateItemId') },
  });
}
/**
 * 获取配件FAQ
 *
 * @remarks
 *
 * @param data - req：配件ID
 * @returns
 *
 * @beta
 */
export function listFaq(data: { req?: string }) {
  return request(Api.ListFaq, {
    method: 'POST',
    type: 'all',
    data,
  }).then((res = {}) => {
    return {
      ...res,
      data: map(res.data, (item = {}, index) => {
        return {
          ...item,
          ...item.faq,
          ...reduce(
            ['title', 'answer'],
            (result, cur) => {
              result[cur] = {
                message: get(item, ['faq', cur]),
                langId: get(item, ['faq', `${cur}LangId`]),
              };
              return result;
            },
            {},
          ),
        };
      }),
    };
  });
}
