import { request } from 'umi';

enum Api {
  ListIntroduction = '/operation-platform/introduction/list',
  CreateIntroduction = '/operation-platform/introduction/add',
  UpdateIntroduction = '/operation-platform/introduction/edit',
  DeleteIntroduction = '/operation-platform/introduction/delete',
}
/**
 * 获取产品引导页
 *
 * @remarks
 *
 * @param id - 产品ID
 * @returns
 *
 * @beta
 */
export function listIntroduction(id: string) {
  return request(Api.ListIntroduction, {
    method: 'POST',
    data: { productId: id },
  });
}
/**
 * 创建引导页
 *
 * @remarks
 *
 * @param data - API.IntroductionItem
 * @returns
 *
 * @beta
 */
export function createIntroduction(data: API.IntroductionItem) {
  return request(Api.CreateIntroduction, {
    method: 'POST',
    data,
  }).then((res) => res.entry);
}
/**
 * 编辑引导页
 *
 * @remarks
 *
 * @param data - API.IntroductionItem
 * @returns
 *
 * @beta
 */
export function updateIntroduction(data: API.IntroductionItem) {
  return request(Api.UpdateIntroduction, {
    method: 'POST',
    data,
  }).then((res) => res.entry);
}
/**
 * 删除引导页
 *
 * @remarks
 *
 * @param data - introductionId：引导页ID；productId：产品ID
 * @returns
 *
 * @beta
 */
export function deleteIntroduction(data: { introductionId: string; productId: string }) {
  return request(Api.DeleteIntroduction, {
    method: 'POST',
    data,
  });
}
