import { request } from 'umi';
import { get, map } from 'lodash-es';
import { ErrorShowType } from '@@/plugin-request/request';

enum Api {
  // APP用户账号
  GetUserList = '/operation-platform/app/user/page',
  // 导出APP用户账号
  ExportUserList = '/operation-platform/app/user/export',
  // APP用户&设备关系
  GetUserDeviceList = '/operation-platform/app/userDevice/page',
  // 导出APP用户&设备关系
  ExportUserDevices = '/operation-platform/app/userDevice/export',
  // APP协议
  ProtocolList = '/operation-platform/app/agreement/manage/page',
  // APP协议详情
  ProtocolDetail = '/operation-platform/app/agreement/detail',
  // 创建APP协议
  CreateProtocol = '/operation-platform/app/agreement/save',
  // 复制APP协议
  CopyProtocol = '/operation-platform/app/agreement/copy',
  // 编辑APP协议
  EditProtocol = '/operation-platform/app/agreement/update',
  // 删除APP协议
  DeleteProtocol = '/operation-platform/app/agreement/delete',
  // 添加新版本APP协议
  NewProtocol = '/operation-platform/app/agreement/addNewVersion',
  // 申请发布APP协议
  ApplyProtocolRelease = '/operation-platform/app/agreement/applyRelease',
  // 申请停止发布APP协议
  ApplyStopRelease = '/operation-platform/app/agreement/applyStopRelease',
  // 撤回发布APP协议申请
  CancelProtocolApply = '/operation-platform/app/agreement/cancelApplyRelease',
  // 撤回停止发布APP协议申请
  CancelApplyStopRelease = '/operation-platform/app/agreement/cancelApplyStopRelease',
  // APP协议发布被驳回原因
  ViewRefuseReleaseReason = '/operation-platform/app/agreement/viewRefuseReleaseReason',
  // APP协议停止发布被驳回原因
  ViewRefuseStopReleaseReason = '/operation-platform/app/agreement/viewRefuseStopReleaseReason',
  // APP协议测试被驳回原因
  ViewRefuseTestReason = '/operation-platform/app/agreement/viewRefuseTestReason',
  // APP协议发布申请列表
  GetAppProtocolList = '/operation-platform/app/agreement/release/page',
  // 确认发布APP协议
  EnsureRelease = '/operation-platform/app/agreement/ensureRelease',
  // 确认停止发布APP协议
  EnsureStopRelease = '/operation-platform/app/agreement/ensureStopRelease',
  // APP协议测试通过
  EnsureTest = '/operation-platform/app/agreement/ensureTest',
  // 驳回发布APP协议
  RefuseRelease = '/operation-platform/app/agreement/refuseRelease',
  // 驳回停止发布APP协议
  RefuseStopRelease = '/operation-platform/app/agreement/refuseStopRelease',
  // APP协议测试不通过
  RefuseTest = '/operation-platform/app/agreement/refuseTest',
  // APP列表顺序
  AppItemList = '/operation-platform/app/listOrder/list',
  // APP列表顺序详情
  AppItemDetail = '/operation-platform/app/listOrder/detail',
  // 编辑APP列表顺序
  EditAppList = '/operation-platform/app/listOrder/edit',

  // APP系统消息
  GetSysMessageList = '/operation-platform/app/sysMsg/manage/page',
  // 添加APP系统消息
  AddSysMessage = '/operation-platform/app/sysMsg/save',
  // 编辑APP系统消息
  EditSysMessage = '/operation-platform/app/sysMsg/update',
  // APP系统消息详情
  SysMessageDetail = '/operation-platform/app/sysMsg/detail',
  // 删除APP系统消息
  DeleteSysMessage = '/operation-platform/app/sysMsg/delete',
  // APP系统消息推送结果
  GetSysMessageResult = '/operation-platform/app/sysMsg/pushResult',
  // 申请发布
  SysMessageApplyRelease = '/operation-platform/app/sysMsg/applyRelease',
  // 撤回发布申请
  SysMessageCancelApplyRelease = '/operation-platform/app/sysMsg/cancelApplyRelease',
  // 申请停止发布
  SysMessageApplyStopRelease = '/operation-platform/app/sysMsg/applyStopRelease',
  // 撤回停止发布申请
  SysMessageCancelApplyStopRelease = '/operation-platform/app/sysMsg/cancelApplyStopRelease',
  // 发布被驳回原因
  SysMessageViewRefuseReleaseReason = '/operation-platform/app/sysMsg/viewRefuseReleaseReason',
  // 停止发布被驳回原因
  SysMessageViewRefuseStopReleaseReason = '/operation-platform/app/sysMsg/viewRefuseStopReleaseReason',
  // 测试被驳回原因
  SysMessageViewRefuseTestReason = '/operation-platform/app/sysMsg/viewRefuseTestReason',
  // APP系统消息申请发布列表
  GetSysMessageReleaseList = '/operation-platform/app/sysMsg/release/page',
  // 确认发布
  EnsureReleaseSysMsg = '/operation-platform/app/sysMsg/ensureRelease',
  // 发布驳回
  RefuseReleaseSysMsg = '/operation-platform/app/sysMsg/refuseRelease',
  // 确认停止发布
  EnsureStopReleaseSysMsg = '/operation-platform/app/sysMsg/ensureStopRelease',
  // 停止发布驳回
  RefuseStopReleaseSysMsg = '/operation-platform/app/sysMsg/refuseStopRelease',
  // 确认测试
  EnsureTestSysMsg = '/operation-platform/app/sysMsg/ensureTest',
  // 测试驳回
  RefuseTestSysMsg = '/operation-platform/app/sysMsg/refuseTest',

  // APP营销消息
  GetMarketingMessageList = '/operation-platform/app/marketingMsg/manage/page',
  // 添加APP营销消息
  AddMarketingMessage = '/operation-platform/app/marketingMsg/save',
  // 编辑APP营销消息
  EditMarketingMessage = '/operation-platform/app/marketingMsg/update',
  // APP营销消息详情
  MarketingMessageDetail = '/operation-platform/app/marketingMsg/detail',
  // 删除APP营销消息
  DeleteMarketingMessage = '/operation-platform/app/marketingMsg/delete',
  // APP营销消息推送结果
  GetMarketingMessageResult = '/operation-platform/app/marketingMsg/pushResult',
  // 申请发布
  MarketingMessageApplyRelease = '/operation-platform/app/marketingMsg/applyRelease',
  // 撤回发布申请
  MarketingMessageCancelApplyRelease = '/operation-platform/app/marketingMsg/cancelApplyRelease',
  // 申请停止发布
  MarketingMessageApplyStopRelease = '/operation-platform/app/marketingMsg/applyStopRelease',
  // 撤回停止发布申请
  MarketingMessageCancelApplyStopRelease = '/operation-platform/app/marketingMsg/cancelApplyStopRelease',
  // 发布被驳回原因
  MarketingMessageViewRefuseReleaseReason = '/operation-platform/app/marketingMsg/viewRefuseReleaseReason',
  // 停止发布被驳回原因
  MarketingMessageViewRefuseStopReleaseReason = '/operation-platform/app/marketingMsg/viewRefuseStopReleaseReason',
  // 测试被驳回原因
  MarketingMessageViewRefuseTestReason = '/operation-platform/app/marketingMsg/viewRefuseTestReason',
  // APP营销消息申请发布列表
  GetMarketingMessageReleaseList = '/operation-platform/app/marketingMsg/release/page',
  // 确认发布
  EnsureReleaseMarketingMsg = '/operation-platform/app/marketingMsg/ensureRelease',
  // 发布驳回
  RefuseReleaseMarketingMsg = '/operation-platform/app/marketingMsg/refuseRelease',
  // 确认停止发布
  EnsureStopReleaseMarketingMsg = '/operation-platform/app/marketingMsg/ensureStopRelease',
  // 停止发布驳回
  RefuseStopReleaseMarketingMsg = '/operation-platform/app/marketingMsg/refuseStopRelease',
  // 确认测试
  EnsureTestMarketingMsg = '/operation-platform/app/marketingMsg/ensureTest',
  // 测试驳回
  RefuseTestMarketingMsg = '/operation-platform/app/marketingMsg/refuseTest',

  // 帮助中心列表
  ListAppQuestion = '/operation-platform/help/faq/page',
  // 添加帮助
  CreateAppQuestion = '/operation-platform/help/faq/add',
  // 更新帮助
  SyncAppQuestion = '/operation-platform/help/faq/renew',
  // 编辑帮助
  UpdateAppQuestion = '/operation-platform/help/faq/edit',
  // 删除帮助
  DeleteAppQuestion = '/operation-platform/help/faq/delete',
  // 导入帮助
  ImportAppQuestion = '/operation-platform/help/faq/import',
  // 导出帮助
  ExportAppQuestion = '/operation-platform/help/faq/export',
  // 导入帮助模板
  DownloadAppQuestionTemplate = '/operation-platform/help/faq/template',
  // 隐藏帮助
  HideAppQuestionTemplate = '/operation-platform/help/faq/hide',
  // 显示帮助
  ShowAppQuestionTemplate = '/operation-platform/help/faq/show',
  // 帮助详情
  ViewAppQuestionTemplate = '/operation-platform/help/faq/detail',
}

/**
 * 获取帮助详情
 *
 * @remarks
 *
 * @param helpFaqId - 帮助ID
 * @returns 帮助详情
 *
 * @beta
 */
export async function viewAppQuestionTemplate(helpFaqId: string) {
  return request(Api.ViewAppQuestionTemplate, {
    method: 'POST',
    data: { req: helpFaqId },
  }).then((res) => {
    const item = get(res, ['data']) || {};
    return {
      ...res,
      data: {
        ...item,
        title: {
          message: item.title,
          langId: item.titleLangId,
        },
        answer: {
          message: item.answer,
          langId: item.answerLangId,
        },
      },
    };
  });
}

/**
 * 显示/隐藏帮助
 *
 * @remarks
 *
 * @param helpFaqId - 帮助ID
 * @param visible - 显示为true, 隐藏false
 * @returns
 *
 * @beta
 */
export async function updateAppQuestionVisible(helpFaqId: string, visible: boolean) {
  return request(visible ? Api.ShowAppQuestionTemplate : Api.HideAppQuestionTemplate, {
    method: 'POST',
    data: { req: helpFaqId },
  });
}

/**
 * 导入模板下载
 *
 * @remarks
 *
 * @param
 * @returns
 *
 * @beta
 */
export async function downloadAppQuestionTemplate() {
  return request(Api.DownloadAppQuestionTemplate, {
    method: 'POST',
    type: 'string',
    responseType: 'blob',
    getResponse: true,
  });
}

/**
 * 获取帮助中心列表
 *
 * @remarks
 *
 * @param data - API.AppQuestionPageParams
 * @returns
 *
 * @beta
 */
export async function listAppQuestion(data: API.AppQuestionPageParams) {
  return request(Api.ListAppQuestion, {
    method: 'POST',
    type: 'page',
    data,
  }).then((res) => {
    return {
      ...res,
      data: map(res.data, (item) => {
        return {
          ...item,
          title: {
            message: item.title,
            langId: item.titleLangId,
          },
          answer: {
            message: item.answer,
            langId: item.answerLangId,
          },
        };
      }),
    };
  });
}

/**
 * 创建帮助
 *
 * @remarks
 *
 * @param data - API.AppQuestionItem
 * @returns
 *
 * @beta
 */
export async function createAppQuestion(data: Partial<API.AppQuestionItem>) {
  return request(Api.CreateAppQuestion, {
    method: 'POST',
    data,
  });
}

/**
 * 更新帮助
 *
 * @remarks
 *
 * @param
 * @returns
 *
 * @beta
 */
export async function syncAppQuestion() {
  return request(Api.SyncAppQuestion, {
    method: 'POST',
  });
}

/**
 * 编辑帮助
 *
 * @remarks
 *
 * @param data - API.AppQuestionItem
 * @returns
 *
 * @beta
 */
export async function updateAppQuestion(data: Partial<API.AppQuestionItem>) {
  return request(Api.UpdateAppQuestion, {
    method: 'POST',
    data,
  });
}

/**
 * 删除帮助
 *
 * @remarks
 *
 * @param data - 要删除的帮助ID
 * @returns
 *
 * @beta
 */
export async function deleteAppQuestion(data: string) {
  return request(Api.DeleteAppQuestion, {
    method: 'POST',
    data: { req: data },
  });
}

/**
 * 导入帮助
 *
 * @remarks
 *
 * @param data - FormData
 * @returns
 *
 * @beta
 */
export async function importAppQuestion(data: FormData) {
  return request(Api.ImportAppQuestion, {
    method: 'POST',
    data,
    showType: ErrorShowType.SILENT,
  });
}

/**
 * 导出帮助
 *
 * @remarks
 *
 * @param data - API.AppQuestionPageParams
 * @returns
 *
 * @beta
 */
export async function exportAppQuestion(data: Partial<API.AppQuestionPageParams>) {
  return request(Api.ExportAppQuestion, {
    method: 'POST',
    type: 'string',
    data,
  });
}

/**
 * 获取APP用户账号
 *
 * @remarks
 *
 * @param data - API.UserPageParams
 * @returns
 *
 * @beta
 */
export async function getUserList(data: Partial<API.UserPageParams>) {
  return request(Api.GetUserList, {
    method: 'POST',
    type: 'page',
    data,
  });
}

/**
 * 导出APP用户账号
 *
 * @remarks
 *
 * @param data - API.UserItem
 * @returns
 *
 * @beta
 */
export const exportUserList = (data: Partial<API.UserItem>) => {
  return request(Api.ExportUserList, {
    method: 'post',
    type: 'string',
    data,
    responseType: 'blob',
    getResponse: true,
  });
};

/**
 * 获取APP用户&设备关系
 *
 * @remarks
 *
 * @param data - API.UserDevicePageParams
 * @returns
 *
 * @beta
 */
export async function getUserDeviceList(data: Partial<API.UserDevicePageParams>) {
  return request(Api.GetUserDeviceList, {
    method: 'POST',
    type: 'page',
    data,
  });
}

/**
 * 导出APP用户&设备关系
 *
 * @remarks
 *
 * @param data - API.UserDeviceItem
 * @returns
 *
 * @beta
 */
export const exportUserDevices = (data: Partial<API.UserDeviceItem>) => {
  return request(Api.ExportUserDevices, {
    method: 'post',
    type: 'string',
    data,
    responseType: 'blob',
    getResponse: true,
  });
};

/**
 * 获取APP协议列表
 *
 * @remarks
 *
 * @param data - API.ProtocolPageParams
 * @returns
 *
 * @beta
 */
export function getProtocolList(data: Partial<API.ProtocolPageParams>) {
  return request(Api.ProtocolList, {
    method: 'POST',
    type: 'page',
    data,
  });
}

/**
 * 获取APP协议详情
 *
 * @remarks
 *
 * @param id - 协议ID
 * @returns
 *
 * @beta
 */
export function getProtocolDetail(id: string) {
  return request(Api.ProtocolDetail, {
    method: 'POST',
    data: { req: id },
  }).then((res) => res?.data);
}

/**
 * 新建APP协议
 *
 * @remarks
 *
 * @param data - API.ProtocolItem
 * @returns
 *
 * @beta
 */
export function createProtocol(data: Partial<API.ProtocolItem>) {
  return request(Api.CreateProtocol, {
    method: 'POST',
    data,
  });
}

/**
 * 复制APP协议
 *
 * @remarks
 *
 * @param id - 协议ID
 * @returns
 *
 * @beta
 */
export function copyProtocol(id: string) {
  return request(Api.CopyProtocol, {
    method: 'POST',
    data: { req: id },
  });
}

/**
 * 添加新版本协议
 *
 * @remarks
 *
 * @param data - API.ProtocolItem
 * @returns
 *
 * @beta
 */
export function addNewProtocol(data: Partial<API.ProtocolItem>) {
  return request(Api.NewProtocol, {
    method: 'POST',
    data,
  });
}

/**
 * 编辑APP协议
 *
 * @remarks
 *
 * @param data - API.ProtocolItem
 * @returns
 *
 * @beta
 */
export function editProtocol(data: Partial<API.ProtocolItem>) {
  return request(Api.EditProtocol, {
    method: 'POST',
    data,
  });
}

/**
 * 删除APP协议
 *
 * @remarks
 *
 * @param data - API.ReqCommonParams
 * @returns
 *
 * @beta
 */
export function deleteProtocol(data: API.ReqCommonParams) {
  return request(Api.DeleteProtocol, {
    method: 'POST',
    data,
  });
}

/**
 * 申请发布APP协议
 *
 * @remarks
 *
 * @param id - 协议ID
 * @returns
 *
 * @beta
 */
export function applyProtocolRelease(id: string) {
  return request(Api.ApplyProtocolRelease, {
    method: 'POST',
    data: { req: id },
  });
}

/**
 * 申请发布APP协议
 *
 * @remarks
 *
 * @param id - 协议ID
 * @returns
 *
 * @beta
 */
export function applyStopRelease(id: string) {
  return request(Api.ApplyStopRelease, {
    method: 'POST',
    data: { req: id },
  });
}

/**
 * 撤回发布APP协议申请
 *
 * @remarks
 *
 * @param id - 协议ID
 * @returns
 *
 * @beta
 */
export function cancelProtocolApply(id: string) {
  return request(Api.CancelProtocolApply, {
    method: 'POST',
    data: { req: id },
  });
}

/**
 * 撤回停止发布APP协议申请
 *
 * @remarks
 *
 * @param id - 协议ID
 * @returns
 *
 * @beta
 */
export function cancelApplyStopRelease(id: string) {
  return request(Api.CancelApplyStopRelease, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * 分页查询APP协议发布申请列表
 *
 * @remarks
 *
 * @param data - API.ProductPageParams
 * @returns
 *
 * @beta
 */
export function getAppProtocolList(data: Partial<API.ProductPageParams>) {
  return request(Api.GetAppProtocolList, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 确认发布APP协议
 *
 * @remarks
 *
 * @param id - 协议ID
 * @returns
 *
 * @beta
 */
export function ensureRelease(id: string) {
  return request(Api.EnsureRelease, {
    method: 'POST',
    data: { req: id },
  });
}

/**
 * 确认停止发布APP协议
 *
 * @remarks
 *
 * @param id - 协议ID
 * @returns
 *
 * @beta
 */
export function ensureStopRelease(id: string) {
  return request(Api.EnsureStopRelease, {
    method: 'POST',
    data: { req: id },
  });
}

/**
 * APP协议测试通过
 *
 * @remarks
 *
 * @param id - 协议ID
 * @returns
 *
 * @beta
 */
export function ensureTest(id: string) {
  return request(Api.EnsureTest, {
    method: 'POST',
    data: { req: id },
  });
}

/**
 * 驳回发布APP协议
 *
 * @remarks
 *
 * @param id - 协议ID
 * @returns
 *
 * @beta
 */
export function refuseRelease(data: API.ProductRefuseItem) {
  return request(Api.RefuseRelease, {
    method: 'POST',
    data: { reason: data.remark, appAgreementContentId: data.pid },
  });
}

/**
 * APP协议测试不通过
 *
 * @remarks
 *
 * @param data - API.ProductRefuseItem
 * @returns
 *
 * @beta
 */
export function refuseTest(data: API.ProductRefuseItem) {
  return request(Api.RefuseTest, {
    method: 'POST',
    data: { reason: data.remark, appAgreementContentId: data.pid },
  });
}

/**
 * 驳回停止发布APP协议
 *
 * @remarks
 *
 * @param data - API.ProductRefuseItem
 * @returns
 *
 * @beta
 */
export function refuseStopRelease(data: API.ProductRefuseItem) {
  return request(Api.RefuseStopRelease, {
    method: 'POST',
    data: { reason: data.remark, appAgreementContentId: data.pid },
  });
}

/**
 * APP协议发布被驳回原因
 *
 * @remarks
 *
 * @param data - API.ReqCommonParams
 * @returns
 *
 * @beta
 */
export async function viewRefuseReleaseReason(data: API.ReqCommonParams) {
  try {
    const res = await request(Api.ViewRefuseReleaseReason, {
      method: 'POST',
      data,
    });
    return res.data;
  } catch (e) {
    return {};
  }
}

/**
 * APP协议停止发布被驳回原因
 *
 * @remarks
 *
 * @param data - API.ReqCommonParams
 * @returns
 *
 * @beta
 */
export async function viewRefuseStopReleaseReason(data: API.ReqCommonParams) {
  try {
    const res = await request(Api.ViewRefuseStopReleaseReason, {
      method: 'POST',
      data,
    });
    return res.data;
  } catch (e) {
    return {};
  }
}

/**
 * APP协议测试被驳回原因
 *
 * @remarks
 *
 * @param data - API.ReqCommonParams
 * @returns
 *
 * @beta
 */
export async function viewRefuseTestReason(data: API.ReqCommonParams) {
  try {
    const res = await request(Api.ViewRefuseTestReason, {
      method: 'POST',
      data,
    });
    return res.data;
  } catch (e) {
    return {};
  }
}

/**
 * APP列表顺序
 *
 * @remarks
 *
 * @param data - API.PageParams
 * @returns
 *
 * @beta
 */
export function getAppItemList(data: API.PageParams) {
  return request(Api.AppItemList, {
    method: 'POST',
    type: 'all',
    data,
  });
}

/**
 * APP列表顺序详情
 *
 * @remarks
 *
 * @param code - 列表Code
 * @returns
 *
 * @beta
 */
export function getItemDetail(code: string) {
  return request(Api.AppItemDetail, {
    method: 'POST',
    data: { req: code },
  }).then((res) => res?.data);
}

/**
 * 编辑APP列表顺序
 *
 * @remarks
 *
 * @param data - API.AppSortItem
 * @returns
 *
 * @beta
 */
export function editAppList(data: Partial<API.AppSortItem>) {
  return request(Api.EditAppList, {
    method: 'POST',
    data,
  });
}

/**
 * 获取APP系统消息列表
 *
 * @remarks
 *
 * @param data - API.AppMessagePageParams
 * @returns
 *
 * @beta
 */
export async function getSysMessageList(data: Partial<API.AppMessagePageParams>) {
  return request(Api.GetSysMessageList, {
    method: 'POST',
    type: 'page',
    data,
  });
}

/**
 * 获取APP系统消息推送结果
 *
 * @remarks
 *
 * @param data - API.AppMessageResultPageParams
 * @returns
 *
 * @beta
 */
export async function getSysMessageResultList(data: Partial<API.AppMessageResultPageParams>) {
  return request(Api.GetSysMessageResult, {
    method: 'POST',
    type: 'page',
    data,
  });
}

/**
 * 添加APP系统消息
 *
 * @remarks
 *
 * @param data - API.AppMessageItem
 * @returns
 *
 * @beta
 */
export function addSysMessage(data: API.AppMessageItem) {
  return request(Api.AddSysMessage, {
    method: 'POST',
    data,
  });
}

/**
 * 编辑APP系统消息
 *
 * @remarks
 *
 * @param data - API.AppMessageItem
 * @returns
 *
 * @beta
 */
export function editSysMessage(data: API.AppMessageItem) {
  return request(Api.EditSysMessage, {
    method: 'POST',
    data,
  });
}

/**
 * 获取APP系统消息详情
 *
 * @remarks
 *
 * @param id - 消息ID
 * @returns
 *
 * @beta
 */
export function getSysMessageDetail(id: string) {
  return request(Api.SysMessageDetail, {
    method: 'POST',
    data: { req: id },
  }).then((res) => res?.data);
}

/**
 * 删除APP系统消息
 *
 * @remarks
 *
 * @param data - API.ReqCommonParams
 * @returns
 *
 * @beta
 */
export function deleteSysMessage(data: API.ReqCommonParams) {
  return request(Api.DeleteSysMessage, {
    method: 'POST',
    data,
  });
}

/**
 * APP系统消息申请发布
 *
 * @remarks
 *
 * @param id - 消息ID
 * @returns
 *
 * @beta
 */
export function applyReleaseSysMessage(id: string) {
  return request(Api.SysMessageApplyRelease, {
    method: 'POST',
    data: { req: id },
  });
}

/**
 * APP系统消息撤回发布申请
 *
 * @remarks
 *
 * @param id - 消息ID
 * @returns
 *
 * @beta
 */
export function cancelApplyReleaseSysMessage(id: string) {
  return request(Api.SysMessageCancelApplyRelease, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * APP系统消息申请停止发布
 *
 * @remarks
 *
 * @param id - 消息ID
 * @returns
 *
 * @beta
 */
export function applyStopReleaseSysMessage(id: string) {
  return request(Api.SysMessageApplyStopRelease, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * APP系统消息撤回停止发布申请
 *
 * @remarks
 *
 * @param id - 消息ID
 * @returns
 *
 * @beta
 */
export function cancelApplyStopReleaseSysMessage(id: string) {
  return request(Api.SysMessageCancelApplyStopRelease, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * APP系统消息发布被驳回原因
 *
 * @remarks
 *
 * @param data - API.ReqCommonParams
 * @returns
 *
 * @beta
 */
export async function viewRefuseReleaseReasonSysMessage(data: API.ReqCommonParams) {
  try {
    const res = await request(Api.SysMessageViewRefuseReleaseReason, {
      method: 'POST',
      data,
    });
    return res.data;
  } catch (e) {
    return {};
  }
}
/**
 * APP系统消息停止发布被驳回原因
 *
 * @remarks
 *
 * @param data - API.ReqCommonParams
 * @returns
 *
 * @beta
 */
export async function viewRefuseStopReleaseReasonSysMessage(data: API.ReqCommonParams) {
  try {
    const res = await request(Api.SysMessageViewRefuseStopReleaseReason, {
      method: 'POST',
      data,
    });
    return res.data;
  } catch (e) {
    return {};
  }
}
/**
 * APP系统消息测试被驳回原因
 *
 * @remarks
 *
 * @param data - API.ReqCommonParams
 * @returns
 *
 * @beta
 */
export async function viewRefuseTestReasonSysMessage(data: API.ReqCommonParams) {
  try {
    const res = await request(Api.SysMessageViewRefuseTestReason, {
      method: 'POST',
      data,
    });
    return res.data;
  } catch (e) {
    return {};
  }
}
/**
 * 确认发布APP系统消息
 *
 * @remarks
 *
 * @param id - 消息ID
 * @returns
 *
 * @beta
 */
export function ensureReleaseSysMsg(id: string) {
  return request(Api.EnsureReleaseSysMsg, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * APP系统消息发布驳回
 *
 * @remarks
 *
 * @param data - API.ProductRefuseItem
 * @returns
 *
 * @beta
 */
export function refuseReleaseSysMsg(data: API.ProductRefuseItem) {
  return request(Api.RefuseReleaseSysMsg, {
    method: 'POST',
    data: { operation: 'ensure_release', reason: data.remark, sysMsgId: data.pid },
  });
}
/**
 * APP系统消息测试通过
 *
 * @remarks
 *
 * @param id - 消息ID
 * @returns
 *
 * @beta
 */
//
export function ensureTestSysMsg(id: string) {
  return request(Api.EnsureTestSysMsg, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * APP系统消息测试驳回
 *
 * @remarks
 *
 * @param data - API.ProductRefuseItem
 * @returns
 *
 * @beta
 */
export function refuseTestSysMsg(data: API.ProductRefuseItem) {
  return request(Api.RefuseTestSysMsg, {
    method: 'POST',
    data: { operation: 'refuse_test', reason: data.remark, sysMsgId: data.pid },
  });
}
/**
 * 确认停止发布APP系统消息
 *
 * @remarks
 *
 * @param id - 消息ID
 * @returns
 *
 * @beta
 */
export function ensureStopReleaseSysMsg(id: string) {
  return request(Api.EnsureStopReleaseSysMsg, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * APP系统消息停止发布驳回
 *
 * @remarks
 *
 * @param data - API.ProductRefuseItem
 * @returns
 *
 * @beta
 */
export function refuseStopReleaseSysMsg(data: API.ProductRefuseItem) {
  return request(Api.RefuseStopReleaseSysMsg, {
    method: 'POST',
    data: {
      operation: 'refuse_stop_release',
      reason: data.remark,
      sysMsgId: data.pid,
    },
  });
}
/**
 * 获取APP系统消息申请发布列表
 *
 * @remarks
 *
 * @param data - API.AppMessagePageParams
 * @returns
 *
 * @beta
 */
export async function getSysMessageReleaseList(data: Partial<API.AppMessagePageParams>) {
  return request(Api.GetSysMessageReleaseList, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 获取APP营销消息列表
 *
 * @remarks
 *
 * @param data - API.AppMessagePageParams
 * @returns
 *
 * @beta
 */
export async function getMarketingMessageList(data: Partial<API.AppMessagePageParams>) {
  return request(Api.GetMarketingMessageList, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 获取APP营销消息推送结果
 *
 * @remarks
 *
 * @param data - API.AppMessageResultPageParams
 * @returns
 *
 * @beta
 */
export async function getMarketingMessageResultList(data: Partial<API.AppMessageResultPageParams>) {
  return request(Api.GetMarketingMessageResult, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 添加APP营销消息
 *
 * @remarks
 *
 * @param data - API.AppMessageItem
 * @returns
 *
 * @beta
 */
export function addMarketingMessage(data: API.AppMessageItem) {
  return request(Api.AddMarketingMessage, {
    method: 'POST',
    data,
  });
}
/**
 * 编辑APP营销消息
 *
 * @remarks
 *
 * @param data - API.AppMessageItem
 * @returns
 *
 * @beta
 */
export function editMarketingMessage(data: API.AppMessageItem) {
  return request(Api.EditMarketingMessage, {
    method: 'POST',
    data,
  });
}
/**
 * APP营销消息详情
 *
 * @remarks
 *
 * @param id - 消息ID
 * @returns
 *
 * @beta
 */
export function getMarketingMessageDetail(id: string) {
  return request(Api.MarketingMessageDetail, {
    method: 'POST',
    data: { req: id },
  }).then((res) => res?.data);
}
/**
 * 删除APP营销消息
 *
 * @remarks
 *
 * @param data - API.ReqCommonParams
 * @returns
 *
 * @beta
 */
export function deleteMarketingMessage(data: API.ReqCommonParams) {
  return request(Api.DeleteMarketingMessage, {
    method: 'POST',
    data,
  });
}
/**
 * APP营销消息申请发布
 *
 * @remarks
 *
 * @param id - 消息ID
 * @returns
 *
 * @beta
 */
export function applyReleaseMarketingMessage(id: string) {
  return request(Api.MarketingMessageApplyRelease, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * APP营销消息撤回发布申请
 *
 * @remarks
 *
 * @param id - 消息ID
 * @returns
 *
 * @beta
 */
export function cancelApplyReleaseMarketingMessage(id: string) {
  return request(Api.MarketingMessageCancelApplyRelease, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * APP营销消息申请停止发布
 *
 * @remarks
 *
 * @param id - 消息ID
 * @returns
 *
 * @beta
 */
export function applyStopReleaseMarketingMessage(id: string) {
  return request(Api.MarketingMessageApplyStopRelease, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * APP营销消息撤回停止发布申请
 *
 * @remarks
 *
 * @param id - 消息ID
 * @returns
 *
 * @beta
 */
export function cancelApplyStopReleaseMarketingMessage(id: string) {
  return request(Api.MarketingMessageCancelApplyStopRelease, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * APP营销消息发布被驳回原因
 *
 * @remarks
 *
 * @param data - API.ReqCommonParams
 * @returns
 *
 * @beta
 */
export async function viewRefuseReleaseReasonMarketingMessage(data: API.ReqCommonParams) {
  try {
    const res = await request(Api.MarketingMessageViewRefuseReleaseReason, {
      method: 'POST',
      data,
    });
    return res.data;
  } catch (e) {
    return {};
  }
}
/**
 * APP营销消息停止发布被驳回原因
 *
 * @remarks
 *
 * @param data - API.ReqCommonParams
 * @returns
 *
 * @beta
 */
export async function viewRefuseStopReleaseReasonMarketingMessage(data: API.ReqCommonParams) {
  try {
    const res = await request(Api.MarketingMessageViewRefuseStopReleaseReason, {
      method: 'POST',
      data,
    });
    return res.data;
  } catch (e) {
    return {};
  }
}
/**
 * APP营销消息测试被驳回原因
 *
 * @remarks
 *
 * @param data - API.ReqCommonParams
 * @returns
 *
 * @beta
 */
export async function viewRefuseTestReasonMarketingMessage(data: API.ReqCommonParams) {
  try {
    const res = await request(Api.MarketingMessageViewRefuseTestReason, {
      method: 'POST',
      data,
    });
    return res.data;
  } catch (e) {
    return {};
  }
}
/**
 * 获取APP营销消息申请发布列表
 *
 * @remarks
 *
 * @param data - API.AppMessagePageParams
 * @returns
 *
 * @beta
 */
export async function getMarketingMessageReleaseList(data: Partial<API.AppMessagePageParams>) {
  return request(Api.GetMarketingMessageReleaseList, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * APP营销消息申请发布通过
 *
 * @remarks
 *
 * @param id - 消息ID
 * @returns
 *
 * @beta
 */
export function ensureReleaseMarketingMsg(id: string) {
  return request(Api.EnsureReleaseMarketingMsg, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * 驳回APP营销消息发布申请
 *
 * @remarks
 *
 * @param data - API.ProductRefuseItem
 * @returns
 *
 * @beta
 */
export function refuseReleaseMarketingMsg(data: API.ProductRefuseItem) {
  return request(Api.RefuseReleaseMarketingMsg, {
    method: 'POST',
    data: { operation: 'ensure_release', reason: data.remark, marketingMsgId: data.pid },
  });
}
/**
 * APP营销消息测试通过
 *
 * @remarks
 *
 * @param id - 消息ID
 * @returns
 *
 * @beta
 */
export function ensureTestMarketingMsg(id: string) {
  return request(Api.EnsureTestMarketingMsg, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * APP营销消息测试驳回
 *
 * @remarks
 *
 * @param data - API.ProductRefuseItem
 * @returns
 *
 * @beta
 */
export function refuseTestMarketingMsg(data: API.ProductRefuseItem) {
  return request(Api.RefuseTestMarketingMsg, {
    method: 'POST',
    data: { operation: 'refuse_test', reason: data.remark, marketingMsgId: data.pid },
  });
}
/**
 * 通过APP营销消息停止发布申请
 *
 * @remarks
 *
 * @param id - 消息ID
 * @returns
 *
 * @beta
 */
export function ensureStopReleaseMarketingMsg(id: string) {
  return request(Api.EnsureStopReleaseMarketingMsg, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * 驳回APP营销消息停止发布申请
 *
 * @remarks
 *
 * @param data - API.ProductRefuseItem
 * @returns
 *
 * @beta
 */
export function refuseStopReleaseMarketingMsg(data: API.ProductRefuseItem) {
  return request(Api.RefuseStopReleaseMarketingMsg, {
    method: 'POST',
    data: {
      operation: 'refuse_stop_release',
      reason: data.remark,
      marketingMsgId: data.pid,
    },
  });
}
