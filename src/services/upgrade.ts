import { request } from '@@/plugin-request/request';
import { map } from 'lodash-es';

enum Api {
  ListUpgradeVersion = '/operation-platform/android/version/page',
  CreateUpgradeVersion = '/operation-platform/android/version/add',
  DeleteUpgradeVersion = '/operation-platform/android/version/delete',
  ViewUpgradeVersion = '/operation-platform/android/version/detail',
}
/**
 * 分页查询APP安卓版本升级记录
 *
 * @remarks
 *
 * @param data - API.AppUpgradePageParams
 * @returns
 *
 * @beta
 */
export async function listUpgradeVersion(data: API.AppUpgradePageParams) {
  return request<{ data: API.AppUpgradeItem[] }>(Api.ListUpgradeVersion, {
    method: 'POST',
    type: 'page',
    data,
  }).then((res) => {
    return {
      ...res,
      data: map(res.data, (item) => {
        return {
          ...item,
          name: {
            message: item.name as string,
          },
          updateContent: {
            message: item.updateContent as string,
            langId: item.updateContentLangId,
          },
        };
      }),
    };
  });
}
/**
 * 添加APP安卓版本升级记录
 *
 * @remarks
 *
 * @param data - API.AppUpgradeItem
 * @returns
 *
 * @beta
 */
export async function createUpgradeVersion(data: API.AppUpgradeItem) {
  return request(Api.CreateUpgradeVersion, {
    method: 'POST',
    data,
  });
}
/**
 * 删除APP安卓版本升级记录
 *
 * @remarks
 *
 * @param id - 升级记录ID
 * @returns
 *
 * @beta
 */
export async function deleteUpgradeVersion(id: string) {
  return request(Api.DeleteUpgradeVersion, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * APP安卓版本升级记录详情
 *
 * @remarks
 *
 * @param id - 升级记录ID
 * @returns
 *
 * @beta
 */
export async function viewUpgradeVersion(id: string) {
  return request(Api.ViewUpgradeVersion, {
    method: 'POST',
    data: { req: id },
  });
}
