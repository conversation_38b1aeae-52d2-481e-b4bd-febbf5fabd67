import { request } from 'umi';
import { isArray, map, reduce, get } from 'lodash-es';
import { ErrorShowType } from '@@/plugin-request/request';
enum Api {
  GetMultiLanguages = '/operation-platform/m/language/listLanguageBySysCode',
  UploadImage = '/operation-platform/common/get/signedUrl',
  GetS3File = '/operation-platform/common/networkfile/convert/s3/filekey/get',
  GetAllBrand = '/operation-platform/brand/all',
  GetAllCategory = '/operation-platform/category/all',
  GetVisibleMenus = '/operation-platform/resource/list',
  GetFleetDeviceCategory = '/operation-platform/fleet/company/device/category/list',
}

/**
 * 获取平台的多语言词条
 *
 * @remarks
 *
 * @params sysCode - 系统Code
 * @returns
 *
 * @beta
 */
export function getMultiLanguages(sysCode: string) {
  return request(Api.GetMultiLanguages, {
    method: 'POST',
    data: { req: sysCode },
    ignoreTrack: true,
  });
}
/**
 * 获取图片上传的aws预签名URL
 *
 * @remarks
 *
 * @params data - fileName：文件名称；fileType：文件类型
 * @returns
 *
 * @beta
 */
export function uploadImage(data: { fileName: string; fileType: string }) {
  return request(Api.UploadImage, {
    method: 'POST',
    data,
    showType: ErrorShowType.SILENT,
  }).then((res) => res.data);
}
/**
 * 下载网络文件，存储志S3，返回文件地址
 *
 * @remarks
 *
 * @params data - networkFileUrl: url路径
 * @returns
 *
 * @beta
 */
export function getS3File(networkFileUrl: string): Promise<{ key: string; size: number }> {
  return request(Api.GetS3File, {
    method: 'POST',
    data: {
      networkFileUrl,
    },
  }).then((res) => res.data);
}
/**
 * 获取全部品牌
 *
 * @remarks
 *
 * @params
 * @returns
 *
 * @beta
 */
export function getAllBrand() {
  return request(Api.GetAllBrand, {
    method: 'POST',
    type: 'all',
    ignoreTrack: true,
  }).then((res: any) =>
    map(res.data, (item) => ({
      id: item.id,
      brandName: get(item, ['brandName', 'message'], ''),
    })),
  );
}
/**
 * 获取全部品类
 *
 * @remarks
 *
 * @params
 * @returns
 *
 * @beta
 */
export function getAllCategory() {
  return request(Api.GetAllCategory, {
    method: 'POST',
    type: 'all',
    ignoreTrack: true,
  }).then((res: any) =>
    map(res.data, (item) => ({
      id: item.id,
      categoryName: get(item, ['categoryName', 'message'], ''),
    })),
  );
}
/**
 * 获取按钮资源
 *
 * @remarks
 *
 * @params list - 资源列表
 * @returns
 *
 * @beta
 */
export const getCode: any = (list?: Record<string, any>[]) => {
  if (!isArray(list) || list.length <= 0) return {};
  return reduce(
    list,
    (result, current, index) => {
      if (current.elementId && current.type === 'B') result[current.elementId] = true;
      return { ...result, ...getCode(current.children) };
    },
    {},
  );
};
/**
 * 获取授权资源（按钮、菜单）
 *
 * @remarks
 *
 * @params
 * @returns
 *
 * @beta
 */
export async function getPatchMenus() {
  return request(Api.GetVisibleMenus, {
    method: 'POST',
    data: { req: 'operation' },
  }).then(({ data }) => {
    // localStorage.setItem('accesses', JSON.stringify(data || []));
    return data;
  });
}

/**
 * 获取租户设备品类
 *
 * @remarks
 *
 * @params
 * @returns
 *
 * @beta
 */
export function getFleetDeviceCategories() {
  return request(Api.GetFleetDeviceCategory, {
    method: 'POST',
    type: 'all',
  }).then((res: any) =>
    map(res.data, (item) => ({
      code: item.code,
      categoryName: item.categoryName,
    })),
  );
}
