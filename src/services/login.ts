import { request } from 'umi';

enum Api {
  SsoAuthUrl = '/operation-platform/sso/getSsoAuthUrl',
  LoginByTicket = '/operation-platform/sso/doLoginByTicket',
}
/**
 * 获取单点登录地址
 *
 * @remarks
 *
 * @param data - clientLoginUrl：登录返回地址
 * @returns
 *
 * @beta
 */
export function getSsoAuthUrl(data: { clientLoginUrl: string }) {
  return request(Api.SsoAuthUrl, {
    method: 'get',
    params: data,
  });
}
/**
 * 获取token
 *
 * @remarks
 *
 * @param data - ticket：登录令牌
 * @returns
 *
 * @beta
 */
export function doLoginByTicket(data: { ticket: string }) {
  return request(Api.LoginByTicket, {
    method: 'get',
    params: data,
  });
}
