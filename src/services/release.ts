import { request } from 'umi';

enum Api {
  PassProductRelease = '/operation-platform/product/confirm/release',
  PassProductOff = '/operation-platform/product/confirm/off',
  PassProductUpdate = '/operation-platform/product/confirm/update/release',
  ExportProducts = '/operation-platform/product/release/export',

  GetFirmwareList = '/operation-platform/ota/job/list/release',
  ExportFirmwares = '/operation-platform/ota/job/export/release',
  UpdateFirmwareStatus = '/operation-platform/ota/job/status/update',
}
/**
 * 导出申请发布产品列表
 *
 * @remarks
 *
 * @param data - API.ProductPageParams
 * @returns
 *
 * @beta
 */
export const exportProducts = (data: Partial<API.ProductPageParams>) => {
  return request(Api.ExportProducts, {
    method: 'post',
    type: 'string',
    data,
    responseType: 'blob',
    getResponse: true,
  });
};
/**
 * 确认产品发布申请
 *
 * @remarks
 *
 * @param id - 产品ID
 * @returns
 *
 * @beta
 */
export function passProductRelease(id: string) {
  return request(Api.PassProductRelease, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * 确认产品下架申请
 *
 * @remarks
 *
 * @param id - 产品ID
 * @returns
 *
 * @beta
 */
export function passProductOff(id: string) {
  return request(Api.PassProductOff, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * 确认产品更新申请
 *
 * @remarks
 *
 * @param id - 产品ID
 * @returns
 *
 * @beta
 */
export function passProductUpdate(id: string) {
  return request(Api.PassProductUpdate, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * 分页查询固件发布申请列表
 *
 * @remarks
 *
 * @param data - API.FirmwareReleasePageParams
 * @returns
 *
 * @beta
 */
export function getFirmwareList(data: Partial<API.FirmwareReleasePageParams>) {
  return request(Api.GetFirmwareList, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 导出固件发布申请列表
 *
 * @remarks
 *
 * @param data - API.FirmwareReleasePageParams
 * @returns
 *
 * @beta
 */
export const exportFirmwares = (data: Partial<API.FirmwareReleasePageParams>) => {
  return request(Api.ExportFirmwares, {
    method: 'post',
    type: 'string',
    data,
    responseType: 'blob',
    getResponse: true,
  });
};
/**
 * 确认固件发布申请
 *
 * @remarks
 *
 * @param id - 固件ID
 * @returns
 *
 * @beta
 */
export function passFirmwareRelease(id: string) {
  return request(Api.UpdateFirmwareStatus, {
    method: 'POST',
    data: { operation: 'PASSED', jobId: id },
  });
}
/**
 * 确认固件测试通过
 *
 * @remarks
 *
 * @param id - 固件ID
 * @returns
 *
 * @beta
 */
export function passFirmwareTest(id: string) {
  return request(Api.UpdateFirmwareStatus, {
    method: 'POST',
    data: { operation: 'TEST_PASSED', jobId: id },
  });
}
/**
 * 确认固件停止发布申请
 *
 * @remarks
 *
 * @param id - 固件ID
 * @returns
 *
 * @beta
 */
export function passFirmwareStop(id: string) {
  return request(Api.UpdateFirmwareStatus, {
    method: 'POST',
    data: { operation: 'STOP_PASSED', jobId: id },
  });
}
/**
 * 确认固件作废申请
 *
 * @remarks
 *
 * @param id - 固件ID
 * @returns
 *
 * @beta
 */
export function passFirmwareNullify(id: string) {
  return request(Api.UpdateFirmwareStatus, {
    method: 'POST',
    data: { operation: 'NULLIFY_PASSED', jobId: id },
  });
}
/**
 * 驳回固件发布申请
 *
 * @remarks
 *
 * @param data - API.ProductRefuseItem
 * @returns
 *
 * @beta
 */
export function refuseFirmwareRelease(data: API.ProductRefuseItem) {
  return request(Api.UpdateFirmwareStatus, {
    method: 'POST',
    data: { operation: 'REFUSED', description: data.remark, jobId: data.pid },
  });
}
/**
 * 驳回固件测试
 *
 * @remarks
 *
 * @param data - API.ProductRefuseItem
 * @returns
 *
 * @beta
 */
export function refuseFirmwareTest(data: API.ProductRefuseItem) {
  return request(Api.UpdateFirmwareStatus, {
    method: 'POST',
    data: { operation: 'TEST_REFUSED', description: data.remark, jobId: data.pid },
  });
}
/**
 * 驳回固件停止发布申请
 *
 * @remarks
 *
 * @param data - API.ProductRefuseItem
 * @returns
 *
 * @beta
 */
export function refuseFirmwareStop(data: API.ProductRefuseItem) {
  return request(Api.UpdateFirmwareStatus, {
    method: 'POST',
    data: { operation: 'STOP_REFUSED', description: data.remark, jobId: data.pid },
  });
}
/**
 * 驳回固件作废申请
 *
 * @remarks
 *
 * @param data - API.ProductRefuseItem
 * @returns
 *
 * @beta
 */
export function refuseFirmwareNullify(data: API.ProductRefuseItem) {
  return request(Api.UpdateFirmwareStatus, {
    method: 'POST',
    data: { operation: 'NULLIFY_REFUSED', description: data.remark, jobId: data.pid },
  });
}
