import { request } from 'umi';
import { ErrorShowType } from '@@/plugin-request/request';
enum Api {
  // 品牌列表
  List = '/operation-platform/brand/list',
  // 创建品牌
  Create = '/operation-platform/brand/add',
  // 编辑品牌
  Edit = '/operation-platform/brand/edit',
  // 删除品牌
  Delete = '/operation-platform/brand/delete',
}
/**
 * 获取品牌列表
 *
 * @remarks
 *
 * @param data - API.BrandPageParams
 * @returns
 *
 * @beta
 */
export async function getList(data: Partial<API.BrandPageParams>) {
  return request(Api.List, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 创建品牌
 *
 * @remarks
 *
 * @param data - API.BrandItem
 * @returns
 *
 * @beta
 */
export function createBrand(data: Partial<API.BrandItem>) {
  return request(Api.Create, {
    method: 'POST',
    data,
  });
}
/**
 * 编辑品牌
 *
 * @remarks
 *
 * @param data - API.BrandItem
 * @returns
 *
 * @beta
 */
export function editBrand(data: Partial<API.BrandItem>) {
  return request(Api.Edit, {
    method: 'POST',
    data,
  });
}
/**
 * 删除品牌
 *
 * @remarks
 *
 * @param req - 品牌ID
 * @returns
 *
 * @beta
 */
export function deleteBrand(req: string) {
  return request(Api.Delete, {
    method: 'POST',
    data: { req },
    showType: ErrorShowType.SILENT,
    force: true,
  });
}
