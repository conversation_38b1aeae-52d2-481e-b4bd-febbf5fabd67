import { request } from 'umi';

enum Api {
  List = '/operation-platform/device/group/page',
  Create = '/operation-platform/device/group/add',
  Update = '/operation-platform/device/group/edit',
  Delete = '/operation-platform/device/group/delete',
  DeviceResultList = '/operation-platform/device/group/device/query',
  UserResultList = '/operation-platform/device/group/user/query',
}
/**
 * 分页查询分组列表
 *
 * @remarks
 *
 * @param data - API.GroupPageParams
 * @returns
 *
 * @beta
 */
export async function getGroupList(data: API.GroupPageParams) {
  return request(Api.List, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 创建分组
 *
 * @remarks
 *
 * @param data - API.GroupItem
 * @returns
 *
 * @beta
 */
export function createGroup(data: API.GroupItem) {
  return request(Api.Create, {
    method: 'POST',
    data,
  });
}
/**
 * 编辑分组
 *
 * @remarks
 *
 * @param data - API.GroupItem
 * @returns
 *
 * @beta
 */
export function updateGroup(data: API.GroupItem) {
  return request(Api.Update, {
    method: 'POST',
    data,
  });
}
/**
 * 删除分组
 *
 * @remarks
 *
 * @param req - 分组ID
 * @returns
 *
 * @beta
 */
export function deleteGroup(req: string) {
  return request(Api.Delete, {
    method: 'POST',
    data: { req },
  });
}
/**
 * 获取设备分组结果
 *
 * @remarks
 *
 * @param data - API.GroupResultPageParams
 * @returns
 *
 * @beta
 */
export async function getDeviceGroupList(data: Partial<API.GroupResultPageParams>) {
  return request(Api.DeviceResultList, {
    method: 'POST',
    data,
  }).then((res) => ({ data: res?.data?.devices }));
}
/**
 * 获取用户分组结果
 *
 * @remarks
 *
 * @param data - API.GroupResultPageParams
 * @returns
 *
 * @beta
 */
export async function getUserGroupList(data: Partial<API.GroupResultPageParams>) {
  return request(Api.UserResultList, {
    method: 'POST',
    type: 'page',
    data,
  });
}
