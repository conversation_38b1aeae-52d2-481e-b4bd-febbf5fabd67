import { request } from 'umi';
import { ErrorShowType } from '@@/plugin-request/request';
enum Api {
  List = '/operation-platform/category/list',
  Create = '/operation-platform/category/add',
  Edit = '/operation-platform/category/edit',
  Delete = '/operation-platform/category/delete',
}
/**
 * 获取品类列表
 *
 * @remarks
 *
 * @param data - API.CategoryPageParams
 * @returns
 *
 * @beta
 */
export async function getList(data: Partial<API.CategoryPageParams>) {
  return request(Api.List, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 创建品类
 *
 * @remarks
 *
 * @param data - API.CategoryItem
 * @returns
 *
 * @beta
 */
export function createCategory(data: Partial<API.CategoryItem>) {
  return request(Api.Create, {
    method: 'POST',
    data,
  });
}
/**
 * 编辑品类
 *
 * @remarks
 *
 * @param data - API.CategoryItem
 * @returns
 *
 * @beta
 */
export function editCategory(data: Partial<API.CategoryItem>) {
  return request(Api.Edit, {
    method: 'POST',
    data,
  });
}
/**
 * 删除品类
 *
 * @remarks
 *
 * @param req - 品类ID
 * @returns
 *
 * @beta
 */
export function deleteCategory(req: string) {
  return request(Api.Delete, {
    method: 'POST',
    data: { req },
    showType: ErrorShowType.SILENT,
    force: true,
  });
}
