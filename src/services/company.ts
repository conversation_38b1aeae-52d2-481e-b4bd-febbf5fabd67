import { request } from 'umi';

enum Api {
  // 租户
  LIST = '/operation-platform/fleet/company/page',
  // 导出租户
  EXPORTLIST = '/operation-platform/fleet/company/export',
  // 启用租户
  ENABLE = '/operation-platform/fleet/company/enable',
  // 停用租户
  STOP = '/operation-platform/fleet/company/deactivate',
  // 注销租户
  CANCEL = '/operation-platform/fleet/company/logoff',
  // 租户详情
  DETAIL = '/operation-platform/fleet/company/detail',
  // 管理员变更记录
  ChANGES = '/operation-platform/fleet/company/adminChangeRecord',
  // 租户与设备关系
  DEVICES = '/operation-platform/fleet/company/device/page',
  // 导出租户与设备关系
  EXPORTDEVICES = '/operation-platform/fleet/company/device/export',
  // 租户员工列表
  EMPLOYEES = '/operation-platform/fleet/user/page',
  // 导出租户员工
  EXPORTEMPLOYEE = '/operation-platform/fleet/user/export',
  // 启用员工
  ENABLEEMPLOYEE = '/operation-platform/fleet/user/enable',
  // 停用员工
  STOPEMPLOYEE = '/operation-platform/fleet/user/deactivate',
  // 员工详情
  EMPLOYEEDETAIL = '/operation-platform/fleet/user/detail',
}
/**
 * 获取租户列表
 *
 * @remarks
 *
 * @param data - API.CompanyPageParams
 * @returns
 *
 * @beta
 */
export async function getList(data: Partial<API.CompanyPageParams>) {
  return request(Api.LIST, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 导出租户
 *
 * @remarks
 *
 * @param data - API.CompanyPageParams
 * @returns
 *
 * @beta
 */
export function exportList(data: Partial<API.CompanyPageParams>) {
  return request(Api.EXPORTLIST, {
    method: 'POST',
    type: 'string',
    data,
    responseType: 'blob',
    getResponse: true,
  });
}
/**
 * 获取租户详情
 *
 * @remarks
 *
 * @param id - 租户ID
 * @returns
 *
 * @beta
 */
export function getDetail(id: string) {
  return request(Api.DETAIL, {
    method: 'POST',
    data: { req: id },
  }).then((res) => res?.data);
}

/**
 * 获取管理员变更记录
 *
 * @remarks
 *
 * @param data - API.adminChangeSearchParams
 * @returns
 *
 * @beta
 */
export function getAdminChanges(data: Partial<API.adminChangeSearchParams>) {
  return request(Api.ChANGES, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 停用租户
 *
 * @remarks
 *
 * @param id - 租户ID
 * @returns
 *
 * @beta
 */
export function stopCompany(id: string) {
  return request(Api.STOP, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * 启用租户
 *
 * @remarks
 *
 * @param id - 租户ID
 * @returns
 *
 * @beta
 */
export function enableCompany(id: string) {
  return request(Api.ENABLE, {
    method: 'POST',
    data: { req: id },
  });
}

/**
 * 注销租户
 *
 * @remarks
 *
 * @param companyId - 租户ID
 * @param password - 登录账号密码
 * @returns
 *
 * @beta
 */
export function cancelCompany(data: { companyId: string; password: string }) {
  return request(Api.CANCEL, {
    method: 'POST',
    data,
  });
}

/**
 * 获取租户与设备关系列表
 *
 * @remarks
 *
 * @param data - API.CompanyAndDevicePageParams
 * @returns
 *
 * @beta
 */
export function getDevices(data: Partial<API.CompanyAndDevicePageParams>) {
  return request(Api.DEVICES, {
    method: 'POST',
    type: 'page',
    data,
  });
}

/**
 * 导出租户与设备关系
 *
 * @remarks
 *
 * @param data - API.CompanyAndDevicePageParams
 * @returns
 *
 * @beta
 */
export function exportDevices(data: Partial<API.CompanyAndDevicePageParams>) {
  return request(Api.EXPORTDEVICES, {
    method: 'POST',
    type: 'string',
    data,
    responseType: 'blob',
    getResponse: true,
  });
}

/**
 * 获取租户员工
 *
 * @remarks
 *
 * @param data - API.CompanyPageParams
 * @returns
 *
 * @beta
 */
export function getEmployees(data: Partial<API.CompanyPageParams>) {
  return request(Api.EMPLOYEES, {
    method: 'POST',
    type: 'page',
    data,
  });
}
/**
 * 导出员工
 *
 * @remarks
 *
 * @param data - API.CompanyPageParams
 * @returns
 *
 * @beta
 */
export function exportEmployee(data: Partial<API.CompanyPageParams>) {
  return request(Api.EXPORTEMPLOYEE, {
    method: 'POST',
    type: 'string',
    data,
    responseType: 'blob',
    getResponse: true,
  });
}
/**
 * 获取员工详情
 *
 * @remarks
 *
 * @param id - 员工ID
 * @returns
 *
 * @beta
 */
export function getEmployeeDetail(id: string) {
  return request(Api.EMPLOYEEDETAIL, {
    method: 'POST',
    data: { req: id },
  }).then((res) => res?.data);
}

/**
 * 停用员工
 *
 * @remarks
 *
 * @param id - 员工ID
 * @returns
 *
 * @beta
 */
export function stopEmployee(id: string) {
  return request(Api.STOPEMPLOYEE, {
    method: 'POST',
    data: { req: id },
  });
}
/**
 * 启用员工
 *
 * @remarks
 *
 * @param id - 员工ID
 * @returns
 *
 * @beta
 */
export function enableEmployee(id: string) {
  return request(Api.ENABLEEMPLOYEE, {
    method: 'POST',
    data: { req: id },
  });
}
