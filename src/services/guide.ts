import { request } from 'umi';

enum Api {
  // 通用操作分页查询
  GetGuideList = '/operation-platform/common/operationGuidance/manage/page',
  // 导出通用操作
  ExportGuides = '/operation-platform/common/operationGuidance/export',
  // 导入模板
  ImportTemplate = '/operation-platform/common/operationGuidance/template',
  // 创建通用操作指导
  CreateGuide = '/operation-platform/common/operationGuidance/add',
  // 编辑通用操作指导
  EditGuide = '/operation-platform/common/operationGuidance/edit',
  // 下载通用操作指导
  DownloadGuide = '/operation-platform/common/operationGuidance/download',
  // 删除通用操作指导
  DeleteGuide = '/operation-platform/common/operationGuidance/delete',
  // 通用操作指导详情
  GuideDetail = '/operation-platform/common/operationGuidance/detail',
  // 通用操作指导已关联产品分页查询
  GuideProducts = '/operation-platform/common/operationGuidance/product/page',
  // 通用操作指导已关联全部产品
  AllGuideProducts = '/operation-platform/common/operationGuidance/productId/list',
  // 添加关联产品
  AddProducts = '/operation-platform/common/operationGuidance/product/add',
  // 删除关联产品
  DeleteProduct = '/operation-platform/common/operationGuidance/product/delete',
}

// 通用操作分页查询
export async function getGuideList(data: Partial<API.GuideItemPageParams>) {
  return request(Api.GetGuideList, {
    method: 'POST',
    type: 'page',
    data,
  });
}
// 导出通用操作
export const exportGuides = (data: API.GuideItemPageParams) => {
  return request(Api.ExportGuides, {
    method: 'POST',
    type: 'string',
    data,
    responseType: 'blob',
    getResponse: true,
  });
};

// 导入模板
export const getImportTemplate = () => {
  return request(Api.ImportTemplate, {
    method: 'post',
    type: 'string',
    responseType: 'blob',
    getResponse: true,
  });
};

// 通用操作指导详情
export function getGuideDetail(id: string) {
  return request(Api.GuideDetail, {
    method: 'POST',
    data: { req: id },
  }).then((res) => res?.data);
}
// 创建通用操作指导
export function createGuide(data: Partial<API.GuideItem>) {
  return request(Api.CreateGuide, {
    method: 'POST',
    data,
  });
}
// 编辑通用操作指导
export function editGuide(data: Partial<API.GuideItem>) {
  return request(Api.EditGuide, {
    method: 'POST',
    data,
  });
}
// 下载通用操作指导
export function downloadGuide(id: string) {
  return request(Api.DownloadGuide, {
    method: 'POST',
    data: { req: id },
  });
}
// 删除通用操作指导
export function deleteGuide(id: string) {
  return request(Api.DeleteGuide, {
    method: 'POST',
    data: { req: id },
  });
}
// 通用操作指导已关联产品分页查询
export const getGuideProducts = async (data: Partial<API.ProductPageParams>) => {
  return request(Api.GuideProducts, {
    method: 'post',
    type: 'page',
    data,
  });
};
// 通用操作指导已关联全部产品
export const getAllGuideProducts = async (data: Partial<API.ProductPageParams>) => {
  return request(Api.AllGuideProducts, {
    method: 'post',
    data,
  });
};
// 添加关联产品
export async function addGuideProducts(data: {
  commonId: string;
  relatedProduct: Record<string, any>[];
}) {
  return request(Api.AddProducts, {
    method: 'POST',
    data,
  });
}
// 删除关联产品
export async function deleteGuideProduct(data: {
  commonId: string;
  relatedProduct: Record<string, any>[];
}) {
  return request(Api.DeleteProduct, {
    method: 'POST',
    data,
  });
}
