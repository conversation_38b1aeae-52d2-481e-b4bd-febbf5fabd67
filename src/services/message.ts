import { request } from 'umi';

enum Api {
  ListMessageRecord = '/operation-platform/message/record/list',
  ExportMessageRecord = '/operation-platform/message/record/list/export/csv',
  ExportMessagePushRecord = '/operation-platform/message/record/detail/push/record/export/csv',
  ViewMessageRecord = '/operation-platform/message/record/detail/push/record/list',
}
/**
 * 分页查询消息记录
 *
 * @remarks
 *
 * @param data - API.MessageRecordPageParams
 * @returns
 *
 * @beta
 */
export async function listMessageRecord(data: Partial<API.MessageRecordPageParams>) {
  return request(Api.ListMessageRecord, {
    method: 'POST',
    type: 'page',
    data: { ...{ messageType: '0' }, ...data },
  });
}
/**
 * 导出消息记录
 *
 * @remarks
 *
 * @param data - API.MessageRecordPageParams
 * @returns
 *
 * @beta
 */
export function exportMessageRecord(data: Partial<API.MessageRecordPageParams>) {
  return request(Api.ExportMessageRecord, {
    method: 'post',
    type: 'string',
    data: { ...{ messageType: '0' }, ...data },
    responseType: 'blob',
    getResponse: true,
  });
}
/**
 * 导出消息推送记录
 *
 * @remarks
 *
 * @param data - API.MessageRecordPageParams
 * @returns
 *
 * @beta
 */
export function exportMessagePushRecord(data: Partial<API.MessageRecordPageParams>) {
  return request(Api.ExportMessagePushRecord, {
    method: 'post',
    type: 'string',
    data,
    responseType: 'blob',
    getResponse: true,
  });
}
/**
 * 查询消息推送记录
 *
 * @remarks
 *
 * @param data - API.MessageRecordPageParams
 * @returns
 *
 * @beta
 */
export async function viewMessageRecord(data: Partial<API.MessageRecordPageParams>) {
  return request(Api.ViewMessageRecord, {
    method: 'POST',
    type: 'page',
    data,
  });
}
