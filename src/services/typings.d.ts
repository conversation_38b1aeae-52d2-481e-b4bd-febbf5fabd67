declare namespace API {
  interface BaseEntity {
    createTime?: string;
    createBy?: string;
    updateTime?: string;
    updateBy?: string;
  }

  interface BaseApprove {
    applyBy?: string;
    applyTime?: string;
    approvedBy?: string;
    approvedTime?: string;
  }

  interface PageParams {
    current?: number;
    pageSize?: number;
  }

  interface Page<T> {
    data?: T[];
    pageNo: number;
    pageSize: number;
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  }

  interface Entity<T> {
    data: T;
    pageNo: number;
    pageSize: number;
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  }

  interface MultiLanguage {
    langId?: string;
    message?: string;
  }

  interface selectOptions {
    label: string;
    value: string;
  }

  interface ReqCommonParams {
    req?: string;
  }

  interface CurrentUser {
    name?: string;
    avatar?: string;
    userid?: string;
    email?: string;
    signature?: string;
    title?: string;
    group?: string;
    tags?: { key?: string; label?: string }[];
    notifyCount?: number;
    unreadCount?: number;
    country?: string;
    access?: string;
    geographic?: {
      province?: { label?: string; key?: string };
      city?: { label?: string; key?: string };
    };
    address?: string;
    phone?: string;
  }

  interface GroupItem extends BaseEntity {
    index?: string;
    id?: string;
    groupName: string;
    groupType?: string;
    conditionContents?: string[];
    conditionRules?: string[];
    conditionValues?: string[];
    conditionTypes?: string[];
    comments?: string;
    items?: GroupCondition[];
  }

  interface GroupPageParams extends PageParams, BaseEntity {
    groupName?: string;
    groupType?: string;
    content?: string;
    value?: string;
    conditionValues?: string;
    conditionContents?: string;
  }

  interface GroupCondition {
    conditionType: string;
    conditionContent: string;
    conditionRule: string;
    conditionValue: string;
  }

  interface GroupResult {
    id: string;
    deviceId: string;
    productKey: string;
    sn: string;
    online: string;
  }

  interface GroupResultPageParams {
    groupName: string;
    maxResults?: number;
    nextToken?: string;
  }

  /**
   * 多码表
   */
  interface MultiCodeItem extends BaseEntity {
    id?: string;
    deviceId: string;
    moCode?: string;
    itemCode?: string;
    mes?: string;
    sn?: string;
    status: number;
    productSnCode?: string;
    productionDate?: number;
  }

  interface MultiCodePageParams extends PageParams {
    productSnCode: string;
    deviceId?: string;
    moCode?: string;
    itemCode?: string;
    productionDate?: number;
    sn?: string;
    status?: number;
  }

  interface ProductPageParams extends PageParams {
    pid?: string;
    commonId?: string;
    req?: string;
    productName?: string;
    productId?: string;
  }

  /**
   * 品牌
   */
  interface BrandItem {
    id?: string;
    brandName?: any;
    brandIcon?: any;
    description?: string;
    createTime?: number;
    iconType?: string;
    iconAddress?: string;
    langId?: string;
    brandInfo?: string;
  }

  interface BrandPageParams extends PageParams {
    brandName?: string;
    id?: string;
  }

  /**
   * 品类
   */
  interface CategoryItem {
    id?: string;
    categoryName?: any;
    categoryIcon?: any;
    description?: string;
    createTime?: number;
    iconType?: string;
    iconAddress?: string;
    langId?: string;
    categoryInfo?: string;
  }

  interface CategoryPageParams extends PageParams, BaseEntity {
    categoryName?: string;
    id?: string;
  }

  /**
   * 产品
   */
  interface ProductItem {
    /** 品类Id */
    categoryId?: string;
    /** 品类名称 */
    categoryName?: MultiLanguage;
    /** 品牌Id */
    brandId?: string;
    /** 品牌名称 */
    brandName?: MultiLanguage;
    /** 商品型号Model# */
    commodityModel?: string;
    /** 创建者 */
    createBy?: string;
    /** 创建时间 */
    createTime?: string;
    /** 产品描述 */
    description?: string;
    /** 产品Pid */
    id?: string;
    /** 产品型号 */
    model?: string;
    /** 设备联网方式，WiFi+BLE：1001，4G+BLE：1002，BLE：1003，D/T：1004，WiFi：1005，4G：1006，LAN：1007， 不联网：1008 */
    networkModes?: string[];
    /** 产品图标 */
    productIcon?: string;
    /** Icon类型 */
    iconType?: string;
    /** Icon地址 */
    iconAddress?: string;
    /** 产品名称 */
    productName?: string;
    /** 产品完整名称 */
    productFullName?: string;
    /** 产品SnCode */
    productSnCode?: string;
    /** 设备类型：0直连设备，1网管子设备，2网管设备，3独立设备 */
    productType?: string;
    /** 产品状态 开发中: PDevelopIng, 1封板审核中: PCloseApprove, 已封板:PApproved  解版审核中:PReopenApprove  4已解版:PReopenSuccess  拒绝封板：PRefuseClose  拒绝解版：PRefuseReopen */
    status?: string;
    /** 更新者 */
    updateBy?: string;
    /** 更新时间 */
    updateTime?: string;
    sourcePid?: string;
    canUpdate?: boolean;
    /** 问卷模板 */
    questionTemplate?: string[];
    /** 只读 */
    readonly?: boolean;
    releaseStatus?: 'PToBeRelease' | 'PReleased' | 'POffReleased';
    createType?: number;
  }

  interface ProductPageParams extends PageParams {
    /** 模糊搜索的Pid */
    pid?: string;
    /** 品类Id */
    categoryId?: number;
    /** 产品型号 */
    model?: string;

    categoryName?: string;
    id?: string;
  }

  interface ProductReleaseConfig {
    /** 模糊搜索的Pid */
    pid?: string;
    /** 测试分组 */
    testGroupIds?: string[];
  }

  interface ProductRefuseItem {
    pid?: string;
    remark?: string;
  }

  interface FirmwareReleaseItem {
    id?: string;
    /** 任务ID */
    jobId?: string;
    /** 产品ID */
    productId?: string;
    /** 产品型号 */
    productModel?: string;
    /** 商品型号Model# */
    commodityModel?: string;
    /** 品类Id */
    categoryId?: string;
    /** 品类名称 */
    categoryName?: MultiLanguage;
    /** 品牌Id */
    brandId?: string;
    /** 品牌名称 */
    brandName?: MultiLanguage;
    /** 固件数量 */
    packageCount?: string;
    /** 发布状态 */
    releaseStatus?: string;
    applyBy?: string;
    applyTime?: string;
    ensuredBy?: string;
    ensuredTime?: string;
  }

  interface FirmwareReleasePageParams extends PageParams {
    /** 任务ID */
    jobId?: string;
    /** 产品ID */
    productId?: string;
    /** 产品型号 */
    productModel?: string;
    /** 品类Id */
    categoryId?: string;
    /** 发布状态 */
    releaseStatus?: string;
  }

  interface FirmwareReleaseConfig extends BaseEntity {
    /** 任务ID */
    jobId?: string;
    /** 固件数量 */
    packageCount?: string;
    /** 升级内容 */
    upgradeContent?: string;
    /** 固件开发状态 */
    developStatus?: string;
    /** 固件发布状态 */
    releaseStatus?: string;
  }

  interface FirmwareReleaseConfigPageParams extends FirmwareReleaseConfig, PageParams {
    productId: string;
  }

  interface FirmwareReleaseDetail {
    /** 任务ID */
    jobId?: string;
    /** 产品ID */
    productId?: string;
    /** 产品型号 */
    productModel?: string;
    /** 商品型号Model# */
    commodityModel?: string;
    /** 品类Id */
    categoryId?: string;
    /** 品类名称 */
    categoryName?: MultiLanguage;
    /** 品牌Id */
    brandId?: string;
    /** 品牌名称 */
    brandName?: MultiLanguage;
    /** 固件数量 */
    packageCount?: string;
    /** 升级说明 */
    releaseContent?: string;
    /** 多语言ID */
    langId?: string;
    /** 生产分组 */
    productGroupNames?: string[];
    /** 测试分组 */
    testGroupNames?: string[];
    /** 开始时间类型 */
    startType?: number | string;
    /** 开始时间时区 */
    startZone?: string;
    /** 开始日期 */
    startDate?: string;
    /** 结束时间类型 */
    endType?: number | string;
    /** 结束时间时区 */
    endZone?: string;
    /** 开始时间 */
    startTime?: number | string;
    /** 结束日期 */
    endDate?: string;
    /** 结束时间 */
    endTime?: number | string;
    /** 技术固件版本号 */
    technologyVersion?: string;
    /** 已发布固件版本 */
    customVersion?: string;
  }

  interface firmwareItem {
    /** 总成零件号 */
    componentNo?: string;
    /** 总成零件名称 */
    componentName?: string;
    /** 总成零件类型 */
    componentType?: string;
    /** 固件名称 */
    packageName?: string;
    /** 固件版本号 */
    packageVersion?: string;
    /** 文件体积 */
    size?: string;
    /** 最低兼容固件版本号 */
    minimumVersion?: string;
    /** 固件类型 */
    packageType?: string;
    key?: string;
    hash?: string;
    orderNum?: number;
  }

  interface UpgradeInfo {
    /** 总成零件号 */
    componentNo?: string;
    componentId?: string;
    /** 总成零件名称 */
    componentName?: string;
    /** 更新前固件版本 */
    oldVersion?: string;
    /** 更新后固件版本 */
    newVersion?: string;
    /** 升级结果 */
    status?: string;
    /** 升级结果时间 */
    resultTime?: string;
  }

  interface UpgradeResultItem {
    /** 设备ID */
    deviceId?: string;
    /** 设备名称 */
    deviceName?: string;
    /** 分组名称 */
    groupId?: string;
    groupName?: string;
    componentResults?: UpgradeInfo[];
  }

  interface UpgradeResultPageParams extends PageParams {
    /** 任务ID */
    jobId?: string;
    /** 设备ID */
    deviceId?: string;
    /** 分组名称 */
    groupName?: string;
    /** 升级结果时间 */
    resultTime?: string;
  }

  interface UpgradeResultSummary {
    /** 设备总数 */
    total?: string;
    /** 升级成功设备数量 */
    succeed?: string;
    /** 升级失败设备数量 */
    failed?: string;
  }

  /**
   * 配件相关
   */
  interface PartItem {
    /** 商品型号Model# */
    commodityModel?: string;
    /** 创建者 */
    createBy?: string;
    /** 创建时间 */
    createTime?: string;
    /** 图片类型 */
    iconType?: string;
    /** 配件图片 */
    iconUrl?: string;
    /** 配件名称 */
    name?: string;
    nameLangId?: string;
    /** 配件id */
    partsId?: string;
    instanceId?: string;
    /** 备注 */
    remark?: string;
    /** 配件类型1 */
    type1?: string;
    /** 配件类型2 */
    type2?: string;
    /** 更新者 */
    updateBy?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 上传的图片名 */
    uploadIconName?: string;
    maintenancePeriod?: number;
    categoryName?: number;
    parts?: PartItem;
    /** 总成零件号 */
    componentNo: string;
    maintenanceType?: number | string;
    maintenanceRemind?: number;
  }

  interface PartPageParams extends PageParams {
    productId?: string;
    /** 商品型号 */
    commodityModel?: string;
    createBy?: string;
    createEndTime?: string;
    createStartTime?: string;
    /** 配件名称 */
    name?: string;
    /** 配件id */
    partsId?: string;
    /** 配件类型1 */
    type1?: string;
    /** 配件类型2 */
    type2?: string;
    updateBy?: string;
    updateEndTime?: string;
    updateStartTime?: string;
  }

  interface PartsDetail {
    /** 商品型号Model# */
    commodityModel?: string;
    /** 配件描述 */
    description?: string;
    /** id */
    id?: number;
    /** 配件长描述 */
    longDesc?: MultiLanguage;
    /** 配件名称 */
    name?: MultiLanguage;
    /** 配件图标 */
    partsIcon?: string;
    /** 配件短描述 */
    shortDesc?: MultiLanguage;
    /** 技术规格 */
    technicalSpecifications?: MultiLanguage;
    /** 配件类型1，字典名称：partsType (通用配件:common_parts_type, 专用配件:special_parts_type, 电池包充电器：battery_pack_charger) */
    type1?: string;
    /** 配件类型2，字典名称：partsType (通用配件:common_parts_type, 专用配件:special_parts_type, 电池包充电器：battery_pack_charger) */
    type2?: string;
  }

  interface PartsPurchaseLink {
    /** 链接 */
    link?: {
      /** 列表显示-链接id */
      instanceId?: number;
      /** url */
      url?: string;
    };
    /** 记录id-用于后续接口入参 */
    partsLinkId?: string;
    operateItemId?: string;
    partsId?: string;
  }

  interface PartsUserManual {
    /** 文件描述多语言Id */
    description?: MultiLanguage;
    /** 文件格式 */
    fileFormat?: string;
    /** 文件链接 */
    fileLink?: string;
    /** 文件名称多语言Id */
    fileName?: MultiLanguage;
    /** 文件大小 */
    fileSize?: number;
    /** 文件类型：0文件上传，1文件链接 */
    fileType?: number;
    /** 用户手册Id */
    id?: string;
    /** 配件Id */
    partsId?: string;
    /** 缩略图链接 */
    thumbnailLink?: string;

    fileUrlKey?: string;

    file?: any;
  }

  interface ProtocolItem extends BaseApprove {
    /** 主协议id */
    appAgreementId?: string;
    /** 具体版本协议id */
    appAgreementContentId?: string;
    /** 协议名称 */
    title?: string;
    /** 协议名称多语言id */
    titleLangId?: string;
    /** 协议类型code */
    typeCode?: string;
    /** 协议版本 */
    version?: string;
    /** 发布状态code */
    statusCode?: string;
    /** 生产分组名称集合 */
    prdGroup?: string[];
    prdGroupList?: string;
    /** 测试分组名称集合 */
    testGroup?: string[];
    testGroupList?: string;
    /** 协议内容 */
    content?: string;
    /** 协议内容多语言id */
    contentLangId?: string;
    /** 协议内容 */
    contentInfo?: string;
    canUpdateTypeAndTitle?: boolean;
    /** 适用APP */
    businessType?: string;
  }

  interface ProtocolPageParams extends PageParams {
    applyBy?: string;
    statusCode?: string;
    title?: string;
    typeCode?: string;
    version?: string;
  }

  interface UserItem {
    /**
     * 唯一 uuid
     */
    id: string;
    /** 邮箱 */
    email?: string;
    /** 用户ID */
    userId?: string;
    /** APP在线状态 */
    appPresenceCode?: string;
    /** 最后登录时间 */
    lastLoginTime?: string;
    /** IP地址 */
    ip?: string;
    /** 手机型号 */
    phoneModel?: string;
    /** 系统版本号 */
    phoneOsVersion?: string;
    /** APP类型 */
    appTypeCode?: string;
    /** APP版本号 */
    appVersion?: string;
    /** 用户类型 */
    userTypeCode?: string;
    /** 用户来源 */
    userSourceCode?: string;
    /** 注册时间 */
    registerTime?: string;
  }

  interface UserPageParams extends UserItem, PageParams {}

  interface UserDeviceItem {
    /**
     * 唯一 uuid
     */
    id: string;
    /** 邮箱 */
    email?: string;
    /** 用户ID */
    userId?: string;
    /** 已绑定设备ID */
    bindDeviceId?: string;
    /** 已绑定设备SN */
    bindDeviceSn?: string;
    /** 已绑定设备品类 */
    bindDeviceCategoryId?: string;
    /** 已绑定设备品牌 */
    bindDeviceBrandId?: string;
    /** 已绑定设备产品型号 */
    model?: string;
    /** 已绑定设备商品型号/Model # */
    commodityModel?: string;
  }

  interface UserDevicePageParams extends UserDeviceItem, PageParams {}

  interface IntroductionItem {
    /** 文案 */
    content?: MultiLanguage;
    /** 引导图片 */
    icon?: string;
    /** 引导图片类型：0图片上传，1图片链接 */
    iconType?: string;
    /** 引导页ID */
    introductionId?: string;
    /** 引导页对应产品ID */
    productId?: string;
    /** 引导页类型：0扫码引导页，1配网引导页，2固件升级引导页 */
    type?: string;
    cardTitle?: string;
  }

  interface DeviceProtocolItem {
    /** 协议Id */
    agreementId?: string;
    /** 能否添加 */
    canAdd?: boolean;
    /** 能否添加新版本 */
    canAddNewVersion?: boolean;
    /** 能否删除 */
    canDelete?: boolean;
    /** 能否修改 */
    canUpdate?: boolean;
    released?: boolean;
    /** 协议内容多语言Id */
    content?: string;
    /** Id */
    id?: string;
    /** 协议名称 */
    name?: MultiLanguage;
    /** 产品Id */
    productId?: string;
    /** 协议类型 */
    type?: 'USAGE_AGREEMENT' | 'USER_PRIVACY_AGREEMENT';
    /** 协议版本号 */
    version?: string;

    groupNameList?: string[];
    readonly?: boolean;
  }

  interface DeviceProtocolPageParams extends PageParams {
    name: string;
    productId: string;
    type: string;
    version: string;
  }

  interface AppSortItem {
    /** 列表Code */
    itemCode?: string;
    elements?: ElementItem[];
    updateTime?: string;
    updateBy?: string;
  }

  interface ElementItem {
    /** 列表项Code */
    elementCode?: string;
    /** 列表项名称 */
    element?: string;
    /** 是否显示 */
    isShow?: number;
  }

  interface ErrorCodeItem extends BaseEntity {
    baseId: string;
    categoryCode: string;
    categoryName: string;
    modelCount: string;
  }

  interface ErrorCodePageParams extends PageParams {
    categoryCode: string;
    categoryId: string;
  }

  /**
   * 消息模板
   */
  interface MessageTemplateItem extends BaseEntity {
    id?: string;
    content: MultiLanguage;
    name: MultiLanguage;
    title: MultiLanguage;
    type: string;
    usedTimes: number;
    templateName?: string;
    templateTitle?: string;
    templateContent?: string;
    messageDisplayType?: string;
  }

  interface MessageTemplatePageParams extends PageParams {
    id?: string;
    type?: string;
    name?: string;
    title?: string;
    content?: string;
  }

  interface OperationGuidance {
    instanceId?: string;
    name?: string;
    nameLangId?: string;
    description?: string;
    descriptionLangId?: string;
    format?: string;
    size?: number;
    fileSize: ?string;
    url?: string;
    statusCode?: string;
    typeCode?: string;
    uploadFileName?: string;
  }

  interface GuideItem {
    commonOperationGuidanceId?: string;
    operationGuidance?: OperationGuidance;
    testGroup?: string[];
    testGroupList?: string;
  }

  interface GuideItemPageParams extends PageParams {
    name?: string;
    instanceId?: string;
  }

  interface Faq {
    instanceId: string;
    answer?: string;
    answerLangId?: string;
    title?: string;
    titleLangId?: string;
    typeCode?: string;
  }

  interface FaqItem {
    commonFaqId?: string;
    faq?: Faq;
    testGroup?: string[];
    testGroupList?: string;
  }

  interface FaqItemPageParams extends PageParams, BaseEntity, BaseApprove {
    faq?: Faq;
  }

  interface SaleFaq {
    /** 答案 */
    answer?: string;
    /** 答案多语言id */
    answerLangId?: string;
    /** faq id */
    faqId?: string;
    /** 题目 */
    title?: string;
    /** 题目多语言id */
    titleLangId?: string;
    /** 问题类型 */
    typeCode?: string;

    instanceId?: string;

    ifCommon?: boolean;

    productFaqId?: string;
  }

  interface SaleFaqDto {
    /** faq */
    faq?: SaleFaq;
    /** manualId、productOperationGuidanceId、productFaqId */
    operateItemId?: string;
    /** 产品id */
    productId?: string;
    id?: string;
  }

  interface SaleFaqPageParams extends PageParams {
    req?: string;
  }

  interface ManualItem {
    /** 文件描述 */
    description?: MultiLanguage;
    /** 文件描述多语言id，新增不必填 */
    descriptionLangId?: number;
    /** 文件格式 */
    format?: string;
    /** 文件名称 */
    name?: MultiLanguage;
    /** 文件名称多语言id，新增不必填 */
    nameLangId?: string;
    /** manualId、productOperationGuidanceId、productFaqId */
    operateItemId?: string;
    manualId?: string;
    /** 产品id */
    productId?: string;
    /** 文件大小，字节 */
    size?: number;
    /** 文件类型code */
    typeCode?: string;
    /** 上传的文件名 */
    uploadFileName?: string;
    /** 文件地址 */
    url?: string;

    [key: string]: any;
  }

  interface AppMessageItem extends BaseEntity {
    /** 系统消息id */
    sysMsgId?: string;
    marketingMsgId?: string;
    /** 父消息id */
    fromId?: string;
    /** 消息标题 */
    title?: string;
    titleLangId?: string;
    /** 消息内容 */
    content?: string;
    contentLangId?: string;
    /** 发布状态code */
    statusCode?: string;
    /** 推送开始时区 */
    startZone?: string;
    /** 推送开始类型code */
    startType?: number | string;
    /** 推送开始时间(列表用) */
    start?: string;
    /** 推送开始时间(表单用) */
    startTime?: string;
    /** 推送开始日期 */
    startDate?: string;
    /** 推送结束时区 */
    endZone?: string;
    /** 推送结束类型code */
    endType?: number | string;
    /** 推送结束时间(列表用) */
    end?: string;
    /** 推送结束时间(表单用) */
    endTime?: string;
    /** 推送开始日期 */
    endDate?: string;
    /** 生产分组名称集合 */
    prdGroup?: string[];
    /** 测试分组名称集合 */
    testGroup?: string[];
    /** 路由地址 */
    rutePath?: string;
    /** 推送频率code */
    pushRateCode?: string;
    /** 推送类型code */
    pushTypeCodes?: string[];
  }

  interface AppMessagePageParams extends PageParams {
    pushTypeCode?: string[];
  }

  interface AppMessageResultItem {
    /** 推送类型code */
    pushTypeCode?: string;
    pushTime?: number;
    userId?: string;
    resultCode?: string;
  }

  interface AppMessageResultPageParams extends PageParams {
    pushType?: string;
    userId?: string;
    resultCode?: string;
    systemMessageId?: string;
  }

  /**
   * 产品管理配置相关
   */
  interface EmployeePageParams extends PageParams {
    employeeNumber?: string;
    userName?: string;
  }

  interface SysUserItem {
    sysUserId?: string;
    sysUserEmployeeNumber?: string;
    sysUsername?: string;
    sysUserGuid?: string;
  }

  interface SysUserPageParams extends PageParams {
    req?: string;
  }

  /**
   * 经销商
   */
  interface DealerItem extends BaseEntity {
    /** 经销商ID */
    dealerId?: string;
    /** 语种 */
    language?: Language;
    /** 经销商名称 */
    name?: string;
    nameLangId?: string;
    /** 所在国家 */
    country?: string;
    /** 所在省份 */
    state?: string;
    /** 所在市区 */
    city?: string;
    /** 经销商地址 */
    address?: string;
    addressLangId?: string;
    /** 邮政编码 */
    zipcode?: string;
    /** 电话 */
    telephone?: string;
    /** 邮箱 */
    email?: string;
    /** 网址 */
    website?: string;
    /** 工作时长 */
    hours?: string | number;
    /** lat */
    lat?: string;
    /** lng */
    lng?: string;
    /** 类别 */
    category?: string[];
    /** 地图缩放等级 */
    zoomLevel?: string | number;
    /** 颜色 */
    color?: string;
    /** 激活状态 */
    isActive?: string;
    /** 图片 */
    image?: string;
    /** 存储定位器 */
    storeLocatorId?: string;
  }
  type Language = 'en' | 'fr';

  interface DealerPageParams extends PageParams {
    dealerId?: string;
    name?: string;
    telephone?: string;
    email?: string;
    lat?: string;
    lng?: string;
    category?: string[];
    country?: string;
    state?: string;
    city?: string;
    address?: string;
  }

  interface Answer {
    message: string;
  }
  interface AppQuestionItem extends BaseEntity {
    helpFaqId?: string;
    /** 答案 */
    answer?: Answer;
    /** 答案多语言id */
    answerLangId?: number;
    /** 列表显示-问题id */
    instanceId?: number;
    /** 题目 */
    title?: string;
    /** 题目多语言id */
    titleLangId?: number;
    /** 问题类型 */
    typeCode?: string;

    /** app显示状态 */
    appShowCode?: string;
    /** 商品型号 */
    model?: string[] | string;
    /** 点赞量 */
    praiseCount?: number;
    /** 阅读量 */
    readCount?: number;
    /** 数据来源code */
    sourceCode?: string;
    /** 同步时间 */
    syncTime?: string;
    /** 标题 */
  }

  interface AppQuestionPageParams extends PageParams {
    req: string;
    createBy?: string;
    createTime?: string;
    updateTime?: string;
    createEndTime?: string;
    createStartTime?: string;
    /** 问题id */
    faqId?: string;
    partsId?: string;
    instanceId?: string;
    orderBy?: number;
    orderType?: number;
    pageNum: number;
    pageSize: number;
    /** 问题 */
    title?: string;
    updateBy?: string;
    updateEndTime?: string;
    updateStartTime?: string;
  }

  interface AppUpgradeItem extends BaseEntity {
    /** 安卓版本id */
    androidVersionId?: string;
    /** 版本名称 */
    name?: MultiLanguage;
    /** 更新内容 */
    updateContent?: MultiLanguage;
    /** 更新内容多语言id */
    updateContentLangId?: string;
    /** 版本号 */
    version?: string;
    /** APP名称 */
    businessType?: string;
  }

  interface AppUpgradePageParams extends PageParams {
    /** 安卓版本id */
    androidVersionId?: string;
    createBy?: string;
    createEndTime?: string;
    createStartTime?: string;
    /** 版本名称 */
    name?: string;
    orderBy?: number;
    orderType?: number;
    pageNum: number;
    pageSize: number;
    updateBy?: string;
    updateEndTime?: string;
    updateStartTime?: string;
    /** 版本号 */
    version?: string;
  }

  interface SuggestionPageParams extends PageParams {
    createEndTime?: string;
    createStartTime?: string;
    /** 建议ID */
    suggestionId?: string;
    /** 标题 */
    title?: string;
    updateEndTime?: string;
    updateStartTime?: string;
    pageNum?: number;
  }

  interface SuggestionItem extends BaseEntity {
    /** 建议 */
    content?: MultiLanguage;
    /** 附加内容 */
    extra?: string;
    /** 消息名称 */
    msg?: string[];
    /** 建议ID */
    suggestionId?: string;
    /** 标题 */
    title?: MultiLanguage;
  }

  interface SuggestionReq extends BaseEntity {
    /** 建议 */
    content?: string;
    /** 附加内容 */
    extra?: string;
    /** 建议ID */
    suggestionId?: string;
    /** 标题 */
    title?: string;
  }

  interface MessageRecordItem extends PageParams {
    msgId?: string;
    title?: string;
    titleLangId?: string;
    content?: string;
    contentLangId?: string;
    messageType?: number;
    productId?: string;
    pushAllNum?: string;
    pushSuccessNum?: string;
    pushFailNum?: string;
  }

  interface MessageRecordPageParams extends PageParams {
    msgId?: string;
    systemMessageId?: string;
    pushTypeCode?: string;
    messageType?: number;
    title?: string;
    productId?: string;
    uuid?: string;
  }

  interface FeeSms {
    chargingId?: string;
    msgId?: string;
    msgTitle?: string;
    chargingStartTime?: string;
    chargingEndTime?: string;
    msgType?: string;
    commodityModel?: string;
    totalSend?: number;
    createTime?: string;
    msgRecordId?: string;
    msgContent?: string;
    pushType?: string;
    toUserId?: string;
    toUserPhoneNumber?: string;
    toUserRegion?: string;
    pushTime?: string;
  }
  interface FeeSmsPageParams extends PageParams {
    chargingId?: string;
    chargingStartTime?: string;
    chargingEndTime?: string;
  }

  interface FeeFLowItem {
    chargingId?: string;
    chargingStartTime?: string;
    chargingEndTime?: string;
    commodityModel?: string;
    deviceCount?: number;
    createTime?: string;
    totalPriceCny?: string;
    totalPriceUsd?: string;
    deviceId?: string;
    iccid?: string;
  }
  interface FeeFlowPageParams extends PageParams {
    chargingId?: string;
    chargingStartTime?: string;
    chargingEndTime?: string;
  }

  /**
   * 设备
   */
  interface DeviceItem {
    id?: string;
    /*  设备昵称 */
    nickName: string;
    /* 在线状态 */
    isOnline: number;
    deviceId: string;
    categoryName: string;
    brandName: string;
    configurationTable: string;
    /* 商品型号/Model # */
    commodityModel: string;
    /* 产品类型 */
    productType: string;
    sn: string;
    pid: string;
    /* 设备状态 */
    status: string;
    /* 使用状态 */
    usageStatus: number;
    activationUserId: number;
    /* 所属产品SnCode */
    productSnCode: string;
    /* 通讯方式 */
    communicateMode: number;
    /* 设备激活时间，设备注册的时候设置设备为激活状态 */
    activationTime: string;
    /* 设备最后一次上线时间 */
    lastLoginTime: string;
    /* 产品型号 */
    productModel: string;
    boundUserIds: string[];
    sensitiveBoundUserIds?: string[];
    /* 注册用户ID */
    infoRegisteredBy?: string;
    /* 注册时间 */
    registerTime?: string;
    /* 已发布固件版本 */
    customVersion?: string;
    /* 技术固件版本号 */
    technologyVersion?: string;
    /* 当前绑定APP */
    currentBindBusinessType: string;
    /* 当前绑定EGO用户ID */
    currentBindEgoUserId?: string[];
    /* 当前绑定Fleet租户ID */
    currentBindFleetCompanyId?: string;
    /* 首次绑定APP */
    firstBindBusinessType: string;
    /* 首次绑定EGO用户ID */
    firstBindEgoUserId?: string;
    /* 首次绑定Fleet租户ID */
    firstBindFleetCompanyId?: string;
    /* 首次绑定Fleet操作帐户ID */
    firstBindFleetUserId?: string;
    /* 首次绑定时间 */
    firstBindTime?: string;
  }
  interface DevicePageParams extends PageParams {
    deviceName?: string;
    productSnCode?: string;
    activationTime?: string;
    lastLoginTime?: string;
    activationStartTime?: string;
    activationEndTime?: string;
    lastLoginStartTime?: string;
    lastLoginEndTime?: string;
    firstBindTime?: string;
    firstBindStartTime?: string;
    firstBindEndTime?: string;
  }

  /**
   * 固件升级记录
   */
  interface FirmwareUpgradeItem {
    id: string;
    oldVersion: string;
    newVersion: string;
    result: number;
    userId: string;
    upgradeTime: number;
  }

  /**
   * 设备升级记录
   */
  interface DeviceUpgradeItem {
    jobId: string;
    oldVersion: string;
    newVersion: string;
    status: string;
    detail: number;
    userId: string;
    upgradeTime: number;
  }

  interface DeviceUpgradePageParams extends PageParams {
    deviceId: string;
  }

  interface DevicePartPageParams extends PageParams {
    deviceId?: string;
  }

  interface UpgradePageParams extends PageParams {
    componentNo?: string;
    deviceId?: string;
    upgradeTime?: number;
  }

  /**
   * 租户列表
   */
  interface CompanyItem {
    companyId: string;
    companyName: string;
    adminId: string;
    adminEmail: string;
    adminFirstName: string;
    adminLastName: string;
    companyState: string;
    registerTime: string;
  }

  interface CompanyPageParams extends PageParams {
    companyId?: string;
    companyName?: string;
    adminId?: string;
    adminEmail?: string;
    companyState?: string;
    /* 注册开始时间 */
    registerStartTime: string;
    /* 注册结束时间 */
    registerEndTime: string;
  }

  /**
   * 管理员变更记录
   */
  interface adminChangeRecord {
    companyId: string;
    adminId: string;
    adminEmail: string;
    adminFirstName: string;
    adminLastName: string;
    changeTime: string;
  }
  interface adminChangeSearchParams extends PageParams {
    companyId?: string;
  }

  /**
   * 租户与设备关系
   */
  interface CompanyAndDeviceItem {
    companyId: string;
    companyName: string;
    count: string;
    deviceId: string;
    deviceSn: string;
    brandId: string;
    categoryId: string;
    model: string;
  }

  interface CompanyAndDevicePageParams extends PageParams {
    companyId?: string;
    companyName?: string;
    deviceId?: string;
    deviceSn?: string;
    brandId?: string;
    categoryId?: string;
    model?: string;
  }

  /**
   * 租户员工列表
   */
  interface CompanyEmployeeItem {
    userId: string;
    userEmail: string;
    userFirstName: string;
    userLastName: string;
    userRole: string;
    companyId: string;
    companyName: string;
    userSourceType: string;
    invitationEmail: string;
    userState: string;
    activeTime: string;
    registerTime: string;
  }
  interface CompanyEmployeePageParams extends PageParams {
    userId?: string;
    userEmail?: string;
    userRole?: string;
    companyId?: string;
    companyName?: string;
    userSourceType?: string;
    invitationEmail?: string;
    userState?: string;
    registerStartTime?: string;
    registerEndTime?: string;
    activeStartTime?: string;
    activeEndTime?: string;
  }

  /**
   * 欧洲经销商
   */
  interface EuropeDealerItem {
    dealerId: string;
    countryCode: string;
    title: string;
    addressOne: string;
    addressTwo: string;
    town: string;
    city: string;
    region: string;
    postcode: string;
    phoneNumber: string;
    email: string;
    geolocation: string;
    langCode: string;
    premium: string;
    proXRangeSeller: string;
    rideOnSeller: string;
    ad: string;
  }
  interface EuropeDealerPageParams extends PageParams {
    dealerId?: string;
    countryCode?: string;
    title?: string;
    town?: string;
    city?: string;
    region?: string;
    postcode?: string;
    phoneNumber?: string;
    email?: string;
  }
}
