import { getLocale, setLocale } from '@@/plugin-locale/localeExports';
import { RequestConfig, ErrorShowType } from '@@/plugin-request/request';
import type { Settings as LayoutSettings } from '@ant-design/pro-layout';
import { PageLoading } from '@ant-design/pro-layout';
import { first, get, omit, replace, split, merge, isString, trim } from 'lodash-es';
import type { RunTimeLayoutConfig } from 'umi';
import type { RequestOptionsInit } from 'umi-request';
import { Context } from 'umi-request';
import defaultSettings from '../config/defaultSettings';
import { getMultiLanguages, getPatchMenus, getCode } from '@/services/common';
import { addLocale, getIntl, history } from 'umi';
import zhCN from 'antd/es/locale/zh_CN';
import enUS from 'antd/es/locale//en_US';
import { createIframe } from '@/utils/iframe';
/** 获取用户信息比较慢的时候会展示一个 loading */
export const initialStateConfig = {
  loading: <PageLoading />,
};

async function initData() {
  // 获取本地语言包，避免等待语言包拉取时，页面词条显示为多语言CODE
  const lang = getLocale();
  const localeType = ROOT_PATH.split('/')[1] + '-' + lang;
  const locales = localStorage.getItem(localeType);
  if (locales) {
    addLocale(lang, JSON.parse(locales), {
      momentLocale: lang,
      antd: (lang === 'zh-CN' ? zhCN : enUS) as any,
    });
  } else {
    // 首次加载，获取语言包
    getMultiLanguages('operation-platform').then((res) => {
      if (res.data) {
        addLocale(lang, res.data, {
          momentLocale: lang,
          antd: (lang === 'zh-CN' ? zhCN : enUS) as any,
        });
        localStorage.setItem(localeType, JSON.stringify(res.data));
      }
    });
  }
  // 公共多语言词条
  const localeCommon = 'webCommon-' + lang;
  const localeItems = localStorage.getItem(localeCommon);
  if (localeItems) {
    addLocale(lang, JSON.parse(localeItems), {
      momentLocale: lang,
      antd: (lang === 'zh-CN' ? zhCN : enUS) as any,
    });
  } else {
    // 首次加载，获取语言包
    getMultiLanguages('web-common').then((res) => {
      if (res.data) {
        addLocale(lang, res.data, {
          momentLocale: lang,
          antd: (lang === 'zh-CN' ? zhCN : enUS) as any,
        });
        localStorage.setItem(localeCommon, JSON.stringify(res.data));
      }
    });
  }
  return getCode(await getPatchMenus());
}
/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: API.CurrentUser;
  accessSet?: Record<string, boolean>;
  loading?: boolean;
  isLogined?: boolean;
  fetchUserInfo?: () => Promise<API.CurrentUser | undefined>;
}> {
  // const fetchUserInfo = async () => {
  //   try {
  //     const msg = await queryCurrentUser();
  //     return msg.data;
  //   } catch (error) {
  //     history.push(loginPath);
  //   }
  //   return undefined;
  // };
  // // 如果不是登录页面，执行
  // if (history.location.pathname !== loginPath) {
  //   const currentUser = await fetchUserInfo();
  //   return {
  //     fetchUserInfo,
  //     currentUser,
  //     settings: defaultSettings,
  //   };
  // }
  let isLogined = false;
  if (history.location.pathname === '/login') {
    return {
      isLogined: true,
      settings: defaultSettings,
    };
  }
  const accessSet = await initData();
  isLogined = true;
  return {
    // fetchUserInfo,
    accessSet,
    settings: defaultSettings,
    isLogined,
  };
}
let isAuth = true;
// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  return {
    ErrorComponent: null,
    disableContentMargin: false,
    locale: 'zh-CN',
    waterMarkProps: {
      content: initialState?.currentUser?.name,
    },
    onPageChange: (location) => {
      // 如果没有登录，重定向到 login
      // if (!initialState?.currentUser && location.pathname !== loginPath) {
      //   history.push(loginPath);
      // }
      // if (
      //   !initialState &&
      //   history.location.pathname !== '/login' &&
      //   localStorage.getItem(ROOT_PATH.replace(/\//g, '') + '-token')
      // ) {
      //   setInitialState((preInitialState) => ({
      //     ...preInitialState,
      //     isLogined: true,
      //     settings: defaultSettings,
      //   }));
      //   setTimeout(async () => {
      //     const res = await initData();
      //     window[ROOT_PATH.replace(/\//g, '') + '_logined'] = true;
      //     setInitialState((preInitialState) => ({
      //       ...preInitialState,
      //       accessSet: res,
      //     }));
      //   }, 0);
      // }
    },
    // 不展示顶栏
    headerRender: false,
    // 展示页脚
    footerRender: false,
    // 不展示菜单
    menuRender: false,
    // 不展示菜单顶栏
    menuHeaderRender: false,
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    // 增加一个 loading 的状态
    childrenRender: (children, props) => {
      // if (initialState?.loading) return <PageLoading />;
      if (initialState?.isLogined || history.location.pathname === '/login') {
        return <>{children}</>;
      } else {
        return <PageLoading />;
      }
    },
    ...initialState?.settings,
  };
};
export const qiankun = {
  // 应用加载之前
  async bootstrap(props: unknown) {
    // console.log('auth app bootstrap', props);
  },
  // 应用 render 之前触发
  async mount(props: any) {
    // console.log('auth app mount', props);
    const { onGlobalStateChange, router } = props;
    isAuth = true;
    window.router = router;
    // sessionStorage.setItem('token', getAppToken?.());
    onGlobalStateChange?.((state: any) => {
      const mainLang = replace(state.locale, '_', '-');
      const lang = getLocale();
      setLocale(lang === mainLang ? lang : mainLang, false);
    }, true);
  },
  // 应用卸载之后触发
  async unmount(props: unknown) {
    console.log('operation app unmount', props);
  },
};

/**
 * request 配置
 */
const authInterceptor = (url: string, options: RequestOptionsInit) => {
  const token = localStorage.getItem(ROOT_PATH.replace(/\//g, '') + '-token');
  const authHeader = {
    Authorization: `Bearer ${token}`,
    lang: first(split(getLocale(), '-')) || 'zh',
  };
  return {
    url,
    options: { ...options, interceptors: true, headers: authHeader },
  };
};

const pageInterceptor = (url: string, options: RequestOptionsInit) => {
  const { data, type } = options;
  const timezoneOffset = -new Date().getTimezoneOffset() / 60;
  // 将 current 转换为 pageNum, API 支持 pageNum
  return {
    url,
    options:
      type === 'page' || type === 'entry'
        ? {
            ...omit(options, ['data']),
            data: {
              ...omit(
                JSON.parse(JSON.stringify(data || {}), (k, v) => {
                  if (isString(v)) return trim(v);
                  return v;
                }),
                ['current'],
              ),
              pageNum: data?.current || 1,
              zone: timezoneOffset,
            },
          }
        : options,
  };
};

const exportInterceptor = (url: string, options: RequestOptionsInit) => {
  const { data, type } = options;
  const timezoneOffset = -new Date().getTimezoneOffset() / 60;
  // 删除参数空格
  return {
    url,
    options:
      type === 'string'
        ? {
            ...omit(options, ['data']),
            data: JSON.parse(
              JSON.stringify({ ...data, ...{ zone: timezoneOffset } } || {}),
              (k, v) => {
                if (isString(v)) return trim(v);
                return v;
              },
            ),
          }
        : options,
  };
};

const statusInterceptor = async (response: Response, options: RequestOptionsInit) => {
  try {
    const res = await response.clone().json();
    if (parseInt(res.responseCode, 10) === 401 && isAuth) {
      isAuth = false;
      if (window['__POWERED_BY_QIANKUN__']) {
        createIframe();
      } else {
        history.push({
          pathname: '/login',
          query: { back: encodeURIComponent(location.href) },
        });
      }
    }
  } catch (e) {
    // console.log(e);
  }
  return response;
};

const dataInterceptor = async (response: Response, options: RequestOptionsInit) => {
  const { type, showType } = options || {};

  if (type === 'string') return response;
  try {
    const res = await response.clone().json();
    const data = res.entry;
    switch (type) {
      case 'page': {
        res.data = get(data, ['list']);
        res.pageNo = get(data, ['pageNum']);
        res.pageSize = get(data, ['pageSize']);
        res.total = get(data, ['total']);
        break;
      }
      case 'all': {
        const len = (res.entry || []).length;
        res.data = res.entry;
        res.pageSize = len;
        res.total = len;
        break;
      }
      default:
        res.data = res.entry;
    }

    res.success = res.status;
    res.errorMessage = res.message;

    if (!res.success && showType !== undefined) res.showType = showType;

    delete res.entry;
    return res;
  } catch (e) {
    return response;
  }
};

export const request: RequestConfig = {
  prefix: '/api',
  timeout: 600000,
  errorConfig: {
    adaptor: (res: any, ctx: Context) => {
      // 如果force == true, 返回所有信息
      const { showType, force } = get(ctx, ['req', 'options']);
      // 后端接口不可用的错误信息
      if (ctx.res.status >= 400) {
        return merge(res, {
          showType,
          errorMessage: getIntl().formatMessage({ id: 'webCommon_page_apiError_toast_text' }),
        });
      }
      // 未登录隐藏后端报错信息（IOT-7688）
      if (ctx.res.responseCode === '401') {
        return merge(res, {
          showType: ErrorShowType.SILENT,
          errorMessage: force ? JSON.stringify(res) : get(res, 'errorMessage'),
        });
      }
      return merge(res, {
        showType,
        errorMessage: force ? JSON.stringify(res) : get(res, 'errorMessage'),
      });
    },
  },
  requestInterceptors: [authInterceptor, pageInterceptor, exportInterceptor],
  responseInterceptors: [statusInterceptor, dataInterceptor],
};
