import React from 'react';
import { KeepAlive, history } from 'umi';

const Index: React.FC = ({ children }) => {
  const KeepLive: any = KeepAlive;
  return (
    <KeepLive name={'/' + ROOT_PATH.replace(/\//g, '') + history.location.pathname}>
      {children}
    </KeepLive>
  );
};

export default Index;

export const createKAC = (Comp: React.FC<any>) => () =>
  (
    <Index>
      <Comp />
    </Index>
  );
