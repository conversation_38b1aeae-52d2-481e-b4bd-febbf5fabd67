import { Access } from '@@/plugin-access/access';
import { FormattedMessage } from '@@/plugin-locale/localeExports';
import { ProFormInstance } from '@ant-design/pro-components';
import { ProForm } from '@ant-design/pro-form';
import { Button, ButtonProps, Modal } from 'antd';
import React, { ReactNode, useRef, useState } from 'react';
import { useAccess } from 'umi';

interface AccessProps<T> {
  text: string;
  code: string | string[];
  onClick?: (values: T) => Promise<void> | void;
  icon?: ReactNode;
  modal?: { title?: string; content: ReactNode; footer?: ReactNode; width?: string };
  type?: 'button' | 'a';
  buttonProps?: ButtonProps;
}

/**
 * @param onClick 点击事件
 * @param text 按钮文字
 * @param code 授权标识, 值为 '*' 可跳过鉴权
 * @param icon 按钮图标, 仅按钮情况有效
 * @param modal Modal 配置, 用于处理点击 按钮/链接 需要二次确认的情况
 * @param type 渲染类型（一般不传）
 * @param buttonProps
 */
export function AccessButton<T>({
  onClick,
  text,
  code,
  icon = null,
  modal,
  type = 'button',
  buttonProps = {},
}: React.PropsWithChildren<AccessProps<T>>) {
  const access = useAccess();
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const formRef = useRef<ProFormInstance>();

  const handleClick = async () => {
    if (!modal) {
      try {
        setLoading(true);
        await onClick?.({} as any);
      } finally {
        setLoading(false);
      }
    } else {
      setVisible(true);
    }
  };

  const onFinish = async (values: T) => {
    setLoading(true);
    try {
      await onClick?.(values);
      setVisible(false);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Access accessible={access.checkAuth(code)}>
        {type === 'button' ? (
          <Button {...buttonProps} onClick={handleClick}>
            {icon}
            <FormattedMessage id={text} />
          </Button>
        ) : (
          <a onClick={handleClick}>
            <FormattedMessage id={text} />
          </a>
        )}
      </Access>
      {modal ? (
        <Modal
          title={<FormattedMessage id={modal.title || text} />}
          open={visible}
          onOk={() => formRef.current?.submit()}
          onCancel={() => setVisible(false)}
          confirmLoading={loading}
          destroyOnClose
          width={modal.width}
          footer={
            modal.footer === null ? (
              <Button style={{ width: '88px' }} onClick={() => setVisible(false)}>
                <FormattedMessage id="webCommon_page_cancel_button_text" />
              </Button>
            ) : (
              modal.footer
            )
          }
        >
          <ProForm
            onFinish={onFinish}
            formRef={formRef}
            labelCol={{ flex: '120px' }}
            layout="horizontal"
            submitter={false}
          >
            {modal.content}
          </ProForm>
        </Modal>
      ) : null}
    </>
  );
}

export function AccessLink<T>(props: React.PropsWithChildren<AccessProps<T>>) {
  return <AccessButton<T> {...props} type="a" />;
}
