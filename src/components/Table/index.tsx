import { useIntl } from '@@/plugin-locale/localeExports';
import { ProTable, ProTableProps } from '@ant-design/pro-table';
import useARH from '@minko-fe/use-antd-resizable-header';
import '@minko-fe/use-antd-resizable-header/dist/style.css';
import { useMemo } from 'react';
import { ParamsType } from '@ant-design/pro-components';
import { findLastIndex, get, omit } from 'lodash-es';
import dayjs from 'dayjs';
export const beforeSearchSubmit = (params: { [key: string]: any }) => {
  return {
    // 时间筛选为区间值，在此做统一转化
    ...omit(params, [
      'createTimeFilter', // 创建时间
      'updateTimeFilter', // 更新时间
      'applyTimeFilter', // 申请时间
      'approvedTimeFilter', // 审批时间
      'pushStartTime', // 消息推送开始时间
      'pushEndTime', // 消息推送结束时间
      'productDate', // 产品多码管理Production Date,
      'createByFilter', // 创建人
      'updateByFilter', // 修改人
      'applyByFilter', // 申请人
      'approveByFilter', // 审批人
      'registerTimeFilter', // 租户注册时间
      'activeTimeFilter', // 租户员工激活时间
    ]),
    registerStartTime: get(params, ['registerTimeFilter', 0])
      ? dayjs(get(params, ['registerTimeFilter', 0])).valueOf()
      : undefined,
    registerEndTime: get(params, ['registerTimeFilter', 1])
      ? dayjs(get(params, ['registerTimeFilter', 1]))
          .add(1, 'day')
          .valueOf()
      : undefined,
    activeStartTime: get(params, ['activeTimeFilter', 0])
      ? dayjs(get(params, ['activeTimeFilter', 0])).valueOf()
      : undefined,
    activeEndTime: get(params, ['activeTimeFilter', 1])
      ? dayjs(get(params, ['activeTimeFilter', 1]))
          .add(1, 'day')
          .valueOf()
      : undefined,
    createStartTime: get(params, ['createTimeFilter', 0]),
    createEndTime: get(params, ['createTimeFilter', 1]),
    updateStartTime: get(params, ['updateTimeFilter', 0]),
    updateEndTime: get(params, ['updateTimeFilter', 1]),
    applyStartTime: get(params, ['applyTimeFilter', 0]),
    applyEndTime: get(params, ['applyTimeFilter', 1]),
    approveStartTime: get(params, ['approvedTimeFilter', 0]),
    approveEndTime: get(params, ['approvedTimeFilter', 1]),
    pushStartMinTime: get(params, ['pushStartTime', 0]),
    pushStartMaxTime: get(params, ['pushStartTime', 1]),
    pushEndMinTime: get(params, ['pushEndTime', 0]),
    pushEndMaxTime: get(params, ['pushEndTime', 1]),
    productionStartDate: get(params, ['productDate', 0]),
    productionEndDate: get(params, ['productDate', 1]),
    createBy: params.createByFilter ?? undefined,
    updateBy: params.updateByFilter ?? undefined,
    applyBy: params.applyByFilter ?? undefined,
    approveBy: params.approveByFilter ?? undefined,
  };
};

const ResizableTable = <T extends Record<string, any>, U extends ParamsType>(
  props: ProTableProps<T, U>,
) => {
  const intl = useIntl();
  const {
    columns,
    pagination = {
      showSizeChanger: true,
      showQuickJumper: true,
      defaultPageSize: 20,
      pageSizeOptions: ['10', '20', '50'],
      showTotal: (total) =>
        `${intl.formatMessage({
          id: 'webCommon_page_prefix_pagination_label',
        })}${total}${intl.formatMessage({
          id: 'webCommon_page_suffix_pagination_label',
        })}`,
    },
    ...rest
  } = props;

  const columnsWithAuto = useMemo(() => {
    if (columns) {
      // 避免修改原 columns
      const temp = [...columns];
      const lastIdx = findLastIndex(temp, (c) => c.fixed === undefined);

      if (lastIdx !== -1) {
        temp[lastIdx] = {
          ...get(columns, [lastIdx]),
          width: 'auto',
        };
      }
      return temp;
    }

    return columns;
  }, [columns]);

  const { components, resizableColumns, tableWidth } = useARH<any>({
    columns: columnsWithAuto,
  });

  return (
    <ProTable<T, U>
      beforeSearchSubmit={(params) => {
        return beforeSearchSubmit(params);
      }}
      {...rest}
      search={
        rest.search === false
          ? false
          : {
              ...rest.search,
              searchText: intl.formatMessage({
                id: 'webCommon_page_search_button_text',
              }),
              resetText: intl.formatMessage({
                id: 'webCommon_page_reset_button_text',
              }),
              // collapseRender: (collapsed: boolean) =>
              //   collapsed
              //     ? intl.formatMessage({
              //         id: 'webCommon_page_expand_button_text',
              //       })
              //     : intl.formatMessage({
              //         id: 'webCommon_page_collapse_button_text',
              //       }),
            }
      }
      revalidateOnFocus={false}
      pagination={pagination}
      columns={resizableColumns}
      scroll={rest.scroll ? { x: tableWidth } : undefined}
      components={components}
    />
  );
};

export default ResizableTable;
