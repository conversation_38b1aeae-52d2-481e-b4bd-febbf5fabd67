import { Modal, message } from 'antd';
import React, { useImperativeHandle, useState, ReactNode } from 'react';
import { FormattedMessage, useIntl } from 'umi';
import { ProFormInstance } from '@ant-design/pro-components';
import { useRequest } from '@@/plugin-request/request';
import type { ActionType } from '@ant-design/pro-components';
import { has } from 'lodash-es';

export interface ModalProps {
  parentRef?: React.MutableRefObject<ActionType | undefined>;
  formRef?: React.MutableRefObject<ProFormInstance | undefined>;
}

interface ModalContent {
  title: string;
  msg: ReactNode;
  value?: string | number;
  onOk?: APP.FnType;
}
export interface RefType {
  open: (content: ModalContent) => void;
  setProps: (settings: { [key: string]: any }) => void;
}

const Index = React.forwardRef<RefType, React.PropsWithChildren<ModalProps>>((props, ref) => {
  const { parentRef, formRef } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const [tips, setTips] = useState<ReactNode>('');
  const [submit, setSubmit] = useState<APP.FnType>();
  const [settings, setSettings] = useState({});
  const [value, setValue] = useState<string | number>();
  const [titleCode, setTitleCode] = useState<string>('');
  const intl = useIntl();

  const { run: handleSubmit, loading: submitLoading } = useRequest(
    async () => {
      // 父组件传递了Form表单对象
      if (formRef) {
        // 仅用于提交前的校验（弹框存在实际Form表单）
        if (formRef.current) {
          formRef.current.submit();
          const validateValues = await formRef.current.validateFieldsReturnFormatValue?.();
          // 验证通过后，在此提交数据
          if (!has(validateValues, 'outOfDate')) {
            await submit?.(validateValues);
          }
        } else {
          // 弹框实际无Form表单，执行确定按钮事件
          await submit?.(value);
        }
      } else {
        // 父组件未传递Form表单对象，执行确定按钮事件
        await submit?.(value);
      }
      setVisible(false);
      message.success(intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }));
      // 提交成功后，父组件（比如table列表）重新加载数据
      parentRef?.current?.reload();
    },
    { manual: true },
  );

  useImperativeHandle(ref, () => ({
    open: (content: ModalContent) => {
      setTitleCode(content.title);
      setTips(content.msg);
      setValue(content.value);
      setSubmit(content.onOk);
      setVisible(true);
    },
    setProps: (propAttr) => {
      setSettings(propAttr);
    },
  }));

  const onCancel = () => {
    setVisible(false);
  };

  return (
    <>
      <Modal
        title={<FormattedMessage id={titleCode} />}
        onCancel={onCancel}
        onOk={handleSubmit}
        open={visible}
        destroyOnClose={true}
        confirmLoading={submitLoading}
        {...settings}
      >
        {tips}
      </Modal>
    </>
  );
});

export default Index;
