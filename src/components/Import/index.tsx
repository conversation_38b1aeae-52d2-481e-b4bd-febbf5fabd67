import { ImportOutlined } from '@ant-design/icons';
import { useRequest } from '@@/plugin-request/request';
import { ProForm, ProFormInstance } from '@ant-design/pro-components';
import { Button, Modal, message } from 'antd';
import React, { useState, useRef, ReactNode } from 'react';
import { FormattedMessage, useIntl, request, useAccess } from 'umi';
import { Access } from '@@/plugin-access/access';
import ImportError, { RefType } from './ImportError';
import { downloadByData } from '@/utils/download';
import { FileUpload } from '@/components/Form/FileUpload';
interface ImportProps {
  pageName: string;
  url: string;
  urlParam: string;
  getTemplate: () => Promise<any>;
  refresh?: () => Promise<void> | undefined;
  data?: { [key: string]: any } | undefined;
  buttonName?: string;
  formItems?: ReactNode;
  hideIcon?: boolean;
  hideConfirmModal?: () => any;
}

const Index = React.forwardRef<APP.RefType, ImportProps>((props, ref) => {
  const {
    refresh,
    url,
    urlParam,
    pageName,
    getTemplate,
    data,
    buttonName,
    formItems,
    hideIcon,
    hideConfirmModal,
  } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const intl = useIntl();
  const errorRef = useRef<RefType>(null);
  const formRef = useRef<ProFormInstance>();
  const access = useAccess();

  const { run: handleSubmit, loading: submitting } = useRequest(
    async (values) => {
      const params = new FormData();
      params.append(urlParam, values[urlParam]);
      if (data) {
        Object.keys(data).forEach((key) => {
          params.append(key, data[key]);
        });
      }
      console.log(params);
      const res = await request(url, {
        method: 'POST',
        data: params,
      });
      if (res.responseCode !== '200') {
        message.error(res.message);
      } else {
        setVisible(false);
        // 接口返回导入数据不合法
        if (res.data && res.data.length > 0) {
          errorRef.current?.open(res.data.join('\n'));
        } else {
          // 接口访问错误
          if (res.data?.status && res.data?.status >= 400) {
            message.error(
              intl.formatMessage({ id: 'webCommon_page_importFailed_toast_text' }) +
                '：' +
                intl.formatMessage({ id: 'webCommon_page_apiError_toast_text' }),
            );
            return;
          }
          // 导入成功
          message.success(
            `${values[urlParam]['name']} ` +
              intl.formatMessage({ id: 'webCommon_page_importSuccessed_toast_text' }),
          );
          refresh?.();
        }
      }
    },
    { manual: true },
  );

  const { run: downloadTemplate, loading } = useRequest(
    async () => {
      const res = await getTemplate();
      const disposition = res.response?.headers.get('content-disposition');
      const filename = disposition ? disposition.split(';')[1].split("filename*=utf-8''")[1] : null;
      if (filename) {
        downloadByData({
          data: res.data,
          filename: decodeURI(filename),
        });
      } else {
        downloadByData({
          data: res.data,
          filename:
            intl.formatMessage({ id: `webOperation_${pageName}_importTemplate_fileName_text` }) +
            '.xlsx',
        });
      }
    },
    { manual: true },
  );

  return (
    <>
      <Modal
        open={visible}
        onCancel={() => setVisible(false)}
        onOk={() => formRef.current?.submit()}
        destroyOnClose={true}
        confirmLoading={submitting}
        title={intl.formatMessage({
          id: buttonName ?? `webOperation_${pageName}_import_common_text`,
        })}
      >
        <ProForm
          formRef={formRef}
          onFinish={handleSubmit}
          labelCol={{ span: 5 }}
          layout="horizontal"
          submitter={false}
        >
          {formItems}
          <ProForm.Item
            name={urlParam}
            label={intl.formatMessage({ id: 'webCommon_page_file_select_placeholder' })}
            rules={[
              {
                required: true,
                message: intl.formatMessage({ id: 'webCommon_page_file_select_placeholder' }),
              },
            ]}
          >
            <FileUpload manual={true} multiple accept=".xls,.xlsx" />
          </ProForm.Item>
        </ProForm>
        <Access accessible={access[pageName + 'ImportTemplate']?.()}>
          <Button
            style={{ marginLeft: 15 }}
            loading={loading}
            type="primary"
            ghost
            onClick={downloadTemplate}
          >
            <FormattedMessage id="webCommon_page_downloadTemplate_button_text" />
          </Button>
        </Access>
      </Modal>

      {hideIcon ? (
        <Button
          type="primary"
          onClick={() => {
            hideConfirmModal?.();
            setVisible(true);
          }}
        >
          <FormattedMessage id={buttonName ?? `webOperation_${pageName}_import_common_text`} />
        </Button>
      ) : (
        <Button type="primary" ghost onClick={() => setVisible(true)}>
          <ImportOutlined />
          <FormattedMessage id={buttonName ?? `webOperation_${pageName}_import_common_text`} />
        </Button>
      )}

      <ImportError ref={errorRef} key="error" pageName={pageName} />
    </>
  );
});

export default Index;
