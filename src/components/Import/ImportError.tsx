import { Button, Modal, message } from 'antd';
import React, { useImperativeHandle, useState } from 'react';
import { FormattedMessage, useIntl } from 'umi';
import CodeMirror from '@uiw/react-codemirror';
import copy from 'copy-to-clipboard';
import styles from './styles.less';

export interface RefType {
  open: (content: string) => void;
}

export interface ImportErrorProps {
  pageName: string;
  title?: string;
  okText?: string;
}

const ImportError = React.forwardRef<RefType, ImportErrorProps>((props, ref) => {
  const { pageName, title, okText } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const [errorContent, setErrorContent] = useState<string>('');
  const intl = useIntl();

  useImperativeHandle(ref, () => ({
    open: (content: string) => {
      setErrorContent(content);
      setVisible(true);
    },
  }));

  const copyCot = () => {
    copy(errorContent);
    message.success(intl.formatMessage({ id: 'webCommon_page_copySuccessed_toast_text' }));
  };

  return (
    <>
      <Modal
        open={visible}
        onCancel={() => setVisible(false)}
        destroyOnClose={true}
        title={intl.formatMessage({
          id: title ? title : `webOperation_${pageName}_import_common_text`,
        })}
        footer={[
          <Button key="copy" type="primary" ghost style={{ width: '88px' }} onClick={copyCot}>
            <FormattedMessage id="webCommon_page_copyResult_button_text" />
          </Button>,
          <Button
            key="close"
            type="primary"
            style={{ width: '88px' }}
            onClick={() => setVisible(false)}
          >
            <FormattedMessage id={okText ?? 'webCommon_page_close_button_text'} />
          </Button>,
        ]}
      >
        <FormattedMessage id={`webOperation_${pageName}_importError_modal_message`} />
        <div className={styles.iot_ant_fail_log}>
          <CodeMirror value={errorContent} readOnly={true} theme="dark" />
        </div>
      </Modal>
    </>
  );
});

export default ImportError;
