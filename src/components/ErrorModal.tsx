import { Button, Modal } from 'antd';
import React, { useImperativeHandle, useState } from 'react';
import { useIntl } from 'umi';
import { FormattedMessage } from '@@/plugin-locale/localeExports';

const ErrorModal = React.forwardRef<APP.ViewRefType, { title: string }>(({ title }, ref) => {
  const intl = useIntl();
  const [visible, setVisible] = useState(false);
  const [msg, setMsg] = useState('');

  useImperativeHandle(
    ref,
    () => ({
      open: (m) => {
        setMsg(m);
        setVisible(true);
      },
    }),
    [setVisible],
  );

  return (
    <Modal
      title={intl.formatMessage({ id: title })}
      open={visible}
      destroyOnClose
      onCancel={() => setVisible(false)}
      footer={
        <Button onClick={() => setVisible(false)}>
          <FormattedMessage id="webCommon_page_close_button_text" />
        </Button>
      }
    >
      <p>{msg}</p>
    </Modal>
  );
});

export default ErrorModal;
