import React, {
  ReactNode,
  useContext,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { Access } from '@@/plugin-access/access';
import { FormattedMessage, useIntl } from '@@/plugin-locale/localeExports';
import { Button, Card, message, Modal, Space } from 'antd';
import { useAccess } from 'umi';
import { AccessLink } from '@/components/Button/AccessButton';
import { ProFormInstance, ProFormTextArea } from '@ant-design/pro-components';
import { ProForm, ProFormText } from '@ant-design/pro-form';
import { LocaleID } from '@/components/Locale';
import copy from 'copy-to-clipboard';
import { len500, len5000 } from '@/utils/validate';
import { DefinitionContext } from '@/utils/context';
import { includes } from 'lodash-es';
import { rbac } from '@/access';
interface DescriptionProps {
  label: string;
  value: API.MultiLanguage;
  onSubmit: (values: API.MultiLanguage) => Promise<void>;
  code: string;
  copyCode: string;
  setLoading?: (loading: boolean) => void;
  extra?: ReactNode;
  disabled?: boolean;
}

const DescriptionForm = React.forwardRef<{ submit: () => void }, DescriptionProps>((props, ref) => {
  const { label, value, onSubmit, extra, setLoading, disabled, copyCode, code } = props;
  const formRef = useRef<ProFormInstance<API.MultiLanguage>>(null);
  const intl = useIntl();
  const { readonly } = useContext(DefinitionContext);
  const access = useAccess();
  useImperativeHandle(ref, () => ({
    submit: () => {
      formRef.current?.submit();
    },
  }));

  useEffect(() => {
    formRef.current?.setFieldsValue(value);
  }, [value]);

  return (
    <ProForm
      onFinish={async (values) => {
        setLoading?.(true);
        try {
          await onSubmit(values);
        } finally {
          setLoading?.(false);
        }
      }}
      formRef={formRef}
      layout="vertical"
      submitter={false}
    >
      <ProFormText name="langId" hidden />
      <div style={{ position: 'relative' }}>
        <ProFormTextArea
          name="message"
          label={<FormattedMessage id={label} />}
          rules={[
            {
              required: true,
              message:
                intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                intl.formatMessage({ id: label }),
            },
            includes([rbac.PART.EDITSHORT, rbac.PRODUCT.EDITSHORT], code) ? len500 : len5000,
          ]}
          extra={<LocaleID id={value?.langId} />}
          fieldProps={{
            readOnly: disabled,
            style: disabled ? { background: '#f5f5f5' } : undefined,
          }}
        />
        {extra ? (
          <Space style={{ position: 'absolute', top: 0, right: 0 }}>
            {readonly ? null : extra}
            <Access accessible={access.checkAuth(copyCode)}>
              <Button
                type="link"
                size="small"
                onClick={() => {
                  const text = formRef.current?.getFieldValue('message');
                  copy(text);
                  message.success(
                    intl.formatMessage({ id: 'webCommon_page_operateSuccessed_toast_text' }),
                  );
                }}
              >
                <FormattedMessage id="webCommon_page_copy_button_linkText" />
              </Button>
            </Access>
          </Space>
        ) : null}
      </div>
    </ProForm>
  );
});

const Description: React.FC<DescriptionProps> = (props) => {
  const { code } = props;
  const [visible, setVisible] = useState(false);
  const intl = useIntl();
  const editRef = useRef<{ submit: () => void }>(null);
  const [loading, setLoading] = useState(false);

  return (
    <Card style={{ margin: 20 }}>
      <DescriptionForm
        {...props}
        disabled
        extra={
          <AccessLink
            text="webCommon_page_edit_common_text"
            code={code}
            onClick={() => {
              setVisible(true);
            }}
          />
        }
      />

      <Modal
        title={intl.formatMessage({ id: 'webCommon_page_edit_common_text' })}
        open={visible}
        onOk={() => editRef.current?.submit()}
        onCancel={() => setVisible(false)}
        confirmLoading={loading}
        destroyOnClose
      >
        <DescriptionForm
          ref={editRef}
          setLoading={setLoading}
          {...props}
          onSubmit={async (v) => {
            await props.onSubmit(v);
            setVisible(false);
          }}
        />
      </Modal>
    </Card>
  );
};

export default Description;
