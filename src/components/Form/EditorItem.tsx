import '@wangeditor/editor/dist/css/style.css'; // 引入 css
import React, { useState, useEffect, useImperativeHandle, CSSProperties } from 'react';
import { Editor, Toolbar } from '@wangeditor/editor-for-react';
import { IDomEditor, IEditorConfig, IToolbarConfig, i18nChangeLanguage } from '@wangeditor/editor';
import { first, split } from 'lodash-es';
import { getLocale } from '@@/plugin-locale/localeExports';
import { useIntl } from 'umi';

interface EditorItemProps {
  readonly: boolean;
  value?: string;
  onChange?: (value: any) => void;
  style?: CSSProperties;
}

export type Validator = (rule: any, _: any, callback: (error?: string) => void) => void;

export const EditorItem = React.forwardRef<{ validate: Validator }, EditorItemProps>(
  (props, ref) => {
    const { onChange, value, readonly = false, style = {} } = props;
    const [editor, setEditor] = useState<IDomEditor | null>(null);
    const intl = useIntl();

    useImperativeHandle(
      ref,
      () => ({
        validate: (rule, _, callback) => {
          const text = editor?.getText() || '';
          if (text.length > 0) {
            callback();
          } else {
            callback(rule.message);
          }
        },
      }),
      [editor],
    );
    // 编辑器配置
    const editorConfig: Partial<IEditorConfig> = {
      placeholder: intl.formatMessage({ id: 'webCommon_page_richtext_input_placeholder' }),
    };

    // 工具栏配置
    const toolbarConfig: Partial<IToolbarConfig> = {
      excludeKeys: ['group-image', 'group-video'],
    };

    useEffect(() => {
      if (editor) {
        // 切换语言 - 'en' 或者 'zh-CN'
        const lang = first(split(getLocale(), '-'));
        i18nChangeLanguage(lang === 'en' ? lang : 'zh-CN');
      }

      return () => {
        if (editor === null) return;
        editor.destroy();
        setEditor(null);
      };
    }, [editor]);

    return (
      <div id="adada" style={{ border: '1px solid #ccc', zIndex: 100, wordBreak: 'break-word' }}>
        <Toolbar
          editor={editor}
          defaultConfig={toolbarConfig}
          mode="default"
          style={{ borderBottom: '1px solid #ccc' }}
        />
        <Editor
          defaultConfig={editorConfig}
          value={value || ''}
          onCreated={(instance) => {
            setEditor(instance);
            if (readonly) instance.disable();
          }}
          onChange={(e) => onChange?.(e.getText().replace(/[\r\n]/g, '') === '' ? '' : e.getHtml())}
          mode="default"
          style={{ height: '500px', overflowY: 'hidden', ...style }}
        />
      </div>
    );
  },
);
