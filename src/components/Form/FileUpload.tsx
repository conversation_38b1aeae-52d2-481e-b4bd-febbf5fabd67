import { FormattedMessage } from '@@/plugin-locale/localeExports';
import { UploadOutlined } from '@ant-design/icons';
import { Button, message, Upload, UploadProps } from 'antd';
import { UploadFile } from 'antd/es/upload/interface';
import { RcFile } from 'antd/lib/upload/interface';
import React, { useState } from 'react';
import { replace, some, split, last } from 'lodash-es';
import { useIntl } from 'umi';

/**
 * manual 手动上传
 */
interface FileUploadProps extends UploadProps {
  value?: any;
  onChange?: (value: any) => void;
  manual?: boolean;
  presigned?: boolean;
  fileName?: string;
  getSignedUrl?: (data: {
    name?: string;
    size?: number;
    type?: string;
  }) => Promise<{ preSignedUrl: string; key: string }>;
  shouldOpenFileDialog?: () => boolean;
}

export const FileUpload: React.FC<FileUploadProps> = (props) => {
  const {
    value,
    onChange,
    manual,
    presigned,
    getSignedUrl,
    shouldOpenFileDialog,
    fileName = '',
    accept,
    ...rest
  } = props;
  const [fileList, setFileList] = useState<UploadFile[]>(
    value
      ? [
          {
            uid: '-1',
            name: fileName,
            status: 'done',
            url: value,
          },
        ]
      : [],
  );
  const [uploading, setUploading] = useState<boolean>(false);
  const intl = useIntl();
  // console.warn('默认值待处理，值为: ', value);

  const handleUpload = async (file: RcFile) => {
    setUploading(true);
    try {
      let url;
      if (presigned) {
        const { preSignedUrl } = await getSignedUrl!({
          name: file.name,
          size: file.size,
          type: file.type,
        });

        await fetch(preSignedUrl, {
          body: file,
          method: 'PUT',
          headers: {
            'Access-Control-Allow-Origin': '*',
            // 'Content-Type': 'text/rtf',
          },
        });
        url = preSignedUrl;
      }
      setFileList([
        {
          uid: '-1',
          name: file.name,
          status: 'done',
          url,
        },
      ]);
      onChange?.(url);
    } catch (e) {
      console.error('upload error: ', e);
    } finally {
      setUploading(false);
    }
  };

  const beforeUpload = async (file: RcFile) => {
    if (accept) {
      const exts = split(accept, ',').map((item) => replace(item, '.', ''));
      const fileExt = last(split(file.name, '.'));
      if (
        !some(exts, (ext) => file.type.indexOf(ext) > -1) &&
        !some(exts, (ext) => fileExt!.indexOf(ext) > -1)
      ) {
        message.error(intl.formatMessage({ id: 'webCommon_page_uploadError_toast_text' }));
        return Upload.LIST_IGNORE;
      }
    }
    // 手动上传
    if (manual) {
      onChange?.(file);
      setFileList([file]);
    } else {
      await handleUpload(file);
    }
    return false;
  };

  return (
    <Upload
      {...rest}
      accept={accept}
      fileList={fileList}
      beforeUpload={beforeUpload}
      onRemove={() => {
        onChange?.(undefined);
        setFileList([]);
      }}
    >
      <Button
        loading={uploading}
        icon={<UploadOutlined />}
        onClick={(e) => {
          if (shouldOpenFileDialog) {
            const valid = shouldOpenFileDialog();
            if (!valid) {
              e.stopPropagation();
            }
          }
        }}
      >
        <FormattedMessage id="webCommon_page_upload_button_text" />
      </Button>
    </Upload>
  );
};
