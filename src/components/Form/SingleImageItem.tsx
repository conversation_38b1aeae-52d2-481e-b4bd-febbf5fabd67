import { message, Modal, Upload, UploadProps } from 'antd';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { UploadFile } from 'antd/es/upload/interface';
import { RcFile } from 'antd/lib/upload/interface';
import React, { ReactNode, useEffect, useState } from 'react';
import { useIntl } from 'umi';
import { indexOf, last, replace, split, join } from 'lodash-es';
import { v4 as uuidv4 } from 'uuid';
export interface Ratio {
  width: number;
  height: number;
}

/**
 * manual 手动上传
 */
interface FileUploadProps extends UploadProps {
  value?: any;
  onChange?: (value: any) => void;
  getSignedUrl?: (name: string) => Promise<{ preSignedUrl: string; key: string }>;
  ratio?: Ratio;
  size?: number;
  accept?: string;
  extra?: ReactNode;
}

const MAX_SIZE: number = 800 * 1024;

export const SingleImageItem: React.FC<FileUploadProps> = (props) => {
  const {
    value,
    onChange,
    getSignedUrl,
    ratio,
    accept = '.png',
    size = MAX_SIZE,
    extra,
    ...rest
  } = props;
  const intl = useIntl();
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [signedUrl, setSignedUrl] = useState('');
  const [fileList, setFileList] = useState<UploadFile[]>(
    value
      ? [
          {
            uid: '-1',
            name: value,
            status: 'done',
            url: value,
          },
        ]
      : [],
  );
  const [uploading, setUploading] = useState<boolean>(false);

  useEffect(() => {
    if (!value) {
      setFileList([]);
      return;
    }
    if (uploading) return;

    if (/^http/i.test(value))
      setFileList([
        {
          uid: '-1',
          name: value,
          status: 'done',
          url: value,
        },
      ]);
  }, [value, uploading]);

  const handleUpload = async (file: RcFile) => {
    setUploading(true);
    const pattern = /[^a-zA-Z0-9]/;
    const ext = last(split(file.name, '.'));
    let filename = file.name;
    if (pattern.test(filename)) {
      filename = join([uuidv4(), ext], '.');
    }
    try {
      const { preSignedUrl, key } = await getSignedUrl!(filename);
      if (preSignedUrl) {
        await fetch(preSignedUrl, {
          body: file,
          method: 'PUT',
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Content-Type': file.type,
          },
        });
        setSignedUrl(preSignedUrl);
        onChange?.(key);
      } else {
        message.error(intl.formatMessage({ id: 'webCommon_page_uploadFailed_toast_message' }));
        return false;
      }
      return true;
    } catch (e) {
      message.error(intl.formatMessage({ id: 'webCommon_page_uploadFailed_toast_message' }));
      console.error('upload error: ', e);
      return false;
    } finally {
      setUploading(false);
    }
  };

  const beforeUpload = async (file: RcFile) => {
    if (accept) {
      const exts = split(accept, ',');
      if (indexOf(exts, '.' + last(split(file.type, '/'))) <= -1) {
        message.error(intl.formatMessage({ id: 'webCommon_page_uploadError_toast_message' }));
        return Upload.LIST_IGNORE;
      }
    }

    if (file.size > size) {
      message.error(intl.formatMessage({ id: 'webCommon_page_uploadImageSize_validator_message' }));
      return Upload.LIST_IGNORE;
    }

    if (ratio) {
      try {
        await new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.onload = function (ev) {
            const img = new Image();
            img.src = ev.target?.result as string;

            img.onload = function () {
              if (img.width !== ratio?.width || img.height !== ratio.height) {
                reject(new Error());
              } else {
                resolve(null);
              }
            };
          };
        });
      } catch (e) {
        message.error(intl.formatMessage({ id: 'webCommon_page_uploadSize_validator_message' }));
        return Upload.LIST_IGNORE;
      }
    }

    const res = await handleUpload(file);
    if (!res) {
      return Upload.LIST_IGNORE;
    }
    return false;
  };

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };

  const uploadButton = (
    <div>
      {uploading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  const handlePreview = async () => {
    setPreviewImage(replace(signedUrl, /\?.*/, '') || value);
    setPreviewOpen(true);
    setPreviewTitle(intl.formatMessage({ id: 'webCommon_page_view_common_text' }));
  };

  return (
    <>
      <Upload
        {...rest}
        fileList={fileList}
        beforeUpload={beforeUpload}
        onChange={handleChange}
        onRemove={() => {
          onChange?.(undefined);
          setFileList([]);
        }}
        className="avatar-uploader"
        listType="picture-card"
        onPreview={handlePreview}
        accept={accept}
      >
        {fileList.length >= 1 ? null : uploadButton}
      </Upload>
      {extra}
      <Modal
        open={previewOpen}
        title={previewTitle}
        footer={null}
        bodyStyle={{ minHeight: 380 }}
        onCancel={() => setPreviewOpen(false)}
      >
        <img alt="" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </>
  );
};
