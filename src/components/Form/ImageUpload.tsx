import { Upload, UploadProps, message, Modal } from 'antd';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { UploadFile } from 'antd/es/upload/interface';
import { RcFile } from 'antd/lib/upload/interface';
import React, { useEffect, useState } from 'react';
import { useIntl } from 'umi';
import { split, last, join } from 'lodash-es';
import { v4 as uuidv4 } from 'uuid';
/**
 * manual 手动上传
 */
interface FileUploadProps extends UploadProps {
  name: string;
  value?: any;
  setFieldValue?: (key: string, value: any) => void;
  getSignedUrl?: (name: string) => Promise<{ preSignedUrl: string; key: string }>;
  shouldOpenFileDialog?: () => boolean;
}

export const ImageUpload: React.FC<FileUploadProps> = (props) => {
  const maxSize: number = 800 * 1024;
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const { name, value, setFieldValue, getSignedUrl, shouldOpenFileDialog, ...rest } = props;
  const [uploading, setUploading] = useState<boolean>(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const intl = useIntl();
  useEffect(() => {
    if (!value) {
      setFileList([]);
      return;
    }
    if (uploading) return;
    setFileList([
      {
        uid: '-1',
        name: value,
        status: 'done',
        url: value,
      },
    ]);
  }, [value, uploading]);

  const handleUpload = async (file: RcFile) => {
    setUploading(true);
    const pattern = /[^a-zA-Z0-9]/;
    const ext = last(split(file.name, '.'));
    let filename = file.name;
    if (pattern.test(filename)) {
      filename = join([uuidv4(), ext], '.');
    }
    try {
      const { preSignedUrl } = await getSignedUrl!(filename);
      if (preSignedUrl) {
        await fetch(preSignedUrl, {
          body: file,
          method: 'PUT',
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Content-Type': file.type,
          },
        });
      } else {
        message.error(intl.formatMessage({ id: 'webCommon_page_uploadFailed_toast_message' }));
        return false;
      }
      return true;
    } catch (e) {
      message.error(intl.formatMessage({ id: 'webCommon_page_uploadFailed_toast_message' }));
      console.error('upload error: ', e);
      return false;
    } finally {
      setUploading(false);
    }
  };
  const handleCancel = () => setPreviewOpen(false);

  const getBase64 = (file: RcFile): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
  };
  const beforeUpload = async (file: RcFile) => {
    // 上传图片格式校验（只允许PNG格式)
    const isPNG = file.type === 'image/png';
    if (!isPNG) {
      message.error(intl.formatMessage({ id: 'webCommon_page_uploadImageType_validator_message' }));
      return Upload.LIST_IGNORE;
    }
    // 上传图片大小校验（大小不能超过800K)
    const isOver = file.size > maxSize;
    if (isOver) {
      message.error(intl.formatMessage({ id: 'webCommon_page_uploadImageSize_validator_message' }));
      return Upload.LIST_IGNORE;
    }
    const res = await handleUpload(file);
    if (!res) {
      return Upload.LIST_IGNORE;
    }
    return false;
  };

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };
  const uploadButton = (
    <div>
      {uploading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  return (
    <>
      <Upload
        {...rest}
        fileList={fileList}
        beforeUpload={beforeUpload}
        onChange={handleChange}
        onRemove={() => {
          setFieldValue?.(name, undefined);
          setFileList([]);
        }}
        onPreview={handlePreview}
        className="avatar-uploader"
        listType="picture-card"
      >
        {fileList.length >= 1 ? null : uploadButton}
      </Upload>
      <Modal open={previewOpen} footer={null} onCancel={handleCancel}>
        <img alt="preview" style={{ width: '100%', marginTop: '20px' }} src={previewImage} />
      </Modal>
    </>
  );
};
