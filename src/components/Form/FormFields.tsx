import {
  ProForm,
  ProFormText,
  ProFormTextArea,
  ProFormSelect,
  ProFormCheckbox,
  ProFormRadio,
  ProFormTimePicker,
  ProFormDatePicker,
  ProFormDigit,
} from '@ant-design/pro-components';
import { useIntl } from 'umi';
import React from 'react';

import type { typeColumns } from '@/hooks/column';
import { len128, len500 } from '@/utils/validate';
import { uploadImage } from '@/services/common';
import { ImageUpload } from '@/components/Form/ImageUpload';

interface listProps<T> {
  columns: typeColumns[];
  pageName: string;
  hideLabel?: boolean;
}

export function FormFields<T>({
  columns,
  pageName,
  hideLabel,
}: React.PropsWithChildren<listProps<T>>) {
  const intl = useIntl();
  const getMessage = (languageCode: string, type: string) => {
    return (
      intl.formatMessage({ id: `webCommon_page_${type}_common_placeholder` }) +
      intl.formatMessage({ id: languageCode })
    );
  };

  const setInputRequireRule = (languageCode: string, type: string, required?: boolean) => {
    if (required) {
      return [
        {
          required: true,
          whitespace: true,
          message: getMessage(languageCode, type),
        },
      ];
    }
    return [];
  };

  const setRequireRule = (languageCode: string, type: string, required?: boolean) => {
    if (required) {
      return [
        {
          required: true,
          message: getMessage(languageCode, type),
        },
      ];
    }
    return [];
  };

  return (
    <>
      {columns
        .filter((item) => item.showInForm)
        .map((item) => {
          let rules = [];
          const fieldName = item.dataIndex as string;
          const languageCode =
            item.langCode ||
            'webOperation_' + pageName + '_' + item.dataIndex + '_tableColumn_text';
          const readonly = !!item.readonly;
          const label = hideLabel
            ? false
            : intl.formatMessage({
                id: languageCode,
              });
          // 自定义表单项(比如表单项由多个输入框组合)
          if (item.renderInEditForm) {
            return item.renderInEditForm?.(intl);
          }
          // ProFormFields标准表单项
          switch (item.valueType) {
            // 文本域
            case 'textarea':
              return (
                <ProFormTextArea
                  name={fieldName}
                  key={fieldName}
                  label={label}
                  placeholder={getMessage(languageCode, 'input')}
                  rules={[
                    item.rule ? item.rule : len500,
                    ...setInputRequireRule(languageCode, 'input', item.required),
                  ]}
                  readonly={readonly}
                  extra={item.extraRender || null}
                />
              );
            // 下拉选择框
            case 'select':
              return (
                <ProFormSelect
                  name={fieldName}
                  key={fieldName}
                  label={label}
                  placeholder={getMessage(languageCode, 'select')}
                  rules={setRequireRule(languageCode, 'select', item.required)}
                  valueEnum={item.valueEnum}
                  readonly={readonly}
                />
              );
            // 多选框
            case 'checkbox':
              return (
                <ProFormCheckbox.Group
                  name={fieldName}
                  key={fieldName}
                  label={label}
                  placeholder={getMessage(languageCode, 'select')}
                  rules={setRequireRule(languageCode, 'select', item.required)}
                  valueEnum={item.valueEnum}
                />
              );
            // 单选框
            case 'radio':
              return (
                <ProFormRadio.Group
                  name={fieldName}
                  key={fieldName}
                  label={label}
                  placeholder={getMessage(languageCode, 'select')}
                  rules={setRequireRule(languageCode, 'select', item.required)}
                  valueEnum={item.valueEnum}
                  readonly={readonly}
                />
              );
            // 图片
            case 'image':
              return (
                <ProForm.Item
                  name={fieldName}
                  key={fieldName}
                  label={label}
                  rules={setRequireRule(languageCode, 'select', item.required)}
                >
                  {item.extraRender ? (
                    item.extraRender
                  ) : (
                    <ImageUpload
                      name={fieldName}
                      maxCount={1}
                      getSignedUrl={async (fileName) => {
                        return await uploadImage({
                          fileName,
                          fileType: 'picture',
                        });
                      }}
                    />
                  )}
                </ProForm.Item>
              );
            // 日期选择
            case 'date':
              return (
                <ProFormDatePicker
                  name={fieldName}
                  key={fieldName}
                  label={label}
                  placeholder={getMessage(languageCode, 'select')}
                  rules={setRequireRule(languageCode, 'select', item.required)}
                />
              );
            // 时间选择
            case 'time':
              return (
                <ProFormTimePicker
                  name={fieldName}
                  key={fieldName}
                  label={label}
                  placeholder={getMessage(languageCode, 'select')}
                  rules={setRequireRule(languageCode, 'select', item.required)}
                />
              );
            // 纯数字输入框
            case 'digit':
              return (
                <ProFormDigit
                  name={fieldName}
                  key={fieldName}
                  label={label}
                  placeholder={getMessage(languageCode, 'input')}
                  rules={[
                    {
                      required: item.required,
                      message: getMessage(languageCode, 'input'),
                    },
                  ]}
                  fieldProps={item.fieldProps ?? undefined}
                />
              );
            // 默认为普通文本框
            default:
              if (item.rule) {
                if (item.rule.max) {
                  rules = [item.rule, ...setInputRequireRule(languageCode, 'input', item.required)];
                } else {
                  rules = [
                    item.rule,
                    len128,
                    ...setInputRequireRule(languageCode, 'input', item.required),
                  ];
                }
              } else {
                rules = [len128, ...setInputRequireRule(languageCode, 'input', item.required)];
              }
              return (
                <ProFormText
                  name={fieldName}
                  key={fieldName}
                  label={label}
                  rules={rules}
                  placeholder={getMessage(languageCode, 'input')}
                  readonly={readonly}
                  extra={item.extraRender || null}
                  fieldProps={item.addonRender ? { addonAfter: item.addonRender } : {}}
                  hidden={!!item.hiddenInForm}
                />
              );
          }
        })}
    </>
  );
}
