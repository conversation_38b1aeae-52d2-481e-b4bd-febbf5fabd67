import {
  ProForm,
  ProFormFieldSet,
  ProFormDependency,
  ProFormRadio,
  ProFormTextArea,
  ProFormInstance,
} from '@ant-design/pro-components';
import { len500, urlChecked } from '@/utils/validate';
import { uploadImage } from '@/services/common';
import { useIntl } from 'umi';
import React from 'react';

import { Space } from 'antd';
import { ImageUpload } from '@/components/Form/ImageUpload';

export interface ImageFieldProps {
  formRef: React.MutableRefObject<ProFormInstance | undefined>;
  label: string;
  fieldUploadName: string;
  fieldInputName: string;
}

const ImageField: React.FC<ImageFieldProps> = ({
  formRef,
  label,
  fieldUploadName,
  fieldInputName,
}) => {
  const intl = useIntl();
  const setFieldValue = (key: string, value: any) => {
    const data = {};
    data[key] = value;
    formRef.current?.setFieldsValue(data);
  };
  return (
    <ProFormFieldSet
      name="imageValue"
      label={label}
      type="group"
      initialValue=" "
      rules={[
        {
          required: true,
          message: intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) + label,
        },
      ]}
      transform={(value: any) => ({})}
    >
      <Space>
        <ProFormRadio.Group
          name="iconType"
          label={false}
          valueEnum={{
            0: intl.formatMessage({ id: 'webOperation_dictionary_imageUpload_select_text' }),
            1: intl.formatMessage({ id: 'webOperation_dictionary_imageLink_select_text' }),
          }}
        />
        <ProFormDependency name={['iconType']}>
          {({ iconType }) => {
            if (iconType === '1') {
              return (
                <ProFormTextArea
                  rules={[
                    {
                      required: true,
                      message:
                        intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                        intl.formatMessage({ id: 'webCommon_page_imageUrl_input_text' }),
                    },
                    urlChecked,
                    len500,
                  ]}
                  width="lg"
                  name={fieldInputName}
                  label={intl.formatMessage({ id: 'webCommon_page_imageUrl_input_text' })}
                  placeholder={
                    intl.formatMessage({ id: 'webCommon_page_input_common_placeholder' }) +
                    intl.formatMessage({ id: 'webCommon_page_imageUrl_input_text' })
                  }
                  transform={(value: any) => ({ [fieldInputName]: value })}
                />
              );
            }
            return (
              <ProForm.Item
                name={fieldUploadName}
                label={false}
                rules={[
                  {
                    required: true,
                    message:
                      intl.formatMessage({ id: 'webCommon_page_select_common_placeholder' }) +
                      label,
                  },
                ]}
                addonAfter={intl.formatMessage({ id: 'webCommon_page_imageSize_tip_message' })}
              >
                <ImageUpload
                  name={fieldUploadName}
                  maxCount={1}
                  getSignedUrl={async (fileName) => {
                    const data = await uploadImage({
                      fileName,
                      fileType: 'picture',
                    });
                    const preSignedUrl = new URL(data.preSignedUrl);
                    const domain = preSignedUrl.protocol + '//' + preSignedUrl.host + '/';
                    setFieldValue?.(fieldUploadName, domain + data.key);
                    return data;
                  }}
                  setFieldValue={setFieldValue}
                />
              </ProForm.Item>
            );
          }}
        </ProFormDependency>
      </Space>
    </ProFormFieldSet>
  );
};

export default ImageField;
