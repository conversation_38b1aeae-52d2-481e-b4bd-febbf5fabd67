import {
  ProForm,
  ProFormDependency,
  ProFormRadio,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { len500 } from '@/utils/validate';
import { uploadImage } from '@/services/common';
import { useIntl } from 'umi';
import { FormattedMessage } from '@@/plugin-locale/localeExports';
import React from 'react';
import { get, omit } from 'lodash-es';
import { Ratio, SingleImageItem } from '@/components/Form/SingleImageItem';

interface OptionalImageItemProps {
  label: string;
  tip?: string;
  ratio?: Ratio;
}

/**
 * @param values form 数据
 * @param type 图片类型
 * @param icon 图片 key/url
 */
export function transformForAPI<T extends {}>(
  values: T,
  type: string,
  icon: string,
  s3key?: string,
  defaultS3Key?: string,
) {
  const optionalType = get(values, ['optional', 'type']);
  const url = get(values, ['optional', optionalType === '1' ? 'url' : 'img']);

  const final = optionalType === '0' ? s3key || icon : icon;
  return {
    ...omit(values, ['optional']),
    [type]: optionalType,
    [final]: optionalType === '0' ? (/^http/.test(url) ? defaultS3Key : url) : url,
  };
}

export function transformForForm<T extends {}>(values: T, type: string, icon: string) {
  const optionalType = get(values, [type]);
  return {
    ...omit(values, ['optional']),
    optional: {
      type: String(optionalType),
      url: get(values, [icon]),
      img: get(values, [icon]),
    },
  };
}

const OptionalItems: React.FC<OptionalImageItemProps> = ({
  label,
  ratio,
  tip = 'webCommon_page_imageSize_tip_message',
}) => {
  const intl = useIntl();
  return (
    <ProForm.Item label={label} required style={{ marginBottom: 0 }}>
      <ProFormRadio.Group
        name={['optional', 'type']}
        label={false}
        initialValue="0"
        valueEnum={{
          0: intl.formatMessage({ id: 'webOperation_dictionary_imageUpload_select_text' }),
          1: intl.formatMessage({ id: 'webOperation_dictionary_imageLink_select_text' }),
        }}
      />
      <ProFormDependency name={['optional', 'type']}>
        {({ optional = {} }) => {
          const { type } = optional;
          if (String(type) === '1') {
            return (
              <>
                <ProFormTextArea
                  name={['optional', 'url']}
                  labelCol={{ flex: '80px' }}
                  label={intl.formatMessage({ id: 'webCommon_page_imageUrl_input_text' })}
                  rules={[
                    {
                      required: true,
                      message: intl.formatMessage({
                        id: 'webCommon_page_imageUrl_input_placeholder',
                      }),
                    },
                    {
                      pattern: /^http:\/\/|^https:\/\//,
                      message: intl.formatMessage({ id: 'webCommon_page_url_validator_message' }),
                    },
                    len500,
                  ]}
                  placeholder={intl.formatMessage({
                    id: 'webCommon_page_imageUrl_input_placeholder',
                  })}
                />

                <ProFormDependency name={[['optional', 'url']]}>
                  {({ optional: optional1 = {} }) => {
                    const { url } = optional1;
                    return url ? (
                      <ProForm.Item>
                        <img
                          src={url}
                          alt=""
                          style={{
                            width: 100,
                            height: 100,
                            objectFit: 'contain',
                            border: '1px solid #f2f2f2',
                            borderRadius: 5,
                            marginLeft: 80,
                          }}
                        />
                      </ProForm.Item>
                    ) : null;
                  }}
                </ProFormDependency>
              </>
            );
          }
          return (
            <ProForm.Item
              name={['optional', 'img']}
              label={false}
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({ id: 'webCommon_page_upload_select_placeholder' }),
                },
              ]}
            >
              <SingleImageItem
                maxCount={1}
                ratio={ratio}
                getSignedUrl={async (fileName) => {
                  return await uploadImage({
                    fileName,
                    fileType: 'picture',
                  });
                }}
                extra={
                  <div style={{ fontSize: 8 }}>
                    <FormattedMessage id={tip} />
                  </div>
                }
              />
            </ProForm.Item>
          );
        }}
      </ProFormDependency>
    </ProForm.Item>
  );
};

export default OptionalItems;
