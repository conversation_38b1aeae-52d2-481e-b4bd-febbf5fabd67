import { FormattedMessage } from '@@/plugin-locale/localeExports';
import { <PERSON>ton, Drawer, DrawerProps, Modal, ModalProps, Space } from 'antd';
import React from 'react';

type ContainerProps = (ModalProps | DrawerProps) & {
  mode?: 'modal' | 'drawer';
  onCancel: () => void;
  onConfirm?: () => void;
  loading?: boolean;
  hiddenConfirm?: boolean;
};

const FormContainer: React.FC<ContainerProps> = ({
  mode,
  onCancel,
  onConfirm,
  loading,
  children,
  footer,
  hiddenConfirm,
  ...rest
}) => {
  if (mode === 'modal') {
    return (
      <Modal
        onCancel={onCancel}
        onOk={onConfirm}
        confirmLoading={loading}
        footer={footer}
        {...(rest as ModalProps)}
      >
        {children}
      </Modal>
    );
  } else {
    return (
      <Drawer
        {...rest}
        onClose={onCancel}
        footer={
          footer === null ? null : (
            <Space>
              <Button type="primary" ghost style={{ width: '88px' }} onClick={onCancel}>
                <FormattedMessage id="webCommon_page_cancel_button_text" />
              </Button>
              <Button
                type="primary"
                style={{ display: hiddenConfirm ? 'none' : 'block', width: '88px' }}
                loading={loading}
                onClick={onConfirm}
              >
                <FormattedMessage id="webCommon_page_confirm_button_text" />
              </Button>
            </Space>
          )
        }
        footerStyle={{ textAlign: 'right', border: 0, padding: '30px 20px' }}
      >
        {children}
      </Drawer>
    );
  }
};

export default FormContainer;
