import { FormattedMessage } from '@@/plugin-locale/localeExports';
import { DownOutlined } from '@ant-design/icons';
import { Divider, Dropdown, Menu, Space as AntSpace, SpaceProps } from 'antd';
import { slice } from 'lodash-es';
import React, { useMemo } from 'react';

type Props = SpaceProps & {
  /**
   * 显示按钮的数量 默认为2，即除了'更多'，显示两个
   * quantity为2时，如果children数量大于3，则显示两个按钮和一个更多按钮，否则显示children数量
   */
  quantity?: number;
};
export const Space = ({ children, quantity = 2, ...rest }: Props) => {
  const { fixed, others } = useMemo(() => {
    const list = React.Children.toArray(children);

    const flag = list.length > quantity + 1;
    return {
      fixed: slice(list, 0, flag ? quantity : quantity + 1),
      others: flag
        ? slice(list, quantity).map((item, index) => {
            return {
              key: index,
              label: item,
            };
          })
        : [],
    };
  }, [children, quantity]);

  return (
    <AntSpace key="option" split={<Divider type="vertical" />} {...rest}>
      {fixed}
      {others.length > 0 ? (
        <Dropdown overlay={<Menu items={others} />} placement="bottomRight">
          <a>
            <FormattedMessage id="webCommon_page_more_button_linkText" />
            <DownOutlined />
          </a>
        </Dropdown>
      ) : null}
    </AntSpace>
  );
};

export default Space;
