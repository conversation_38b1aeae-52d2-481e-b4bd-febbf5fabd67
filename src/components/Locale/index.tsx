import { FormattedMessage } from '@@/plugin-locale/localeExports';
import { isEmpty } from 'lodash-es';
import React, { CSSProperties } from 'react';

export const LocaleID: React.FC<{ id?: string; style?: CSSProperties }> = ({ id, style = {} }) => {
  return !isEmpty(id) ? (
    <span
      style={{
        whiteSpace: 'nowrap',
        position: 'absolute',
        bottom: '-32px',
        height: '32px',
        right: 0,
        ...style,
      }}
    >
      <FormattedMessage id="webCommon_page_langId_common_text" /> {id}
    </span>
  ) : null;
};
