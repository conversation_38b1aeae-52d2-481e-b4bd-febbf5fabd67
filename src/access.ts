/**
 * @see https://umijs.org/zh-CN/plugins/plugin-access
 * */
import { indexOf, some, isArray } from 'lodash-es';

export default function access(
  initialState: { currentUser?: API.CurrentUser; accessSet: Record<string, boolean> } | undefined,
) {
  // @ts-ignore
  const { currentUser, accessSet = {} } = initialState ?? {};

  const checkAuth = (code: string | string[]) => {
    if (code === '*' || indexOf(code, '*') > -1) return true;
    else {
      return some(isArray(code) ? code : [code], (item) => accessSet[item]);
    }
  };

  return {
    canAdmin: currentUser && currentUser.access === 'admin',

    // 分组按钮
    canViewGroup: () => checkAuth('group:view'),
    canEditGroup: () => checkAuth('group:edit'),
    canCreateGroup: () => checkAuth('group:create'),
    canDeleteGroup: () => checkAuth('group:delete'),
    canViewGroupResult: () => checkAuth('group:viewResult'),
    canCreateGroupCondition: () => checkAuth('group:createCondition'),
    canEditGroupCondition: () => checkAuth('group:editCondition'),
    canDeleteGroupCondition: () => checkAuth('group:deleteCondition'),

    // 多码按钮
    canEditCodeStatus: () => checkAuth('multicode:editStatus'),
    canCreateCode: () => checkAuth('multicode:create'),
    canExportCode: () => checkAuth('multicode:export'),
    canImportCode: () => checkAuth('multicode:import'),

    // 产品管理按钮
    canCreateProduct: () => checkAuth('product:create'),
    canExportProduct: () => checkAuth('product:export'),
    canImportProduct: () => checkAuth('product:import'),
    canViewProduct: () => checkAuth('product:canViewDetail'),
    canReleaseProduct: () => checkAuth('product:canRelease'),
    canWithdrawProduct: () => checkAuth('product:canWithdraw'),
    canDeleteProduct: () => checkAuth('product:canDelete'),
    canUpdateProduct: () => checkAuth('product:canUpdate'),

    // 产品管理配置
    canSettingProductPermission: () => checkAuth('operation.permission.product'),
    canAddPermissionProductApprove: () => checkAuth('operation.permission.approve.product.add'),
    canDeletePermissionProductApprove: () =>
      checkAuth('operation.permission.approve.product.delete'),
    canAddPermissionProductEdit: () => checkAuth('operation.permission.edit.product.add'),
    canDeletePermissionProductEdit: () => checkAuth('operation.permission.edit.product.delete'),
    canAddPermissionProductTest: () => checkAuth('operation.permission.test.product.add'),
    canDeletePermissionProductTest: () => checkAuth('operation.permission.test.product.delete'),
    canAddPermissionRnTest: () => checkAuth('operation.permission.test.rn.add'),
    canDeletePermissionRnTest: () => checkAuth('operation.permission.test.rn.delete'),
    canAddPermissionRnApprove: () => checkAuth('operation.permission.approve.rn.add'),
    canDeletePermissionRnApprove: () => checkAuth('operation.permission.approve.rn.delete'),
    canAddPermissionOtaTest: () => checkAuth('operation.permission.test.ota.add'),
    canDeletePermissionOtaTest: () => checkAuth('operation.permission.test.ota.delete'),
    canAddPermissionOtaApprove: () => checkAuth('operation.permission.approve.ota.add'),
    canDeletePermissionOtaApprove: () => checkAuth('operation.permission.approve.ota.delete'),

    // RN发布配置按钮
    canApplyReleaseRnconfig: () => checkAuth('rnConfig:canApplyRelease'),
    canCancelApplyReleaseRnconfig: () => checkAuth('rnConfig:canCancelApplyRelease'),
    canConfigRnconfig: () => checkAuth('rnConfig:canConfig'),
    canDownLoadRnconfig: () => checkAuth('rnConfig:canDownLoad'),
    canViewConfigRnconfig: () => checkAuth('rnConfig:canViewConfig'),
    canViewRefuseReleaseReasonRnconfig: () => checkAuth('rnConfig:canViewRefuseReleaseReason'),
    canViewRefuseTestReasonRnconfig: () => checkAuth('rnConfig:canViewRefuseTestReason'),
    canDeleteProGroupRnconfig: () => checkAuth('rnConfig:canDeleteProGroup'),
    canDeleteTestGroupRnconfig: () => checkAuth('rnConfig:canDeleteTestGroup'),
    canAddGroupProRnconfig: () => checkAuth('rnConfig:canAddGroupPro'),
    canAddGroupTestRnconfig: () => checkAuth('rnConfig:canAddGroupTest'),
    canAddGroupCommonConfig: () => checkAuth('rnConfig:canAddGroupCommon'),

    // 固件发布配置按钮
    canViewJobDetailOtaconfig: () => checkAuth('otaConfig:canViewJobDetail'),
    canConfigReleaseOtaconfig: () => checkAuth('otaConfig:canConfigRelease'),
    canViewReleaseDetailOtaconfig: () => checkAuth('otaConfig:canViewReleaseDetail'),
    canViewOtaResultOtaconfig: () => checkAuth('otaConfig:canViewOtaResult'),
    canApplyReleaseOtaconfig: () => checkAuth('otaConfig:canApplyRelease'),
    canCancelApplyReleaseOtaconfig: () => checkAuth('otaConfig:canCancelApplyRelease'),
    canApplyStopReleaseOtaconfig: () => checkAuth('otaConfig:canApplyStopRelease'),
    canCancelStopReleaseOtaconfig: () => checkAuth('otaConfig:canCancelStopRelease'),
    canViewApplyRefuseReasonOtaconfig: () => checkAuth('otaConfig:canViewApplyRefuseReason'),
    canViewTestRefuseReasonOtaconfig: () => checkAuth('otaConfig:canViewTestRefuseReason'),
    canViewStopRefuseReasonOtaconfig: () => checkAuth('otaConfig:canViewStopRefuseReason'),
    canDeleteProGroupOtaconfig: () => checkAuth('otaConfig:canDeleteProGroup'),
    canDeleteTestGroupOtaconfig: () => checkAuth('otaConfig:canDeleteTestGroup'),
    canAddGroupProOtaconfig: () => checkAuth('otaConfig:canAddGroupPro'),
    canAddGroupTestOtaconfig: () => checkAuth('otaConfig:canAddGroupTest'),
    canDownloadOtaconfig: () => checkAuth('otaConfig:canDownload'),
    canNullifyApplyOtaconfig: () => checkAuth('otaConfig:canNullifyApply'),
    canNullifyApplyCancelOtaconfig: () => checkAuth('otaConfig:canNullifyApplyCancel'),
    canViewNullifyRefusedReasonOtaconfig: () => checkAuth('otaConfig:canViewNullifyRefusedReason'),

    // APP协议管理按钮
    canCreateAppProtocol: () => checkAuth('appProtocol:create'),
    canViewAppProtocol: () => checkAuth('appProtocol:canView'),
    canApplyReleaseAppProtocol: () => checkAuth('appProtocol:canApplyRelease'),
    canCancelApplyReleaseAppProtocol: () => checkAuth('appProtocol:canCancelApplyRelease'),
    canAddNewVersionAppProtocol: () => checkAuth('appProtocol:canAddNewVersion'),
    canCopyAppProtocol: () => checkAuth('appProtocol:canCopy'),
    canUpdateAppProtocol: () => checkAuth('appProtocol:canUpdate'),
    canApplyStopReleaseAppProtocol: () => checkAuth('appProtocol:canApplyStopRelease'),
    canCancelApplyStopReleaseAppProtocol: () => checkAuth('appProtocol:canCancelApplyStopRelease'),
    canViewRefuseReleaseReasonAppProtocol: () =>
      checkAuth('appProtocol:canViewRefuseReleaseReason'),
    canViewRefuseTestReasonAppProtocol: () => checkAuth('appProtocol:canViewRefuseTestReason'),
    canViewRefuseStopReleaseReasonAppProtocol: () =>
      checkAuth('appProtocol:canViewRefuseStopReleaseReason'),
    canDeleteAppProtocol: () => checkAuth('appProtocol:canDelete'),
    canDeleteProGroupAppProtocol: () => checkAuth('appProtocol:canDeleteProGroup'),
    canDeleteTestGroupAppProtocol: () => checkAuth('appProtocol:canDeleteTestGroup'),
    canAddGroupProAppProtocol: () => checkAuth('appProtocol:canAddGroupPro'),
    canAddGroupTestAppProtocol: () => checkAuth('appProtocol:canAddGroupTest'),

    // 固件发布按钮
    canExportOtaRelease: () => checkAuth('otaRelease:export'),
    canViewReleaseDetailOtaRelease: () => checkAuth('otaRelease:canViewReleaseDetail'),
    canViewOtaResultOtaRelease: () => checkAuth('otaRelease:canViewOtaResult'),
    canEnsureReleaseOtaRelease: () => checkAuth('otaRelease:canEnsureRelease'),
    canTestEnsureReleaseOtaRelease: () => checkAuth('otaRelease:canTestEnsureRelease'),
    canEnsureStopReleaseOtaRelease: () => checkAuth('otaRelease:canEnsureStopRelease'),
    canRefuseReleaseOtaRelease: () => checkAuth('otaRelease:canRefuseRelease'),
    canTestRefuseReleaseOtaRelease: () => checkAuth('otaRelease:canTestRefuseRelease'),
    canRefuseStopReleaseOtaRelease: () => checkAuth('otaRelease:canRefuseStopRelease'),
    canViewApplyRefuseReasonOtaRelease: () => checkAuth('otaRelease:canViewApplyRefuseReason'),
    canViewTestRefuseReasonOtaRelease: () => checkAuth('otaRelease:canViewTestRefuseReason'),
    canViewStopRefuseReasonOtaRelease: () => checkAuth('otaRelease:canViewStopRefuseReason'),
    canNullifyPassedOtaRelease: () => checkAuth('otaRelease:canNullifyPassed'),
    canNullifyRefusedOtaRelease: () => checkAuth('otaRelease:canNullifyRefused'),
    canViewNullifyRefusedReasonOtaRelease: () =>
      checkAuth('otaRelease:canViewNullifyRefusedReasonOtaRelease'),

    // APP协议发布按钮
    canViewAppProtocolRelease: () => checkAuth('appProtocolRelease:canView'),
    canEnsureReleaseAppProtocolRelease: () => checkAuth('appProtocolRelease:canEnsureRelease'),
    canEnsureTestAppProtocolRelease: () => checkAuth('appProtocolRelease:canEnsureTest'),
    canEnsureStopReleaseAppProtocolRelease: () =>
      checkAuth('appProtocolRelease:canEnsureStopRelease'),
    canRefuseReleaseAppProtocolRelease: () => checkAuth('appProtocolRelease:canRefuseRelease'),
    canRefuseTestAppProtocolRelease: () => checkAuth('appProtocolRelease:canRefuseTest'),
    canRefuseStopReleaseAppProtocolRelease: () =>
      checkAuth('appProtocolRelease:canRefuseStopRelease'),
    canViewRefuseReleaseReasonAppProtocolRelease: () =>
      checkAuth('appProtocolRelease:canViewRefuseReleaseReason'),
    canViewRefuseTestReasonAppProtocolRelease: () =>
      checkAuth('appProtocolRelease:canViewRefuseTestReason'),
    canViewRefuseStopReleaseReasonAppProtocolRelease: () =>
      checkAuth('appProtocolRelease:canViewRefuseStopReleaseReason'),

    // 通用操作指导管理
    canCreateGuide: () => checkAuth('guide:create'),
    canExportGuide: () => checkAuth('guide:export'),
    canImportGuide: () => checkAuth('guide:import'),
    canAddGroupTestGuide: () => checkAuth('guide:canAddGroupTest'),
    canViewRelativeProductGuide: () => checkAuth('guide:canViewRelativeProduct'),
    canAddRelativeProductGuide: () => checkAuth('guide:canAddRelativeProduct'),
    canDeleteRelativeProductGuide: () => checkAuth('guide:canDeleteRelativeProduct'),
    canViewGuideApply: () => checkAuth('guideApply:canView'),
    canUpdateGuideApply: () => checkAuth('guideApply:canUpdate'),
    canDownloadGuideApply: () => checkAuth('guideApply:canDownload'),
    canDeleteGuideApply: () => checkAuth('guideApply:canDelete'),
    canViewGuide: () => checkAuth('guideRelease:canView'),
    canViewRelativeProductGuideRelease: () => checkAuth('guideRelease:canViewRelativeProduct'),

    // 通用FAQ管理
    canCreateFaq: () => checkAuth('faq:create'),
    canExportFaq: () => checkAuth('faq:export'),
    canImportFaq: () => checkAuth('faq:import'),
    canAddGroupTestFaq: () => checkAuth('faq:canAddGroupTest'),
    canViewRelativeProductFaq: () => checkAuth('faq:canViewRelativeProduct'),
    canAddRelativeProductFaq: () => checkAuth('faq:canAddRelativeProduct'),
    canDeleteRelativeProductFaq: () => checkAuth('faq:canDeleteRelativeProduct'),
    canViewFaqApply: () => checkAuth('faqApply:canView'),
    canUpdateFaqApply: () => checkAuth('faqApply:canUpdate'),
    canDeleteFaqApply: () => checkAuth('faqApply:canDelete'),
    canViewFaq: () => checkAuth('faqRelease:canView'),
    canViewRelativeProductFaqRelease: () => checkAuth('faqRelease:canViewRelativeProduct'),
    // APP系统消息
    canCreateSysMsgApply: () => checkAuth('sysMsgApply:canCreate'),
    canViewSysMsgApply: () => checkAuth('sysMsgApply:canView'),
    canViewPushResultSysMsgApply: () => checkAuth('sysMsgApply:canViewPushResult'),
    canCopySysMsgApply: () => checkAuth('sysMsgApply:canCopy'),
    canApplyReleaseSysMsgApply: () => checkAuth('sysMsgApply:canApplyRelease'),
    canUpdateSysMsgApply: () => checkAuth('sysMsgApply:canUpdate'),
    canCancelApplyReleaseSysMsgApply: () => checkAuth('sysMsgApply:canCancelApplyRelease'),
    canDeleteSysMsgApply: () => checkAuth('sysMsgApply:canDelete'),
    canApplyStopReleaseSysMsgApply: () => checkAuth('sysMsgApply:canApplyStopRelease'),
    canCancelApplyStopReleaseSysMsgApply: () => checkAuth('sysMsgApply:canCancelApplyStopRelease'),
    canViewRefuseReleaseReasonSysMsgApply: () =>
      checkAuth('sysMsgApply:canViewRefuseReleaseReason'),
    canViewRefuseTestReasonSysMsgApply: () => checkAuth('sysMsgApply:canViewRefuseTestReason'),
    canViewRefuseStopReleaseReasonSysMsgApply: () =>
      checkAuth('sysMsgApply:canViewRefuseStopReleaseReason'),
    canDeleteProGroupSysMsgApply: () => checkAuth('sysMsgApply:canDeleteProGroup'),
    canDeleteTestGroupSysMsgApply: () => checkAuth('sysMsgApply:canDeleteTestGroup'),
    canAddGroupProSysMsgApply: () => checkAuth('sysMsgApply:canAddGroupPro'),
    canAddGroupTestSysMsgApply: () => checkAuth('sysMsgApply:canAddGroupTest'),

    canViewSysMsg: () => checkAuth('sysMsgRelease:canView'),
    canEnsureTestSysMsg: () => checkAuth('sysMsgRelease:canEnsureTest'),
    canEnsureReleaseSysMsg: () => checkAuth('sysMsgRelease:canEnsureRelease'),
    canEnsureStopReleaseSysMsg: () => checkAuth('sysMsgRelease:canEnsureStopRelease'),
    canRefuseReleaseSysMsg: () => checkAuth('sysMsgRelease:canRefuseRelease'),
    canRefuseTestSysMsg: () => checkAuth('sysMsgRelease:canRefuseTest'),
    canRefuseStopReleaseSysMsg: () => checkAuth('sysMsgRelease:canRefuseStopRelease'),
    canViewRefuseReleaseReasonSysMsg: () => checkAuth('sysMsgRelease:canViewRefuseReleaseReason'),
    canViewRefuseTestReasonSysMsg: () => checkAuth('sysMsgRelease:canViewRefuseTestReason'),
    canViewRefuseStopReleaseReasonSysMsg: () =>
      checkAuth('sysMsgRelease:canViewRefuseStopReleaseReason'),

    // APP营销消息
    canCreateMarketingMsgApply: () => checkAuth('marketingMsgApply:canCreate'),
    canViewMarketingMsgApply: () => checkAuth('marketingMsgApply:canView'),
    canViewPushResultMarketingMsgApply: () => checkAuth('marketingMsgApply:canViewPushResult'),
    canCopyMarketingMsgApply: () => checkAuth('marketingMsgApply:canCopy'),
    canApplyReleaseMarketingMsgApply: () => checkAuth('marketingMsgApply:canApplyRelease'),
    canUpdateMarketingMsgApply: () => checkAuth('marketingMsgApply:canUpdate'),
    canCancelApplyReleaseMarketingMsgApply: () =>
      checkAuth('marketingMsgApply:canCancelApplyRelease'),
    canDeleteMarketingMsgApply: () => checkAuth('marketingMsgApply:canDelete'),
    canApplyStopReleaseMarketingMsgApply: () => checkAuth('marketingMsgApply:canApplyStopRelease'),
    canCancelApplyStopReleaseMarketingMsgApply: () =>
      checkAuth('marketingMsgApply:canCancelApplyStopRelease'),
    canViewRefuseReleaseReasonMarketingMsgApply: () =>
      checkAuth('marketingMsgApply:canViewRefuseReleaseReason'),
    canViewRefuseTestReasonMarketingMsgApply: () =>
      checkAuth('marketingMsgApply:canViewRefuseTestReason'),
    canViewRefuseStopReleaseReasonMarketingMsgApply: () =>
      checkAuth('marketingMsgApply:canViewRefuseStopReleaseReason'),
    canDeleteProGroupMarketingMsgApply: () => checkAuth('marketingMsgApply:canDeleteProGroup'),
    canDeleteTestGroupMarketingMsgApply: () => checkAuth('marketingMsgApply:canDeleteTestGroup'),
    canAddGroupProMarketingMsgApply: () => checkAuth('marketingMsgApply:canAddGroupPro'),
    canAddGroupTestMarketingMsgApply: () => checkAuth('marketingMsgApply:canAddGroupTest'),

    canViewMarketingMsg: () => checkAuth('marketingMsgRelease:canView'),
    canEnsureTestMarketingMsg: () => checkAuth('marketingMsgRelease:canEnsureTest'),
    canEnsureReleaseMarketingMsg: () => checkAuth('marketingMsgRelease:canEnsureRelease'),
    canEnsureStopReleaseMarketingMsg: () => checkAuth('marketingMsgRelease:canEnsureStopRelease'),
    canRefuseReleaseMarketingMsg: () => checkAuth('marketingMsgRelease:canRefuseRelease'),
    canRefuseTestMarketingMsg: () => checkAuth('marketingMsgRelease:canRefuseTest'),
    canRefuseStopReleaseMarketingMsg: () => checkAuth('marketingMsgRelease:canRefuseStopRelease'),
    canViewRefuseReleaseReasonMarketingMsg: () =>
      checkAuth('marketingMsgRelease:canViewRefuseReleaseReason'),
    canViewRefuseTestReasonMarketingMsg: () =>
      checkAuth('marketingMsgRelease:canViewRefuseTestReason'),
    canViewRefuseStopReleaseReasonMarketingMsg: () =>
      checkAuth('marketingMsgRelease:canViewRefuseStopReleaseReason'),

    // 消息模板
    canViewMessageTemplate: () => checkAuth('messageTemplate:view'),
    canCreateMessageTemplate: () => checkAuth('messageTemplate:create'),
    canEditMessageTemplate: () => checkAuth('messageTemplate:edit'),
    canDeleteMessageTemplate: () => checkAuth('messageTemplate:delete'),

    // part
    canViewPart: () => checkAuth(rbac.PART.VIEW),
    canCreatePart: () => checkAuth(rbac.PART.CREATE),
    canUpdatePart: () => checkAuth(rbac.PART.UPDATE),
    canDeletePart: () => checkAuth(rbac.PART.DELETE),

    // device protocol
    canCreateDeviceProtocol: () => checkAuth(rbac.PROTOCOL.CREATE),
    canRecreateDeviceProtocol: () => checkAuth('recreate:deviceProtocol'),
    canUpdateDeviceProtocol: () => checkAuth('update:deviceProtocol'),
    canDeleteDeviceProtocol: () => checkAuth('delete:deviceProtocol'),
    canViewDeviceProtocol: () => checkAuth('view:deviceProtocol'),
    canDeleteGroupDeviceProtocol: () => checkAuth('deviceProtocol:canDeleteGroup'),
    canAddGroupDeviceProtocol: () => checkAuth('deviceProtocol:canAddGroup'),

    canExportUserList: () => checkAuth('userList_export'),
    canExportUserDevice: () => checkAuth('userDevice_export'),

    // APP列表顺序
    canViewAppSort: () => checkAuth('appSort:canView'),
    canEditAppSort: () => checkAuth('appSort:canEdit'),
    canEditStatusAppSort: () => checkAuth('appSort:canEditStatus'),

    // 品类
    canViewCategory: () => checkAuth('category:view'),
    canCreateCategory: () => checkAuth('category:create'),
    canEditCategory: () => checkAuth('category:edit'),
    canDeleteCategory: () => checkAuth('category:delete'),

    // 品牌
    canViewBrand: () => checkAuth('brand:view'),
    canCreateBrand: () => checkAuth('brand:create'),
    canEditBrand: () => checkAuth('brand:edit'),
    canDeleteBrand: () => checkAuth('brand:delete'),

    // 经销商
    canViewDealer: () => checkAuth('dealer:view'),
    canCreateDealer: () => checkAuth('dealer:create'),
    canEditDealer: () => checkAuth('dealer:edit'),
    canDeleteDealer: () => checkAuth('dealer:delete'),
    canImportDealer: () => checkAuth('dealer:import'),

    canCreateProductPart: () => checkAuth(rbac.PRODUCT_PART.CREATE),
    canSortProductPart: () => checkAuth(rbac.PRODUCT_PART.SORT),

    // canSortSaleFaq: () => checkAuth(rbac.FAQ.SORT),
    // canUpdateSaleFaq: () => checkAuth(rbac.FAQ.UPDATE),

    // canSortFaqGuide: () => checkAuth(rbac.FAQ.GUIDE.SORT),

    canCreateAppQuestion: () => checkAuth(rbac.HELP.CREATE),
    canUpdateAppQuestion: () => checkAuth(rbac.HELP.UPDATE),

    canCreateAppVersion: () => checkAuth(rbac.APP_VERSION.CREATE),

    canCreateSuggestion: () => checkAuth(rbac.SUGGESTION.CREATE),
    canUpdateSuggestion: () => checkAuth(rbac.SUGGESTION.UPDATE),

    canExportMessageRecord: () => checkAuth(rbac.MESSAGE_RECORD.EXPORT),
    canExportMessagePushRecord: () => checkAuth(rbac.MESSAGE_RECORD.PUSH_EXPORT),

    // 导入模板下载按钮
    faqImportTemplate: () => checkAuth('faq:downloadTemplate'),
    guideImportTemplate: () => checkAuth('guide:downloadTemplate'),
    helpImportTemplate: () => checkAuth('help:downloadTemplate'),
    suggestionImportTemplate: () => checkAuth('suggestion:downloadTemplate'),
    codeImportTemplate: () => checkAuth('multicode:downloadTemplate'),
    productImportTemplate: () => checkAuth('product:downloadTemplate'),
    dealerImportTemplate: () => checkAuth('dealer:downloadTemplate'),
    partImportTemplate: () => checkAuth('part:downloadTemplate'),

    // 计费管理
    canViewSmsDetail: () => checkAuth('sms:detail'),
    canExportSms: () => checkAuth('sms_export'),
    canExportSmsDetails: () => checkAuth('sms_exportDetails'),
    canViewFlowDetail: () => checkAuth('flow:detail'),
    canExportFlow: () => checkAuth('flow:export'),
    canExportFlowDetails: () => checkAuth('flow:exportDetails'),

    // 设备信息
    canViewDeviceDetail: () => checkAuth('device:detail'),
    canViewOtaUpgrade: () => checkAuth('view:partUpgradeHistory'),
    canStopDevice: () => checkAuth('enable:device'),
    canExportDevice: () => checkAuth('export:devices'),

    // common
    checkAuth,
  };
}

/**
 * 按钮权限CODE
 */
export const rbac = {
  PART: {
    CREATE: 'create:part',
    IMPORT: 'import:part',
    EXPORT: 'export:part',
    VIEW: 'view:part',
    RELATIVE: 'viewRelativeProduct:part',
    // 配件详情页面涉及到的 编辑、删除、添加 统一由此权限控制
    UPDATE: 'update:part',
    DELETE: 'delete:part',
    EDITSHORT: 'editShort:part',
    COPYSHORT: 'copyShort:part',
    EDITLONG: 'editLong:part',
    COPYLONG: 'copyLong:part',
    EDITTECH: 'editTech:part',
    COPYTECH: 'copyTech:part',

    LINK: {
      CREATE: 'add:part.payLink',
      UPDATE: 'update:part.payLink',
      DELETE: 'delete:part.payLink',
    },
    GUIDE: {
      SORT: 'sort:part.guide',
      VIEW: 'view:part.guide',
      UPDATE: 'update:part.guide',
      DELETE: 'delete:part.guide',
      DOWNLOAD: 'download:part.guide',
      CREATE: 'add:part.guide',
    },

    MANUAL: {
      VIEW: 'view:part.manual',
      UPDATE: 'update:part.manual',
      DELETE: 'delete:part.manual',
      DOWNLOAD: 'download:part.manual',
      CREATE: 'add:part.manual',
    },
    FAQ: {
      VIEW: 'view:part.faq',
      UPDATE: 'update:part.faq',
      DELETE: 'delete:part.faq',
      SORT: 'sort:part.faq',
      CREATE: 'add:part.faq',
    },
  },
  INTRODUCTION: {
    CREATE0: 'create:introduction.qrcode',
    UPDATE0: 'update:introduction.qrcode',
    DELETE0: 'delete:introduction.qrcode',
    CREATE1: 'create:introduction.net',
    UPDATE1: 'update:introduction.net',
    DELETE1: 'delete:introduction.net',
    CREATE2: 'create:introduction.upgrade',
    UPDATE2: 'update:introduction.upgrade',
    DELETE2: 'delete:introduction.upgrade',
  },
  PROTOCOL: {
    VIEW: 'view:deviceProtocol',
    UPDATE: 'update:deviceProtocol',
    DELETE: 'delete:deviceProtocol',
    RECREATE: 'recreate:deviceProtocol',
    CREATE: 'create:deviceProtocol',
  },
  PRODUCT_PART: {
    VIEW: 'view:productPart',
    UPDATE: 'update:productPart',
    DELETE: 'delete:productPart',
    CREATE: 'create:productPart',
    SORT: 'sort:productPart',
  },
  PRODUCT: {
    EDITSHORT: 'update:productShort',
    COPYSHORT: 'copy:productShort',
    EDITLONG: 'update:productLong',
    COPYLONG: 'copy:productLong',
    EDITTECH: 'update:productTech',
    COPYTECH: 'copy:productTech',

    GUIDE: {
      SORT: 'sort:product.guide',
      VIEW: 'view:product.guide',
      UPDATE: 'update:product.guide',
      DELETE: 'delete:product.guide',
      DOWNLOAD: 'download:product.guide',
      CREATE: 'add:product.guide',
    },

    MANUAL: {
      CREATE: 'create:product.manual',
      VIEW: 'view:product.manual',
      UPDATE: 'update:product.manual',
      DELETE: 'delete:product.manual',
      DOWNLOAD: 'download:product.manual',
    },
    FAQ: {
      VIEW: 'view:product.faq',
      UPDATE: 'update:product.faq',
      DELETE: 'delete:product.faq',
      SORT: 'sort:product.faq',
      CREATE: 'add:product.faq',
    },
  },
  HELP: {
    LIST: 'list:help',
    VIEW: 'view:help',
    VISIBLE: 'view:visible',
    SYNC: 'view:sync',
    UPDATE: 'update:help',
    CREATE: 'create:help',
    DELETE: 'delete:help',
    IMPORT: 'import:help',
    EXPORT: 'export:help',
  },
  APP_VERSION: {
    LIST: 'list:app.version',
    CREATE: 'create:app.version',
    DELETE: 'delete:app.version',
  },
  SUGGESTION: {
    LIST: 'list:suggestion',
    VIEW: 'view:suggestion',
    UPDATE: 'update:suggestion',
    CREATE: 'create:suggestion',
    DELETE: 'delete:suggestion',
    IMPORT: 'import:suggestion',
    EXPORT: 'export:suggestion',
  },
  MESSAGE_RECORD: {
    LIST: 'list:message.record',
    VIEW: 'view:message.record',
    EXPORT: 'export:message.record',
    PUSH_EXPORT: 'export:message.pushRecord',
  },
  COMPANY: {
    ACCOUNT: {
      DETAIL: 'companyAccount:detail',
      STATUS: 'companyAccount:stopandenable',
      ADMINCHANGES: 'companyAccount:adminchanges',
      CANCEL: 'companyAccount:cancel',
      EXPORT: 'companyAccount:export',
    },
    DEVICE: {
      EXPORT: 'companyDEVICES:export',
    },
    EMPLOYEE: {
      DETAIL: 'companyEmployee:detail',
      STATUS: 'companyEmployee:stopandenable',
      EXPORT: 'companyEmployee:export',
    },
  },
  DEALER: {
    NA: {
      CREATE: 'companyNaDealer:create',
      VIEW: 'companyNaDealer:view',
      EDIT: 'companyNaDealer:edit',
      DELETE: 'companyNaDealer:delete',
      IMPORT: 'companyNaDealer:import',
    },
    EU: {
      CREATE: 'companyEuDealer:create',
      VIEW: 'companyEuDealer:view',
      EDIT: 'companyEuDealer:edit',
      DELETE: 'companyEuDealer:delete',
      BATCHDELETE: 'companyEuDealer:batchDelete',
      IMPORT: 'companyEuDealer:import',
    },
  },
};
