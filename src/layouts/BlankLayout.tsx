import React from 'react';
import { useModel, useAliveController } from 'umi';
import { useMount } from 'ahooks';
import { Inspector } from 'react-dev-inspector';
import { map } from 'lodash-es';

const InspectorWrapper = process.env.NODE_ENV === 'development' ? Inspector : React.Fragment;

const Layout: React.FC = ({ children }) => {
  const masterProps = useModel('@@qiankunStateFromMaster');
  const { dropScope } = useAliveController();
  useMount(() => {
    masterProps?.onGlobalStateChange((state: any) => {
      map(state.closePaths, (item) => {
        const appBase = item.match?.(/(\w+)/) ? item.match?.(/(\w+)/)?.[0] : null;
        if ('/' + appBase + '/' === ROOT_PATH) {
          dropScope(item);
        }
      });
    });
  });

  return <InspectorWrapper>{children}</InspectorWrapper>;
};

export default Layout;
