// https://umijs.org/config/
import { defineConfig } from 'umi';

import defaultSettings from './defaultSettings';
import proxy from './proxy';
import routes from './routes';
import AntdDayjsWebpackPlugin from 'antd-dayjs-webpack-plugin';
const { REACT_APP_ENV, NODE_ENV, PUBLIC_PATH } = process.env;

export default defineConfig({
  chainWebpack: (config: any) => {
    config.plugin('antd-dayjs-webpack-plugin').use(AntdDayjsWebpackPlugin, [
      {
        plugins: [
          'isSameOrBefore',
          'isSameOrAfter',
          'advancedFormat',
          'customParseFormat',
          'weekday',
          'weekYear',
          'weekOfYear',
          'isMoment',
          'localeData',
          'localizedFormat',
        ],
      },
    ]);
  },
  define: {
    ROOT_PATH: PUBLIC_PATH,
  },
  hash: true,
  publicPath: NODE_ENV == 'production' ? PUBLIC_PATH : '/',
  antd: {
    config: {
      prefixCls: 'operation-ant',
    },
  },
  base: '/operation',
  history: {
    type: 'browser',
  },
  dva: {
    hmr: true,
  },
  layout: {
    // https://umijs.org/zh-CN/plugins/plugin-layout
    locale: true,
    siderWidth: 208,
    ...defaultSettings,
  },
  // https://umijs.org/zh-CN/plugins/plugin-locale
  locale: {
    default: 'zh-CN',
    antd: true,
    baseNavigator: true,
  },
  dynamicImport: {
    loading: '@ant-design/pro-layout/es/PageLoading',
  },
  targets: {
    ie: 11,
  },
  // umi routes: https://umijs.org/docs/routing
  routes,
  access: {},
  // Theme for antd: https://ant.design/docs/react/customize-theme-cn
  theme: {
    // 如果不想要 configProvide 动态设置主题需要把这个设置为 default
    // 只有设置为 variable， 才能使用 configProvide 动态设置主色调
    // https://ant.design/docs/react/customize-theme-variable-cn
    'root-entry-name': 'variable',
    'primary-color': '#77BC1F',
  },
  // esbuild is father build tools
  // https://umijs.org/plugins/plugin-esbuild
  esbuild: {},
  title: false,
  ignoreMomentLocale: true,
  proxy: proxy[REACT_APP_ENV || 'dev'],
  // Fast Refresh 热更新
  fastRefresh: {},
  nodeModulesTransform: { type: 'none' },
  mfsu: {},
  webpack5: {},
  qiankun: {
    slave: {},
  },
  lessLoader: {
    modifyVars: {
      // 或者可以通过 less 文件覆盖（文件路径为绝对路径）
      'ant-prefix': 'operation-ant',
    },
  },
});
