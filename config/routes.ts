export default [
  {
    path: '/',
    component: '../layouts/BlankLayout',
    routes: [
      {
        path: '/message', // 消息中心
        icon: 'smile',
        hideInMenu: true,
        routes: [
          {
            path: '/message/template', // 消息模板
            component: './message/TemplateList',
          },
          {
            path: '/message/record', // 消息记录
            component: './message/RecordList',
          },
        ],
      },
      {
        path: '/brand', // 品牌管理
        icon: 'smile',
        hideInMenu: true,
        component: './brand/BrandList',
      },
      {
        path: '/category', // 品类管理
        icon: 'smile',
        hideInMenu: true,
        component: './category/CategoryList',
      },
      {
        path: '/dealer', // 经销商管理
        icon: 'smile',
        hideInMenu: true,
        component: './dealer/DealerList',
      },
      {
        path: '/device',
        hideInMenu: true,
        icon: 'smile',
        routes: [
          {
            path: '/device', // 设备管理
            component: './device/DeviceList',
          },
          {
            path: '/device/:id', // 设备详情
            component: './device/DeviceDetail',
          },
        ],
      },
      {
        path: '/group', // 分组管理
        icon: 'smile',
        hideInMenu: true,
        component: './group/GroupList',
      },
      {
        path: '/suggestion', // 处理建议
        icon: 'smile',
        hideInMenu: true,
        component: './suggestion/SuggestionList',
      },
      {
        path: '/part',
        hideInMenu: true,
        icon: 'smile',
        routes: [
          {
            path: '/part', // 配件管理
            component: './part/PartList',
          },
          {
            path: '/part/:id', // 配件详情
            component: './part/PartDetail',
          },
        ],
      },
      {
        path: '/product',
        icon: 'smile',
        hideInMenu: true,
        routes: [
          {
            path: '/product', // 产品管理
            component: './product/ProductList',
          },
          {
            path: '/product/permission', // 产品管理配置
            component: './product/permission/ProductList',
          },
        ],
      },
      {
        path: '/bussiness',
        icon: 'smile',
        hideInMenu: true,
        routes: [
          {
            path: '/bussiness/company/account', // Fleet租户管理
            component: './bussiness/company/AccountList',
          },
          {
            path: '/bussiness/company/devices', // Fleet租户&设备关系管理
            component: './bussiness/company/DeviceList',
          },
          {
            path: '/bussiness/company/employee', // Fleet租户员工管理
            component: './bussiness/company/EmployeeList',
          },
          {
            path: '/bussiness/dealer/na', // 北美经销商管理
            component: './bussiness/dealer/AmericanList',
          },
          {
            path: '/bussiness/dealer/eu', // 欧洲经销商管理
            component: './bussiness/dealer/EuropeList',
          },
        ],
      },
      {
        path: '/release', // 发布管理
        icon: 'smile',
        hideInMenu: true,
        routes: [
          {
            path: '/release/firmware', // 固件发布管理
            component: './release/FirmwareList',
          },
          {
            path: '/release/protocol', // APP协议发布管理
            component: './release/AppProtocol',
          },
          {
            path: '/release/sysmessage', // APP系统消息发布管理
            component: './release/AppMessageSys',
          },
          {
            path: '/release/marketingmessage', // APP营销消息发布管理
            component: './release/AppMessageMarketing',
          },
        ],
      },
      {
        path: '/app', // APP管理
        icon: 'smile',
        hideInMenu: true,
        routes: [
          {
            path: '/app/userlist', // APP用户账号管理
            component: './app/UserList',
          },
          {
            path: '/app/userdevice', // APP用户&设备关系管理
            component: './app/UserDevice',
          },
          {
            path: '/app/protocol', // APP协议管理
            component: './app/ProtocolList',
          },
          {
            path: '/app/sort', // APP列表顺序管理
            component: './app/AppSort',
          },
          {
            path: '/app/message/system', // APP系统消息管理
            component: './app/MessageSystem',
          },
          {
            path: '/app/message/marketing', // APP营销消息管理
            component: './app/MessageMarketing',
          },
          {
            path: '/app/question', // 帮助中心
            component: './app/HelpList',
          },
          {
            path: '/app/upgrade', // APP安卓版本升级管理
            component: './app/AppUpgradeList',
          },
        ],
      },
      {
        path: '/aftersales', // 通用售后内容管理
        icon: 'smile',
        hideInMenu: true,
        routes: [
          {
            path: '/aftersales/guide', // 通用操作指导管理
            component: './aftersales/GeneralGuides',
          },
          {
            path: '/aftersales/faq', // 通用FAQ管理
            component: './aftersales/GeneralFaqs',
          },
        ],
      },
      {
        path: '/fee', // 计费管理
        icon: 'smile',
        hideInMenu: true,
        routes: [
          {
            path: '/fee/sms', // 短信流量统计
            component: './fee/SmsList',
          },
          {
            path: '/fee/flow', // 流量费用统计
            component: './fee/FlowList',
          },
        ],
      },
      // {
      //   path: '/',
      //   redirect: '/product',
      // },
      {
        path: '/login', // 登录处理页
        icon: 'smile',
        hideInMenu: true,
        component: './login/Index',
      },
      {
        layout: false,
        component: './404',
      },
    ],
  },
];
